import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Phone,
  ArrowLeft,
  Shield,
  CheckCircle,
  Clock,
  LogIn,
  Smartphone,
} from "lucide-react";
import { NavLink } from "react-router";
import axiosPrivate from "@/config/api";
import { toast } from "sonner";
import { GoogleLogin } from "@react-oauth/google";

const Login = () => {
  const [step, setStep] = useState<"options" | "phone" | "otp">("options");
  const [phone, setPhone] = useState("");
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);
  const [timer, setTimer] = useState(0);
  const [canResend, setCanResend] = useState(false);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timer]);

  const startTimer = () => {
    setTimer(60); // 1 minute
    setCanResend(false);
  };

  const handleSendOtp = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    setLoading(true);

    try {
      // Ensure phone number has 998 prefix (without +)
      let phoneWithPrefix = phone.trim();
      if (phoneWithPrefix.startsWith("+998")) {
        // Remove + and keep 998 prefix
        phoneWithPrefix = phoneWithPrefix.substring(1);
      } else if (phoneWithPrefix.startsWith("998")) {
        // Already has 998 prefix
        phoneWithPrefix = phoneWithPrefix;
      } else {
        // No prefix at all
        phoneWithPrefix = `998${phoneWithPrefix}`;
      }

      await axiosPrivate.post("/api/otp/send", { phone: phoneWithPrefix });
      toast.success("OTP kodu gönderildi");
      setStep("otp");
      startTimer(); // Start the 1-minute timer
    } catch (error: any) {
      toast.error(error.response?.data?.message || "OTP gönderilemedi");
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Ensure phone number has 998 prefix (without +) and ensure code is string
      let phoneWithPrefix = phone.trim();
      if (phoneWithPrefix.startsWith("+998")) {
        // Remove + and keep 998 prefix
        phoneWithPrefix = phoneWithPrefix.substring(1);
      } else if (phoneWithPrefix.startsWith("998")) {
        // Already has 998 prefix
        phoneWithPrefix = phoneWithPrefix;
      } else {
        // No prefix at all
        phoneWithPrefix = `998${phoneWithPrefix}`;
      }

      const response = await axiosPrivate.post("/api/otp/verify", {
        phoneNumber: phoneWithPrefix,
        code: otp.toString(),
      });

      if (response.data.accessToken) {
        localStorage.setItem("accessToken", response.data.accessToken);
        toast.success("Giriş başarılı");
        window.location.href = "/";
      } else if (response.data.message || response.status === 200) {
        toast.success("Giriş başarılı");
        window.location.href = "/";
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "OTP doğrulanamadı");
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSuccess = async (credentialResponse: any) => {
    setLoading(true);
    try {
      const response = await axiosPrivate.post("/api/auth/google", {
        credential: credentialResponse.credential,
      });

      if (response.data.accessToken) {
        localStorage.setItem("accessToken", response.data.accessToken);
        toast.success("Google ile giriş başarılı");
        window.location.href = "/";
      }
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || "Google ile giriş başarısız"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleError = () => {
    toast.error("Google ile giriş başarısız");
  };

  const getTitle = () => {
    switch (step) {
      case "options":
        return "Giriş Yap";
      case "phone":
        return "Telefon ile Giriş";
      case "otp":
        return "OTP Doğrulama";
      default:
        return "Giriş Yap";
    }
  };

  const getDescription = () => {
    switch (step) {
      case "options":
        return "Giriş yapmak için bir yöntem seçin";
      case "phone":
        return "Telefon numaranızı girin";
      case "otp":
        return "Size gönderilen 4 haneli kodu girin";
      default:
        return "";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-md">
        {/* Progress indicator for phone/otp steps */}
        {step !== "options" && (
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-4">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 ${
                  step === "phone"
                    ? "bg-red-600 border-red-600 text-white"
                    : "bg-white border-red-600 text-red-600"
                }`}
              >
                <Phone className="w-4 h-4" />
              </div>
              <div
                className={`h-1 w-12 transition-all duration-300 ${
                  step === "otp" ? "bg-red-600" : "bg-gray-200"
                }`}
              />
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 ${
                  step === "otp"
                    ? "bg-red-600 border-red-600 text-white"
                    : "bg-white border-gray-200 text-gray-400"
                }`}
              >
                <Shield className="w-4 h-4" />
              </div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-2 px-8">
              <span>Telefon</span>
              <span>Doğrulama</span>
            </div>
          </div>
        )}

        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
              {step === "options" ? (
                <LogIn className="w-8 h-8 text-white" />
              ) : step === "phone" ? (
                <Phone className="w-8 h-8 text-white" />
              ) : (
                <Shield className="w-8 h-8 text-white" />
              )}
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {getTitle()}
            </CardTitle>
            <CardDescription className="text-gray-600">
              {getDescription()}
            </CardDescription>
            {step === "otp" && (
              <Badge
                variant="outline"
                className="mx-auto mt-2 border-red-200 text-red-700"
              >
                <Clock className="w-3 h-3 mr-1" />
                {timer > 0
                  ? `${Math.floor(timer / 60)}:${(timer % 60)
                      .toString()
                      .padStart(2, "0")}`
                  : "Süre doldu"}
              </Badge>
            )}
          </CardHeader>
          <CardContent className="space-y-6">
            {step === "options" ? (
              <div className="space-y-6">
                {/* Google Login */}
                <div className="w-full">
                  <GoogleLogin
                    onSuccess={handleGoogleSuccess}
                    onError={handleGoogleError}
                    useOneTap={false}
                    theme="outline"
                    size="large"
                    width="100%"
                    text="signin_with"
                    locale="tr"
                  />
                </div>

                {/* Divider */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-gray-200" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-4 text-gray-500 font-medium">
                      veya
                    </span>
                  </div>
                </div>

                {/* Phone Login Button */}
                <Button
                  onClick={() => setStep("phone")}
                  className="w-full h-12 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Yükleniyor...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Smartphone className="w-4 h-4" />
                      <span>Telefon ile Giriş Yap</span>
                    </div>
                  )}
                </Button>
              </div>
            ) : step === "phone" ? (
              <form onSubmit={handleSendOtp} className="space-y-6">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => setStep("options")}
                  className="mb-4 text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Geri Dön
                </Button>

                <div className="space-y-2">
                  <Label
                    htmlFor="phone"
                    className="text-sm font-medium text-gray-700"
                  >
                    Telefon Numarası
                  </Label>
                  <div className="relative group">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400 group-focus-within:text-red-500 transition-colors" />
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="+998901234567"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="pl-10 h-12 border-gray-200 focus:border-red-500 focus:ring-red-500/20 transition-all duration-200"
                      required
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Uzbekistan telefon numaranızı girin
                  </p>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Gönderiliyor...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4" />
                      <span>OTP Gönder</span>
                    </div>
                  )}
                </Button>
              </form>
            ) : (
              <form onSubmit={handleVerifyOtp} className="space-y-6">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => setStep("phone")}
                  className="mb-4 text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Geri Dön
                </Button>

                <div className="space-y-4">
                  <div className="text-center">
                    <Label className="text-lg font-medium text-gray-700">
                      OTP Kodu
                    </Label>
                    <p className="text-sm text-gray-500 mt-1">
                      {phone.startsWith("+")
                        ? phone
                        : phone.startsWith("998")
                        ? `+${phone}`
                        : `+998${phone}`}{" "}
                      numarasına gönderilen 4 haneli kodu girin
                    </p>
                  </div>

                  <div className="flex justify-center py-4">
                    <InputOTP
                      maxLength={4}
                      pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                      value={otp}
                      onChange={(value) => setOtp(value)}
                      className="gap-3"
                    >
                      <InputOTPGroup className="gap-3">
                        <InputOTPSlot
                          index={0}
                          className="w-14 h-14 text-xl font-bold border-2 border-gray-200 focus:border-red-500 focus:ring-red-500/20 rounded-lg transition-all duration-200"
                        />
                        <InputOTPSlot
                          index={1}
                          className="w-14 h-14 text-xl font-bold border-2 border-gray-200 focus:border-red-500 focus:ring-red-500/20 rounded-lg transition-all duration-200"
                        />
                        <InputOTPSlot
                          index={2}
                          className="w-14 h-14 text-xl font-bold border-2 border-gray-200 focus:border-red-500 focus:ring-red-500/20 rounded-lg transition-all duration-200"
                        />
                        <InputOTPSlot
                          index={3}
                          className="w-14 h-14 text-xl font-bold border-2 border-gray-200 focus:border-red-500 focus:ring-red-500/20 rounded-lg transition-all duration-200"
                        />
                      </InputOTPGroup>
                    </InputOTP>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                  disabled={loading || otp.length !== 4}
                >
                  {loading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Doğrulanıyor...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4" />
                      <span>Giriş Yap</span>
                    </div>
                  )}
                </Button>

                <div className="text-center">
                  {timer > 0 ? (
                    <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                      <Clock className="w-4 h-4" />
                      <span>
                        Tekrar gönder: {Math.floor(timer / 60)}:
                        {(timer % 60).toString().padStart(2, "0")}
                      </span>
                    </div>
                  ) : (
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full h-12 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 transition-all duration-200"
                      onClick={() => handleSendOtp()}
                      disabled={loading || !canResend}
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Tekrar Gönder
                    </Button>
                  )}
                </div>
              </form>
            )}
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            Hesabınız yok mu?{" "}
            <NavLink
              to="/signup"
              className="text-red-600 hover:text-red-700 font-medium hover:underline transition-colors"
            >
              Kayıt Ol
            </NavLink>
          </p>
        </div>

        {/* Trust indicators */}
        <div className="mt-6 flex items-center justify-center space-x-6 text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <Shield className="w-3 h-3" />
            <span>Güvenli</span>
          </div>
          <div className="flex items-center space-x-1">
            <CheckCircle className="w-3 h-3" />
            <span>Hızlı Giriş</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3" />
            <span>7/24 Erişim</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
