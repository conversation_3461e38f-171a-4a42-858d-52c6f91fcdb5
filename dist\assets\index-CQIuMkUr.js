var N0=n=>{throw TypeError(n)};var Td=(n,r,i)=>r.has(n)||N0("Cannot "+i);var Z=(n,r,i)=>(Td(n,r,"read from private field"),i?i.call(n):r.get(n)),Qe=(n,r,i)=>r.has(n)?N0("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(n):r.set(n,i),je=(n,r,i,o)=>(Td(n,r,"write to private field"),o?o.call(n,i):r.set(n,i),i),kt=(n,r,i)=>(Td(n,r,"access private method"),i);var _o=(n,r,i,o)=>({set _(d){je(n,r,d,i)},get _(){return Z(n,r,o)}});function f1(n,r){for(var i=0;i<r.length;i++){const o=r[i];if(typeof o!="string"&&!Array.isArray(o)){for(const d in o)if(d!=="default"&&!(d in n)){const f=Object.getOwnPropertyDescriptor(o,d);f&&Object.defineProperty(n,d,f.get?f:{enumerable:!0,get:()=>o[d]})}}}return Object.freeze(Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))o(d);new MutationObserver(d=>{for(const f of d)if(f.type==="childList")for(const m of f.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&o(m)}).observe(document,{childList:!0,subtree:!0});function i(d){const f={};return d.integrity&&(f.integrity=d.integrity),d.referrerPolicy&&(f.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?f.credentials="include":d.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function o(d){if(d.ep)return;d.ep=!0;const f=i(d);fetch(d.href,f)}})();function Xg(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var Rd={exports:{}},$i={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var E0;function h1(){if(E0)return $i;E0=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function i(o,d,f){var m=null;if(f!==void 0&&(m=""+f),d.key!==void 0&&(m=""+d.key),"key"in d){f={};for(var h in d)h!=="key"&&(f[h]=d[h])}else f=d;return d=f.ref,{$$typeof:n,type:o,key:m,ref:d!==void 0?d:null,props:f}}return $i.Fragment=r,$i.jsx=i,$i.jsxs=i,$i}var j0;function m1(){return j0||(j0=1,Rd.exports=h1()),Rd.exports}var u=m1(),Md={exports:{}},Ji={},Ad={exports:{}},Cd={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var T0;function v1(){return T0||(T0=1,function(n){function r(L,ae){var J=L.length;L.push(ae);e:for(;0<J;){var Ee=J-1>>>1,R=L[Ee];if(0<d(R,ae))L[Ee]=ae,L[J]=R,J=Ee;else break e}}function i(L){return L.length===0?null:L[0]}function o(L){if(L.length===0)return null;var ae=L[0],J=L.pop();if(J!==ae){L[0]=J;e:for(var Ee=0,R=L.length,X=R>>>1;Ee<X;){var le=2*(Ee+1)-1,ee=L[le],re=le+1,Te=L[re];if(0>d(ee,J))re<R&&0>d(Te,ee)?(L[Ee]=Te,L[re]=J,Ee=re):(L[Ee]=ee,L[le]=J,Ee=le);else if(re<R&&0>d(Te,J))L[Ee]=Te,L[re]=J,Ee=re;else break e}}return ae}function d(L,ae){var J=L.sortIndex-ae.sortIndex;return J!==0?J:L.id-ae.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var m=Date,h=m.now();n.unstable_now=function(){return m.now()-h}}var v=[],g=[],y=1,S=null,w=3,T=!1,j=!1,N=!1,C=!1,M=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,_=typeof setImmediate<"u"?setImmediate:null;function V(L){for(var ae=i(g);ae!==null;){if(ae.callback===null)o(g);else if(ae.startTime<=L)o(g),ae.sortIndex=ae.expirationTime,r(v,ae);else break;ae=i(g)}}function q(L){if(N=!1,V(L),!j)if(i(v)!==null)j=!0,O||(O=!0,qe());else{var ae=i(g);ae!==null&&Ne(q,ae.startTime-L)}}var O=!1,te=-1,K=5,W=-1;function ge(){return C?!0:!(n.unstable_now()-W<K)}function Le(){if(C=!1,O){var L=n.unstable_now();W=L;var ae=!0;try{e:{j=!1,N&&(N=!1,P(te),te=-1),T=!0;var J=w;try{t:{for(V(L),S=i(v);S!==null&&!(S.expirationTime>L&&ge());){var Ee=S.callback;if(typeof Ee=="function"){S.callback=null,w=S.priorityLevel;var R=Ee(S.expirationTime<=L);if(L=n.unstable_now(),typeof R=="function"){S.callback=R,V(L),ae=!0;break t}S===i(v)&&o(v),V(L)}else o(v);S=i(v)}if(S!==null)ae=!0;else{var X=i(g);X!==null&&Ne(q,X.startTime-L),ae=!1}}break e}finally{S=null,w=J,T=!1}ae=void 0}}finally{ae?qe():O=!1}}}var qe;if(typeof _=="function")qe=function(){_(Le)};else if(typeof MessageChannel<"u"){var pt=new MessageChannel,Me=pt.port2;pt.port1.onmessage=Le,qe=function(){Me.postMessage(null)}}else qe=function(){M(Le,0)};function Ne(L,ae){te=M(function(){L(n.unstable_now())},ae)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(L){L.callback=null},n.unstable_forceFrameRate=function(L){0>L||125<L?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<L?Math.floor(1e3/L):5},n.unstable_getCurrentPriorityLevel=function(){return w},n.unstable_next=function(L){switch(w){case 1:case 2:case 3:var ae=3;break;default:ae=w}var J=w;w=ae;try{return L()}finally{w=J}},n.unstable_requestPaint=function(){C=!0},n.unstable_runWithPriority=function(L,ae){switch(L){case 1:case 2:case 3:case 4:case 5:break;default:L=3}var J=w;w=L;try{return ae()}finally{w=J}},n.unstable_scheduleCallback=function(L,ae,J){var Ee=n.unstable_now();switch(typeof J=="object"&&J!==null?(J=J.delay,J=typeof J=="number"&&0<J?Ee+J:Ee):J=Ee,L){case 1:var R=-1;break;case 2:R=250;break;case 5:R=1073741823;break;case 4:R=1e4;break;default:R=5e3}return R=J+R,L={id:y++,callback:ae,priorityLevel:L,startTime:J,expirationTime:R,sortIndex:-1},J>Ee?(L.sortIndex=J,r(g,L),i(v)===null&&L===i(g)&&(N?(P(te),te=-1):N=!0,Ne(q,J-Ee))):(L.sortIndex=R,r(v,L),j||T||(j=!0,O||(O=!0,qe()))),L},n.unstable_shouldYield=ge,n.unstable_wrapCallback=function(L){var ae=w;return function(){var J=w;w=ae;try{return L.apply(this,arguments)}finally{w=J}}}}(Cd)),Cd}var R0;function g1(){return R0||(R0=1,Ad.exports=v1()),Ad.exports}var Dd={exports:{}},Re={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var M0;function p1(){if(M0)return Re;M0=1;var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),m=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),S=Symbol.iterator;function w(R){return R===null||typeof R!="object"?null:(R=S&&R[S]||R["@@iterator"],typeof R=="function"?R:null)}var T={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},j=Object.assign,N={};function C(R,X,le){this.props=R,this.context=X,this.refs=N,this.updater=le||T}C.prototype.isReactComponent={},C.prototype.setState=function(R,X){if(typeof R!="object"&&typeof R!="function"&&R!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,R,X,"setState")},C.prototype.forceUpdate=function(R){this.updater.enqueueForceUpdate(this,R,"forceUpdate")};function M(){}M.prototype=C.prototype;function P(R,X,le){this.props=R,this.context=X,this.refs=N,this.updater=le||T}var _=P.prototype=new M;_.constructor=P,j(_,C.prototype),_.isPureReactComponent=!0;var V=Array.isArray,q={H:null,A:null,T:null,S:null,V:null},O=Object.prototype.hasOwnProperty;function te(R,X,le,ee,re,Te){return le=Te.ref,{$$typeof:n,type:R,key:X,ref:le!==void 0?le:null,props:Te}}function K(R,X){return te(R.type,X,void 0,void 0,void 0,R.props)}function W(R){return typeof R=="object"&&R!==null&&R.$$typeof===n}function ge(R){var X={"=":"=0",":":"=2"};return"$"+R.replace(/[=:]/g,function(le){return X[le]})}var Le=/\/+/g;function qe(R,X){return typeof R=="object"&&R!==null&&R.key!=null?ge(""+R.key):X.toString(36)}function pt(){}function Me(R){switch(R.status){case"fulfilled":return R.value;case"rejected":throw R.reason;default:switch(typeof R.status=="string"?R.then(pt,pt):(R.status="pending",R.then(function(X){R.status==="pending"&&(R.status="fulfilled",R.value=X)},function(X){R.status==="pending"&&(R.status="rejected",R.reason=X)})),R.status){case"fulfilled":return R.value;case"rejected":throw R.reason}}throw R}function Ne(R,X,le,ee,re){var Te=typeof R;(Te==="undefined"||Te==="boolean")&&(R=null);var he=!1;if(R===null)he=!0;else switch(Te){case"bigint":case"string":case"number":he=!0;break;case"object":switch(R.$$typeof){case n:case r:he=!0;break;case y:return he=R._init,Ne(he(R._payload),X,le,ee,re)}}if(he)return re=re(R),he=ee===""?"."+qe(R,0):ee,V(re)?(le="",he!=null&&(le=he.replace(Le,"$&/")+"/"),Ne(re,X,le,"",function(Ht){return Ht})):re!=null&&(W(re)&&(re=K(re,le+(re.key==null||R&&R.key===re.key?"":(""+re.key).replace(Le,"$&/")+"/")+he)),X.push(re)),1;he=0;var He=ee===""?".":ee+":";if(V(R))for(var Ze=0;Ze<R.length;Ze++)ee=R[Ze],Te=He+qe(ee,Ze),he+=Ne(ee,X,le,Te,re);else if(Ze=w(R),typeof Ze=="function")for(R=Ze.call(R),Ze=0;!(ee=R.next()).done;)ee=ee.value,Te=He+qe(ee,Ze++),he+=Ne(ee,X,le,Te,re);else if(Te==="object"){if(typeof R.then=="function")return Ne(Me(R),X,le,ee,re);throw X=String(R),Error("Objects are not valid as a React child (found: "+(X==="[object Object]"?"object with keys {"+Object.keys(R).join(", ")+"}":X)+"). If you meant to render a collection of children, use an array instead.")}return he}function L(R,X,le){if(R==null)return R;var ee=[],re=0;return Ne(R,ee,"","",function(Te){return X.call(le,Te,re++)}),ee}function ae(R){if(R._status===-1){var X=R._result;X=X(),X.then(function(le){(R._status===0||R._status===-1)&&(R._status=1,R._result=le)},function(le){(R._status===0||R._status===-1)&&(R._status=2,R._result=le)}),R._status===-1&&(R._status=0,R._result=X)}if(R._status===1)return R._result.default;throw R._result}var J=typeof reportError=="function"?reportError:function(R){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var X=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof R=="object"&&R!==null&&typeof R.message=="string"?String(R.message):String(R),error:R});if(!window.dispatchEvent(X))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",R);return}console.error(R)};function Ee(){}return Re.Children={map:L,forEach:function(R,X,le){L(R,function(){X.apply(this,arguments)},le)},count:function(R){var X=0;return L(R,function(){X++}),X},toArray:function(R){return L(R,function(X){return X})||[]},only:function(R){if(!W(R))throw Error("React.Children.only expected to receive a single React element child.");return R}},Re.Component=C,Re.Fragment=i,Re.Profiler=d,Re.PureComponent=P,Re.StrictMode=o,Re.Suspense=v,Re.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=q,Re.__COMPILER_RUNTIME={__proto__:null,c:function(R){return q.H.useMemoCache(R)}},Re.cache=function(R){return function(){return R.apply(null,arguments)}},Re.cloneElement=function(R,X,le){if(R==null)throw Error("The argument must be a React element, but you passed "+R+".");var ee=j({},R.props),re=R.key,Te=void 0;if(X!=null)for(he in X.ref!==void 0&&(Te=void 0),X.key!==void 0&&(re=""+X.key),X)!O.call(X,he)||he==="key"||he==="__self"||he==="__source"||he==="ref"&&X.ref===void 0||(ee[he]=X[he]);var he=arguments.length-2;if(he===1)ee.children=le;else if(1<he){for(var He=Array(he),Ze=0;Ze<he;Ze++)He[Ze]=arguments[Ze+2];ee.children=He}return te(R.type,re,void 0,void 0,Te,ee)},Re.createContext=function(R){return R={$$typeof:m,_currentValue:R,_currentValue2:R,_threadCount:0,Provider:null,Consumer:null},R.Provider=R,R.Consumer={$$typeof:f,_context:R},R},Re.createElement=function(R,X,le){var ee,re={},Te=null;if(X!=null)for(ee in X.key!==void 0&&(Te=""+X.key),X)O.call(X,ee)&&ee!=="key"&&ee!=="__self"&&ee!=="__source"&&(re[ee]=X[ee]);var he=arguments.length-2;if(he===1)re.children=le;else if(1<he){for(var He=Array(he),Ze=0;Ze<he;Ze++)He[Ze]=arguments[Ze+2];re.children=He}if(R&&R.defaultProps)for(ee in he=R.defaultProps,he)re[ee]===void 0&&(re[ee]=he[ee]);return te(R,Te,void 0,void 0,null,re)},Re.createRef=function(){return{current:null}},Re.forwardRef=function(R){return{$$typeof:h,render:R}},Re.isValidElement=W,Re.lazy=function(R){return{$$typeof:y,_payload:{_status:-1,_result:R},_init:ae}},Re.memo=function(R,X){return{$$typeof:g,type:R,compare:X===void 0?null:X}},Re.startTransition=function(R){var X=q.T,le={};q.T=le;try{var ee=R(),re=q.S;re!==null&&re(le,ee),typeof ee=="object"&&ee!==null&&typeof ee.then=="function"&&ee.then(Ee,J)}catch(Te){J(Te)}finally{q.T=X}},Re.unstable_useCacheRefresh=function(){return q.H.useCacheRefresh()},Re.use=function(R){return q.H.use(R)},Re.useActionState=function(R,X,le){return q.H.useActionState(R,X,le)},Re.useCallback=function(R,X){return q.H.useCallback(R,X)},Re.useContext=function(R){return q.H.useContext(R)},Re.useDebugValue=function(){},Re.useDeferredValue=function(R,X){return q.H.useDeferredValue(R,X)},Re.useEffect=function(R,X,le){var ee=q.H;if(typeof le=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return ee.useEffect(R,X)},Re.useId=function(){return q.H.useId()},Re.useImperativeHandle=function(R,X,le){return q.H.useImperativeHandle(R,X,le)},Re.useInsertionEffect=function(R,X){return q.H.useInsertionEffect(R,X)},Re.useLayoutEffect=function(R,X){return q.H.useLayoutEffect(R,X)},Re.useMemo=function(R,X){return q.H.useMemo(R,X)},Re.useOptimistic=function(R,X){return q.H.useOptimistic(R,X)},Re.useReducer=function(R,X,le){return q.H.useReducer(R,X,le)},Re.useRef=function(R){return q.H.useRef(R)},Re.useState=function(R){return q.H.useState(R)},Re.useSyncExternalStore=function(R,X,le){return q.H.useSyncExternalStore(R,X,le)},Re.useTransition=function(){return q.H.useTransition()},Re.version="19.1.0",Re}var A0;function du(){return A0||(A0=1,Dd.exports=p1()),Dd.exports}var Od={exports:{}},Lt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var C0;function y1(){if(C0)return Lt;C0=1;var n=du();function r(v){var g="https://react.dev/errors/"+v;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var y=2;y<arguments.length;y++)g+="&args[]="+encodeURIComponent(arguments[y])}return"Minified React error #"+v+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var o={d:{f:i,r:function(){throw Error(r(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},d=Symbol.for("react.portal");function f(v,g,y){var S=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:S==null?null:""+S,children:v,containerInfo:g,implementation:y}}var m=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(v,g){if(v==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return Lt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,Lt.createPortal=function(v,g){var y=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(r(299));return f(v,g,null,y)},Lt.flushSync=function(v){var g=m.T,y=o.p;try{if(m.T=null,o.p=2,v)return v()}finally{m.T=g,o.p=y,o.d.f()}},Lt.preconnect=function(v,g){typeof v=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,o.d.C(v,g))},Lt.prefetchDNS=function(v){typeof v=="string"&&o.d.D(v)},Lt.preinit=function(v,g){if(typeof v=="string"&&g&&typeof g.as=="string"){var y=g.as,S=h(y,g.crossOrigin),w=typeof g.integrity=="string"?g.integrity:void 0,T=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;y==="style"?o.d.S(v,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:S,integrity:w,fetchPriority:T}):y==="script"&&o.d.X(v,{crossOrigin:S,integrity:w,fetchPriority:T,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},Lt.preinitModule=function(v,g){if(typeof v=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var y=h(g.as,g.crossOrigin);o.d.M(v,{crossOrigin:y,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&o.d.M(v)},Lt.preload=function(v,g){if(typeof v=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var y=g.as,S=h(y,g.crossOrigin);o.d.L(v,y,{crossOrigin:S,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},Lt.preloadModule=function(v,g){if(typeof v=="string")if(g){var y=h(g.as,g.crossOrigin);o.d.m(v,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:y,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else o.d.m(v)},Lt.requestFormReset=function(v){o.d.r(v)},Lt.unstable_batchedUpdates=function(v,g){return v(g)},Lt.useFormState=function(v,g,y){return m.H.useFormState(v,g,y)},Lt.useFormStatus=function(){return m.H.useHostTransitionStatus()},Lt.version="19.1.0",Lt}var D0;function Fg(){if(D0)return Od.exports;D0=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Od.exports=y1(),Od.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var O0;function x1(){if(O0)return Ji;O0=1;var n=g1(),r=du(),i=Fg();function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function m(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function h(e){if(f(e)!==e)throw Error(o(188))}function v(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(o(188));return t!==e?null:e}for(var a=e,l=t;;){var s=a.return;if(s===null)break;var c=s.alternate;if(c===null){if(l=s.return,l!==null){a=l;continue}break}if(s.child===c.child){for(c=s.child;c;){if(c===a)return h(s),e;if(c===l)return h(s),t;c=c.sibling}throw Error(o(188))}if(a.return!==l.return)a=s,l=c;else{for(var p=!1,b=s.child;b;){if(b===a){p=!0,a=s,l=c;break}if(b===l){p=!0,l=s,a=c;break}b=b.sibling}if(!p){for(b=c.child;b;){if(b===a){p=!0,a=c,l=s;break}if(b===l){p=!0,l=c,a=s;break}b=b.sibling}if(!p)throw Error(o(189))}}if(a.alternate!==l)throw Error(o(190))}if(a.tag!==3)throw Error(o(188));return a.stateNode.current===a?e:t}function g(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=g(e),t!==null)return t;e=e.sibling}return null}var y=Object.assign,S=Symbol.for("react.element"),w=Symbol.for("react.transitional.element"),T=Symbol.for("react.portal"),j=Symbol.for("react.fragment"),N=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),M=Symbol.for("react.provider"),P=Symbol.for("react.consumer"),_=Symbol.for("react.context"),V=Symbol.for("react.forward_ref"),q=Symbol.for("react.suspense"),O=Symbol.for("react.suspense_list"),te=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),W=Symbol.for("react.activity"),ge=Symbol.for("react.memo_cache_sentinel"),Le=Symbol.iterator;function qe(e){return e===null||typeof e!="object"?null:(e=Le&&e[Le]||e["@@iterator"],typeof e=="function"?e:null)}var pt=Symbol.for("react.client.reference");function Me(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===pt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case j:return"Fragment";case C:return"Profiler";case N:return"StrictMode";case q:return"Suspense";case O:return"SuspenseList";case W:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case T:return"Portal";case _:return(e.displayName||"Context")+".Provider";case P:return(e._context.displayName||"Context")+".Consumer";case V:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case te:return t=e.displayName||null,t!==null?t:Me(e.type)||"Memo";case K:t=e._payload,e=e._init;try{return Me(e(t))}catch{}}return null}var Ne=Array.isArray,L=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ae=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,J={pending:!1,data:null,method:null,action:null},Ee=[],R=-1;function X(e){return{current:e}}function le(e){0>R||(e.current=Ee[R],Ee[R]=null,R--)}function ee(e,t){R++,Ee[R]=e.current,e.current=t}var re=X(null),Te=X(null),he=X(null),He=X(null);function Ze(e,t){switch(ee(he,t),ee(Te,e),ee(re,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Jv(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Jv(t),e=Iv(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}le(re),ee(re,e)}function Ht(){le(re),le(Te),le(he)}function it(e){e.memoizedState!==null&&ee(He,e);var t=re.current,a=Iv(t,e.type);t!==a&&(ee(Te,e),ee(re,a))}function Bt(e){Te.current===e&&(le(re),le(Te)),He.current===e&&(le(He),Ki._currentValue=J)}var Ra=Object.prototype.hasOwnProperty,Ir=n.unstable_scheduleCallback,Ma=n.unstable_cancelCallback,xu=n.unstable_shouldYield,bu=n.unstable_requestPaint,ea=n.unstable_now,wu=n.unstable_getCurrentPriorityLevel,ys=n.unstable_ImmediatePriority,xs=n.unstable_UserBlockingPriority,$l=n.unstable_NormalPriority,tn=n.unstable_LowPriority,En=n.unstable_IdlePriority,bs=n.log,Wr=n.unstable_setDisableYieldValue,Pt=null,ut=null;function Aa(e){if(typeof bs=="function"&&Wr(e),ut&&typeof ut.setStrictMode=="function")try{ut.setStrictMode(Pt,e)}catch{}}var At=Math.clz32?Math.clz32:ws,Su=Math.log,qa=Math.LN2;function ws(e){return e>>>=0,e===0?32:31-(Su(e)/qa|0)|0}var fl=256,hl=4194304;function an(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ml(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var s=0,c=e.suspendedLanes,p=e.pingedLanes;e=e.warmLanes;var b=l&134217727;return b!==0?(l=b&~c,l!==0?s=an(l):(p&=b,p!==0?s=an(p):a||(a=b&~e,a!==0&&(s=an(a))))):(b=l&~c,b!==0?s=an(b):p!==0?s=an(p):a||(a=l&~e,a!==0&&(s=an(a)))),s===0?0:t!==0&&t!==s&&(t&c)===0&&(c=s&-s,a=t&-t,c>=a||c===32&&(a&4194048)!==0)?t:s}function Ga(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Ss(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jl(){var e=fl;return fl<<=1,(fl&4194048)===0&&(fl=256),e}function Ns(){var e=hl;return hl<<=1,(hl&62914560)===0&&(hl=4194304),e}function Il(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function vl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Es(e,t,a,l,s,c){var p=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var b=e.entanglements,E=e.expirationTimes,H=e.hiddenUpdates;for(a=p&~a;0<a;){var Q=31-At(a),$=1<<Q;b[Q]=0,E[Q]=-1;var B=H[Q];if(B!==null)for(H[Q]=null,Q=0;Q<B.length;Q++){var G=B[Q];G!==null&&(G.lane&=-536870913)}a&=~$}l!==0&&gl(e,l,0),c!==0&&s===0&&e.tag!==0&&(e.suspendedLanes|=c&~(p&~t))}function gl(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-At(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function pl(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-At(a),s=1<<l;s&t|e[l]&t&&(e[l]|=t),a&=~s}}function ei(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function ti(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function A(){var e=ae.p;return e!==0?e:(e=window.event,e===void 0?32:p0(e.type))}function k(e,t){var a=ae.p;try{return ae.p=e,t()}finally{ae.p=a}}var Y=Math.random().toString(36).slice(2),I="__reactFiber$"+Y,ne="__reactProps$"+Y,ce="__reactContainer$"+Y,pe="__reactEvents$"+Y,ie="__reactListeners$"+Y,me="__reactHandles$"+Y,ve="__reactResources$"+Y,be="__reactMarker$"+Y;function de(e){delete e[I],delete e[ne],delete e[pe],delete e[ie],delete e[me]}function Se(e){var t=e[I];if(t)return t;for(var a=e.parentNode;a;){if(t=a[ce]||a[I]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=a0(e);e!==null;){if(a=e[I])return a;e=a0(e)}return t}e=a,a=e.parentNode}return null}function Ge(e){if(e=e[I]||e[ce]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function lt(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(o(33))}function ct(e){var t=e[ve];return t||(t=e[ve]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ye(e){e[be]=!0}var Ue=new Set,jn={};function qt(e,t){ca(e,t),ca(e+"Capture",t)}function ca(e,t){for(jn[e]=t,e=0;e<t.length;e++)Ue.add(t[e])}var Xt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Tn={},Rn={};function ai(e){return Ra.call(Rn,e)?!0:Ra.call(Tn,e)?!1:Xt.test(e)?Rn[e]=!0:(Tn[e]=!0,!1)}function da(e,t,a){if(ai(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function nn(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function De(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var zt,Ca;function Da(e){if(zt===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);zt=t&&t[1]||"",Ca=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+zt+e+Ca}var yl=!1;function st(e,t){if(!e||yl)return"";yl=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var $=function(){throw Error()};if(Object.defineProperty($.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct($,[])}catch(G){var B=G}Reflect.construct(e,[],$)}else{try{$.call()}catch(G){B=G}e.call($.prototype)}}else{try{throw Error()}catch(G){B=G}($=e())&&typeof $.catch=="function"&&$.catch(function(){})}}catch(G){if(G&&B&&typeof G.stack=="string")return[G.stack,B.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),p=c[0],b=c[1];if(p&&b){var E=p.split(`
`),H=b.split(`
`);for(s=l=0;l<E.length&&!E[l].includes("DetermineComponentFrameRoot");)l++;for(;s<H.length&&!H[s].includes("DetermineComponentFrameRoot");)s++;if(l===E.length||s===H.length)for(l=E.length-1,s=H.length-1;1<=l&&0<=s&&E[l]!==H[s];)s--;for(;1<=l&&0<=s;l--,s--)if(E[l]!==H[s]){if(l!==1||s!==1)do if(l--,s--,0>s||E[l]!==H[s]){var Q=`
`+E[l].replace(" at new "," at ");return e.displayName&&Q.includes("<anonymous>")&&(Q=Q.replace("<anonymous>",e.displayName)),Q}while(1<=l&&0<=s);break}}}finally{yl=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?Da(a):""}function Mn(e){switch(e.tag){case 26:case 27:case 5:return Da(e.type);case 16:return Da("Lazy");case 13:return Da("Suspense");case 19:return Da("SuspenseList");case 0:case 15:return st(e.type,!1);case 11:return st(e.type.render,!1);case 1:return st(e.type,!0);case 31:return Da("Activity");default:return""}}function ni(e){try{var t="";do t+=Mn(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Ft(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Pf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function sx(e){var t=Pf(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var s=a.get,c=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(p){l=""+p,c.call(this,p)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(p){l=""+p},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function js(e){e._valueTracker||(e._valueTracker=sx(e))}function Xf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Pf(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function Ts(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var ox=/[\n"\\]/g;function fa(e){return e.replace(ox,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Nu(e,t,a,l,s,c,p,b){e.name="",p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.type=p:e.removeAttribute("type"),t!=null?p==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Ft(t)):e.value!==""+Ft(t)&&(e.value=""+Ft(t)):p!=="submit"&&p!=="reset"||e.removeAttribute("value"),t!=null?Eu(e,p,Ft(t)):a!=null?Eu(e,p,Ft(a)):l!=null&&e.removeAttribute("value"),s==null&&c!=null&&(e.defaultChecked=!!c),s!=null&&(e.checked=s&&typeof s!="function"&&typeof s!="symbol"),b!=null&&typeof b!="function"&&typeof b!="symbol"&&typeof b!="boolean"?e.name=""+Ft(b):e.removeAttribute("name")}function Ff(e,t,a,l,s,c,p,b){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||a!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;a=a!=null?""+Ft(a):"",t=t!=null?""+Ft(t):a,b||t===e.value||(e.value=t),e.defaultValue=t}l=l??s,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=b?e.checked:!!l,e.defaultChecked=!!l,p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"&&(e.name=p)}function Eu(e,t,a){t==="number"&&Ts(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function Wl(e,t,a,l){if(e=e.options,t){t={};for(var s=0;s<a.length;s++)t["$"+a[s]]=!0;for(a=0;a<e.length;a++)s=t.hasOwnProperty("$"+e[a].value),e[a].selected!==s&&(e[a].selected=s),s&&l&&(e[a].defaultSelected=!0)}else{for(a=""+Ft(a),t=null,s=0;s<e.length;s++){if(e[s].value===a){e[s].selected=!0,l&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Zf(e,t,a){if(t!=null&&(t=""+Ft(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Ft(a):""}function $f(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(o(92));if(Ne(l)){if(1<l.length)throw Error(o(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=Ft(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function er(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var ux=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Jf(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||ux.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function If(e,t,a){if(t!=null&&typeof t!="object")throw Error(o(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var s in t)l=t[s],t.hasOwnProperty(s)&&a[s]!==l&&Jf(e,s,l)}else for(var c in t)t.hasOwnProperty(c)&&Jf(e,c,t[c])}function ju(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var cx=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),dx=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Rs(e){return dx.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Tu=null;function Ru(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var tr=null,ar=null;function Wf(e){var t=Ge(e);if(t&&(e=t.stateNode)){var a=e[ne]||null;e:switch(e=t.stateNode,t.type){case"input":if(Nu(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+fa(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var s=l[ne]||null;if(!s)throw Error(o(90));Nu(l,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&Xf(l)}break e;case"textarea":Zf(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&Wl(e,!!a.multiple,t,!1)}}}var Mu=!1;function eh(e,t,a){if(Mu)return e(t,a);Mu=!0;try{var l=e(t);return l}finally{if(Mu=!1,(tr!==null||ar!==null)&&(ho(),tr&&(t=tr,e=ar,ar=tr=null,Wf(t),e)))for(t=0;t<e.length;t++)Wf(e[t])}}function li(e,t){var a=e.stateNode;if(a===null)return null;var l=a[ne]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(o(231,t,typeof a));return a}var ln=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Au=!1;if(ln)try{var ri={};Object.defineProperty(ri,"passive",{get:function(){Au=!0}}),window.addEventListener("test",ri,ri),window.removeEventListener("test",ri,ri)}catch{Au=!1}var An=null,Cu=null,Ms=null;function th(){if(Ms)return Ms;var e,t=Cu,a=t.length,l,s="value"in An?An.value:An.textContent,c=s.length;for(e=0;e<a&&t[e]===s[e];e++);var p=a-e;for(l=1;l<=p&&t[a-l]===s[c-l];l++);return Ms=s.slice(e,1<l?1-l:void 0)}function As(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Cs(){return!0}function ah(){return!1}function Zt(e){function t(a,l,s,c,p){this._reactName=a,this._targetInst=s,this.type=l,this.nativeEvent=c,this.target=p,this.currentTarget=null;for(var b in e)e.hasOwnProperty(b)&&(a=e[b],this[b]=a?a(c):c[b]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Cs:ah,this.isPropagationStopped=ah,this}return y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Cs)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Cs)},persist:function(){},isPersistent:Cs}),t}var xl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ds=Zt(xl),ii=y({},xl,{view:0,detail:0}),fx=Zt(ii),Du,Ou,si,Os=y({},ii,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_u,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==si&&(si&&e.type==="mousemove"?(Du=e.screenX-si.screenX,Ou=e.screenY-si.screenY):Ou=Du=0,si=e),Du)},movementY:function(e){return"movementY"in e?e.movementY:Ou}}),nh=Zt(Os),hx=y({},Os,{dataTransfer:0}),mx=Zt(hx),vx=y({},ii,{relatedTarget:0}),zu=Zt(vx),gx=y({},xl,{animationName:0,elapsedTime:0,pseudoElement:0}),px=Zt(gx),yx=y({},xl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xx=Zt(yx),bx=y({},xl,{data:0}),lh=Zt(bx),wx={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Nx={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Ex(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Nx[e])?!!t[e]:!1}function _u(){return Ex}var jx=y({},ii,{key:function(e){if(e.key){var t=wx[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=As(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Sx[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_u,charCode:function(e){return e.type==="keypress"?As(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?As(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Tx=Zt(jx),Rx=y({},Os,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),rh=Zt(Rx),Mx=y({},ii,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_u}),Ax=Zt(Mx),Cx=y({},xl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Dx=Zt(Cx),Ox=y({},Os,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),zx=Zt(Ox),_x=y({},xl,{newState:0,oldState:0}),kx=Zt(_x),Lx=[9,13,27,32],ku=ln&&"CompositionEvent"in window,oi=null;ln&&"documentMode"in document&&(oi=document.documentMode);var Ux=ln&&"TextEvent"in window&&!oi,ih=ln&&(!ku||oi&&8<oi&&11>=oi),sh=" ",oh=!1;function uh(e,t){switch(e){case"keyup":return Lx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ch(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var nr=!1;function Hx(e,t){switch(e){case"compositionend":return ch(t);case"keypress":return t.which!==32?null:(oh=!0,sh);case"textInput":return e=t.data,e===sh&&oh?null:e;default:return null}}function Bx(e,t){if(nr)return e==="compositionend"||!ku&&uh(e,t)?(e=th(),Ms=Cu=An=null,nr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ih&&t.locale!=="ko"?null:t.data;default:return null}}var qx={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function dh(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!qx[e.type]:t==="textarea"}function fh(e,t,a,l){tr?ar?ar.push(l):ar=[l]:tr=l,t=xo(t,"onChange"),0<t.length&&(a=new Ds("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var ui=null,ci=null;function Gx(e){Pv(e,0)}function zs(e){var t=lt(e);if(Xf(t))return e}function hh(e,t){if(e==="change")return t}var mh=!1;if(ln){var Lu;if(ln){var Uu="oninput"in document;if(!Uu){var vh=document.createElement("div");vh.setAttribute("oninput","return;"),Uu=typeof vh.oninput=="function"}Lu=Uu}else Lu=!1;mh=Lu&&(!document.documentMode||9<document.documentMode)}function gh(){ui&&(ui.detachEvent("onpropertychange",ph),ci=ui=null)}function ph(e){if(e.propertyName==="value"&&zs(ci)){var t=[];fh(t,ci,e,Ru(e)),eh(Gx,t)}}function Yx(e,t,a){e==="focusin"?(gh(),ui=t,ci=a,ui.attachEvent("onpropertychange",ph)):e==="focusout"&&gh()}function Vx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return zs(ci)}function Qx(e,t){if(e==="click")return zs(t)}function Kx(e,t){if(e==="input"||e==="change")return zs(t)}function Px(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ta=typeof Object.is=="function"?Object.is:Px;function di(e,t){if(ta(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var s=a[l];if(!Ra.call(t,s)||!ta(e[s],t[s]))return!1}return!0}function yh(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function xh(e,t){var a=yh(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=yh(a)}}function bh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?bh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function wh(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ts(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Ts(e.document)}return t}function Hu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Xx=ln&&"documentMode"in document&&11>=document.documentMode,lr=null,Bu=null,fi=null,qu=!1;function Sh(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;qu||lr==null||lr!==Ts(l)||(l=lr,"selectionStart"in l&&Hu(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),fi&&di(fi,l)||(fi=l,l=xo(Bu,"onSelect"),0<l.length&&(t=new Ds("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=lr)))}function bl(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var rr={animationend:bl("Animation","AnimationEnd"),animationiteration:bl("Animation","AnimationIteration"),animationstart:bl("Animation","AnimationStart"),transitionrun:bl("Transition","TransitionRun"),transitionstart:bl("Transition","TransitionStart"),transitioncancel:bl("Transition","TransitionCancel"),transitionend:bl("Transition","TransitionEnd")},Gu={},Nh={};ln&&(Nh=document.createElement("div").style,"AnimationEvent"in window||(delete rr.animationend.animation,delete rr.animationiteration.animation,delete rr.animationstart.animation),"TransitionEvent"in window||delete rr.transitionend.transition);function wl(e){if(Gu[e])return Gu[e];if(!rr[e])return e;var t=rr[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Nh)return Gu[e]=t[a];return e}var Eh=wl("animationend"),jh=wl("animationiteration"),Th=wl("animationstart"),Fx=wl("transitionrun"),Zx=wl("transitionstart"),$x=wl("transitioncancel"),Rh=wl("transitionend"),Mh=new Map,Yu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Yu.push("scrollEnd");function Oa(e,t){Mh.set(e,t),qt(t,[e])}var Ah=new WeakMap;function ha(e,t){if(typeof e=="object"&&e!==null){var a=Ah.get(e);return a!==void 0?a:(t={value:e,source:t,stack:ni(t)},Ah.set(e,t),t)}return{value:e,source:t,stack:ni(t)}}var ma=[],ir=0,Vu=0;function _s(){for(var e=ir,t=Vu=ir=0;t<e;){var a=ma[t];ma[t++]=null;var l=ma[t];ma[t++]=null;var s=ma[t];ma[t++]=null;var c=ma[t];if(ma[t++]=null,l!==null&&s!==null){var p=l.pending;p===null?s.next=s:(s.next=p.next,p.next=s),l.pending=s}c!==0&&Ch(a,s,c)}}function ks(e,t,a,l){ma[ir++]=e,ma[ir++]=t,ma[ir++]=a,ma[ir++]=l,Vu|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Qu(e,t,a,l){return ks(e,t,a,l),Ls(e)}function sr(e,t){return ks(e,null,null,t),Ls(e)}function Ch(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var s=!1,c=e.return;c!==null;)c.childLanes|=a,l=c.alternate,l!==null&&(l.childLanes|=a),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(s=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,s&&t!==null&&(s=31-At(a),e=c.hiddenUpdates,l=e[s],l===null?e[s]=[t]:l.push(t),t.lane=a|536870912),c):null}function Ls(e){if(50<Ui)throw Ui=0,$c=null,Error(o(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var or={};function Jx(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function aa(e,t,a,l){return new Jx(e,t,a,l)}function Ku(e){return e=e.prototype,!(!e||!e.isReactComponent)}function rn(e,t){var a=e.alternate;return a===null?(a=aa(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Dh(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Us(e,t,a,l,s,c){var p=0;if(l=e,typeof e=="function")Ku(e)&&(p=1);else if(typeof e=="string")p=Wb(e,a,re.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case W:return e=aa(31,a,t,s),e.elementType=W,e.lanes=c,e;case j:return Sl(a.children,s,c,t);case N:p=8,s|=24;break;case C:return e=aa(12,a,t,s|2),e.elementType=C,e.lanes=c,e;case q:return e=aa(13,a,t,s),e.elementType=q,e.lanes=c,e;case O:return e=aa(19,a,t,s),e.elementType=O,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case M:case _:p=10;break e;case P:p=9;break e;case V:p=11;break e;case te:p=14;break e;case K:p=16,l=null;break e}p=29,a=Error(o(130,e===null?"null":typeof e,"")),l=null}return t=aa(p,a,t,s),t.elementType=e,t.type=l,t.lanes=c,t}function Sl(e,t,a,l){return e=aa(7,e,l,t),e.lanes=a,e}function Pu(e,t,a){return e=aa(6,e,null,t),e.lanes=a,e}function Xu(e,t,a){return t=aa(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ur=[],cr=0,Hs=null,Bs=0,va=[],ga=0,Nl=null,sn=1,on="";function El(e,t){ur[cr++]=Bs,ur[cr++]=Hs,Hs=e,Bs=t}function Oh(e,t,a){va[ga++]=sn,va[ga++]=on,va[ga++]=Nl,Nl=e;var l=sn;e=on;var s=32-At(l)-1;l&=~(1<<s),a+=1;var c=32-At(t)+s;if(30<c){var p=s-s%5;c=(l&(1<<p)-1).toString(32),l>>=p,s-=p,sn=1<<32-At(t)+s|a<<s|l,on=c+e}else sn=1<<c|a<<s|l,on=e}function Fu(e){e.return!==null&&(El(e,1),Oh(e,1,0))}function Zu(e){for(;e===Hs;)Hs=ur[--cr],ur[cr]=null,Bs=ur[--cr],ur[cr]=null;for(;e===Nl;)Nl=va[--ga],va[ga]=null,on=va[--ga],va[ga]=null,sn=va[--ga],va[ga]=null}var Gt=null,ft=null,Pe=!1,jl=null,Ya=!1,$u=Error(o(519));function Tl(e){var t=Error(o(418,""));throw vi(ha(t,e)),$u}function zh(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[I]=e,t[ne]=l,a){case"dialog":_e("cancel",t),_e("close",t);break;case"iframe":case"object":case"embed":_e("load",t);break;case"video":case"audio":for(a=0;a<Bi.length;a++)_e(Bi[a],t);break;case"source":_e("error",t);break;case"img":case"image":case"link":_e("error",t),_e("load",t);break;case"details":_e("toggle",t);break;case"input":_e("invalid",t),Ff(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),js(t);break;case"select":_e("invalid",t);break;case"textarea":_e("invalid",t),$f(t,l.value,l.defaultValue,l.children),js(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||$v(t.textContent,a)?(l.popover!=null&&(_e("beforetoggle",t),_e("toggle",t)),l.onScroll!=null&&_e("scroll",t),l.onScrollEnd!=null&&_e("scrollend",t),l.onClick!=null&&(t.onclick=bo),t=!0):t=!1,t||Tl(e)}function _h(e){for(Gt=e.return;Gt;)switch(Gt.tag){case 5:case 13:Ya=!1;return;case 27:case 3:Ya=!0;return;default:Gt=Gt.return}}function hi(e){if(e!==Gt)return!1;if(!Pe)return _h(e),Pe=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||fd(e.type,e.memoizedProps)),a=!a),a&&ft&&Tl(e),_h(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){ft=_a(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}ft=null}}else t===27?(t=ft,Kn(e.type)?(e=gd,gd=null,ft=e):ft=t):ft=Gt?_a(e.stateNode.nextSibling):null;return!0}function mi(){ft=Gt=null,Pe=!1}function kh(){var e=jl;return e!==null&&(It===null?It=e:It.push.apply(It,e),jl=null),e}function vi(e){jl===null?jl=[e]:jl.push(e)}var Ju=X(null),Rl=null,un=null;function Cn(e,t,a){ee(Ju,t._currentValue),t._currentValue=a}function cn(e){e._currentValue=Ju.current,le(Ju)}function Iu(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Wu(e,t,a,l){var s=e.child;for(s!==null&&(s.return=e);s!==null;){var c=s.dependencies;if(c!==null){var p=s.child;c=c.firstContext;e:for(;c!==null;){var b=c;c=s;for(var E=0;E<t.length;E++)if(b.context===t[E]){c.lanes|=a,b=c.alternate,b!==null&&(b.lanes|=a),Iu(c.return,a,e),l||(p=null);break e}c=b.next}}else if(s.tag===18){if(p=s.return,p===null)throw Error(o(341));p.lanes|=a,c=p.alternate,c!==null&&(c.lanes|=a),Iu(p,a,e),p=null}else p=s.child;if(p!==null)p.return=s;else for(p=s;p!==null;){if(p===e){p=null;break}if(s=p.sibling,s!==null){s.return=p.return,p=s;break}p=p.return}s=p}}function gi(e,t,a,l){e=null;for(var s=t,c=!1;s!==null;){if(!c){if((s.flags&524288)!==0)c=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var p=s.alternate;if(p===null)throw Error(o(387));if(p=p.memoizedProps,p!==null){var b=s.type;ta(s.pendingProps.value,p.value)||(e!==null?e.push(b):e=[b])}}else if(s===He.current){if(p=s.alternate,p===null)throw Error(o(387));p.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(e!==null?e.push(Ki):e=[Ki])}s=s.return}e!==null&&Wu(t,e,a,l),t.flags|=262144}function qs(e){for(e=e.firstContext;e!==null;){if(!ta(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ml(e){Rl=e,un=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function _t(e){return Lh(Rl,e)}function Gs(e,t){return Rl===null&&Ml(e),Lh(e,t)}function Lh(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},un===null){if(e===null)throw Error(o(308));un=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else un=un.next=t;return a}var Ix=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},Wx=n.unstable_scheduleCallback,eb=n.unstable_NormalPriority,wt={$$typeof:_,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function ec(){return{controller:new Ix,data:new Map,refCount:0}}function pi(e){e.refCount--,e.refCount===0&&Wx(eb,function(){e.controller.abort()})}var yi=null,tc=0,dr=0,fr=null;function tb(e,t){if(yi===null){var a=yi=[];tc=0,dr=nd(),fr={status:"pending",value:void 0,then:function(l){a.push(l)}}}return tc++,t.then(Uh,Uh),t}function Uh(){if(--tc===0&&yi!==null){fr!==null&&(fr.status="fulfilled");var e=yi;yi=null,dr=0,fr=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function ab(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(s){a.push(s)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var s=0;s<a.length;s++)(0,a[s])(t)},function(s){for(l.status="rejected",l.reason=s,s=0;s<a.length;s++)(0,a[s])(void 0)}),l}var Hh=L.S;L.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&tb(e,t),Hh!==null&&Hh(e,t)};var Al=X(null);function ac(){var e=Al.current;return e!==null?e:at.pooledCache}function Ys(e,t){t===null?ee(Al,Al.current):ee(Al,t.pool)}function Bh(){var e=ac();return e===null?null:{parent:wt._currentValue,pool:e}}var xi=Error(o(460)),qh=Error(o(474)),Vs=Error(o(542)),nc={then:function(){}};function Gh(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Qs(){}function Yh(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Qs,Qs),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Qh(e),e;default:if(typeof t.status=="string")t.then(Qs,Qs);else{if(e=at,e!==null&&100<e.shellSuspendCounter)throw Error(o(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var s=t;s.status="fulfilled",s.value=l}},function(l){if(t.status==="pending"){var s=t;s.status="rejected",s.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Qh(e),e}throw bi=t,xi}}var bi=null;function Vh(){if(bi===null)throw Error(o(459));var e=bi;return bi=null,e}function Qh(e){if(e===xi||e===Vs)throw Error(o(483))}var Dn=!1;function lc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function rc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function On(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function zn(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,($e&2)!==0){var s=l.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),l.pending=t,t=Ls(e),Ch(e,null,a),t}return ks(e,l,t,a),Ls(e)}function wi(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,pl(e,a)}}function ic(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var s=null,c=null;if(a=a.firstBaseUpdate,a!==null){do{var p={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};c===null?s=c=p:c=c.next=p,a=a.next}while(a!==null);c===null?s=c=t:c=c.next=t}else s=c=t;a={baseState:l.baseState,firstBaseUpdate:s,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var sc=!1;function Si(){if(sc){var e=fr;if(e!==null)throw e}}function Ni(e,t,a,l){sc=!1;var s=e.updateQueue;Dn=!1;var c=s.firstBaseUpdate,p=s.lastBaseUpdate,b=s.shared.pending;if(b!==null){s.shared.pending=null;var E=b,H=E.next;E.next=null,p===null?c=H:p.next=H,p=E;var Q=e.alternate;Q!==null&&(Q=Q.updateQueue,b=Q.lastBaseUpdate,b!==p&&(b===null?Q.firstBaseUpdate=H:b.next=H,Q.lastBaseUpdate=E))}if(c!==null){var $=s.baseState;p=0,Q=H=E=null,b=c;do{var B=b.lane&-536870913,G=B!==b.lane;if(G?(Be&B)===B:(l&B)===B){B!==0&&B===dr&&(sc=!0),Q!==null&&(Q=Q.next={lane:0,tag:b.tag,payload:b.payload,callback:null,next:null});e:{var we=e,ye=b;B=t;var et=a;switch(ye.tag){case 1:if(we=ye.payload,typeof we=="function"){$=we.call(et,$,B);break e}$=we;break e;case 3:we.flags=we.flags&-65537|128;case 0:if(we=ye.payload,B=typeof we=="function"?we.call(et,$,B):we,B==null)break e;$=y({},$,B);break e;case 2:Dn=!0}}B=b.callback,B!==null&&(e.flags|=64,G&&(e.flags|=8192),G=s.callbacks,G===null?s.callbacks=[B]:G.push(B))}else G={lane:B,tag:b.tag,payload:b.payload,callback:b.callback,next:null},Q===null?(H=Q=G,E=$):Q=Q.next=G,p|=B;if(b=b.next,b===null){if(b=s.shared.pending,b===null)break;G=b,b=G.next,G.next=null,s.lastBaseUpdate=G,s.shared.pending=null}}while(!0);Q===null&&(E=$),s.baseState=E,s.firstBaseUpdate=H,s.lastBaseUpdate=Q,c===null&&(s.shared.lanes=0),Gn|=p,e.lanes=p,e.memoizedState=$}}function Kh(e,t){if(typeof e!="function")throw Error(o(191,e));e.call(t)}function Ph(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Kh(a[e],t)}var hr=X(null),Ks=X(0);function Xh(e,t){e=pn,ee(Ks,e),ee(hr,t),pn=e|t.baseLanes}function oc(){ee(Ks,pn),ee(hr,hr.current)}function uc(){pn=Ks.current,le(hr),le(Ks)}var _n=0,Ae=null,Ie=null,yt=null,Ps=!1,mr=!1,Cl=!1,Xs=0,Ei=0,vr=null,nb=0;function vt(){throw Error(o(321))}function cc(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!ta(e[a],t[a]))return!1;return!0}function dc(e,t,a,l,s,c){return _n=c,Ae=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,L.H=e===null||e.memoizedState===null?Cm:Dm,Cl=!1,c=a(l,s),Cl=!1,mr&&(c=Zh(t,a,l,s)),Fh(e),c}function Fh(e){L.H=Ws;var t=Ie!==null&&Ie.next!==null;if(_n=0,yt=Ie=Ae=null,Ps=!1,Ei=0,vr=null,t)throw Error(o(300));e===null||jt||(e=e.dependencies,e!==null&&qs(e)&&(jt=!0))}function Zh(e,t,a,l){Ae=e;var s=0;do{if(mr&&(vr=null),Ei=0,mr=!1,25<=s)throw Error(o(301));if(s+=1,yt=Ie=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}L.H=cb,c=t(a,l)}while(mr);return c}function lb(){var e=L.H,t=e.useState()[0];return t=typeof t.then=="function"?ji(t):t,e=e.useState()[0],(Ie!==null?Ie.memoizedState:null)!==e&&(Ae.flags|=1024),t}function fc(){var e=Xs!==0;return Xs=0,e}function hc(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function mc(e){if(Ps){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Ps=!1}_n=0,yt=Ie=Ae=null,mr=!1,Ei=Xs=0,vr=null}function $t(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return yt===null?Ae.memoizedState=yt=e:yt=yt.next=e,yt}function xt(){if(Ie===null){var e=Ae.alternate;e=e!==null?e.memoizedState:null}else e=Ie.next;var t=yt===null?Ae.memoizedState:yt.next;if(t!==null)yt=t,Ie=e;else{if(e===null)throw Ae.alternate===null?Error(o(467)):Error(o(310));Ie=e,e={memoizedState:Ie.memoizedState,baseState:Ie.baseState,baseQueue:Ie.baseQueue,queue:Ie.queue,next:null},yt===null?Ae.memoizedState=yt=e:yt=yt.next=e}return yt}function vc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ji(e){var t=Ei;return Ei+=1,vr===null&&(vr=[]),e=Yh(vr,e,t),t=Ae,(yt===null?t.memoizedState:yt.next)===null&&(t=t.alternate,L.H=t===null||t.memoizedState===null?Cm:Dm),e}function Fs(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ji(e);if(e.$$typeof===_)return _t(e)}throw Error(o(438,String(e)))}function gc(e){var t=null,a=Ae.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=Ae.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(s){return s.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=vc(),Ae.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=ge;return t.index++,a}function dn(e,t){return typeof t=="function"?t(e):t}function Zs(e){var t=xt();return pc(t,Ie,e)}function pc(e,t,a){var l=e.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=a;var s=e.baseQueue,c=l.pending;if(c!==null){if(s!==null){var p=s.next;s.next=c.next,c.next=p}t.baseQueue=s=c,l.pending=null}if(c=e.baseState,s===null)e.memoizedState=c;else{t=s.next;var b=p=null,E=null,H=t,Q=!1;do{var $=H.lane&-536870913;if($!==H.lane?(Be&$)===$:(_n&$)===$){var B=H.revertLane;if(B===0)E!==null&&(E=E.next={lane:0,revertLane:0,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null}),$===dr&&(Q=!0);else if((_n&B)===B){H=H.next,B===dr&&(Q=!0);continue}else $={lane:0,revertLane:H.revertLane,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null},E===null?(b=E=$,p=c):E=E.next=$,Ae.lanes|=B,Gn|=B;$=H.action,Cl&&a(c,$),c=H.hasEagerState?H.eagerState:a(c,$)}else B={lane:$,revertLane:H.revertLane,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null},E===null?(b=E=B,p=c):E=E.next=B,Ae.lanes|=$,Gn|=$;H=H.next}while(H!==null&&H!==t);if(E===null?p=c:E.next=b,!ta(c,e.memoizedState)&&(jt=!0,Q&&(a=fr,a!==null)))throw a;e.memoizedState=c,e.baseState=p,e.baseQueue=E,l.lastRenderedState=c}return s===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function yc(e){var t=xt(),a=t.queue;if(a===null)throw Error(o(311));a.lastRenderedReducer=e;var l=a.dispatch,s=a.pending,c=t.memoizedState;if(s!==null){a.pending=null;var p=s=s.next;do c=e(c,p.action),p=p.next;while(p!==s);ta(c,t.memoizedState)||(jt=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),a.lastRenderedState=c}return[c,l]}function $h(e,t,a){var l=Ae,s=xt(),c=Pe;if(c){if(a===void 0)throw Error(o(407));a=a()}else a=t();var p=!ta((Ie||s).memoizedState,a);p&&(s.memoizedState=a,jt=!0),s=s.queue;var b=Wh.bind(null,l,s,e);if(Ti(2048,8,b,[e]),s.getSnapshot!==t||p||yt!==null&&yt.memoizedState.tag&1){if(l.flags|=2048,gr(9,$s(),Ih.bind(null,l,s,a,t),null),at===null)throw Error(o(349));c||(_n&124)!==0||Jh(l,t,a)}return a}function Jh(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=Ae.updateQueue,t===null?(t=vc(),Ae.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function Ih(e,t,a,l){t.value=a,t.getSnapshot=l,em(t)&&tm(e)}function Wh(e,t,a){return a(function(){em(t)&&tm(e)})}function em(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!ta(e,a)}catch{return!0}}function tm(e){var t=sr(e,2);t!==null&&sa(t,e,2)}function xc(e){var t=$t();if(typeof e=="function"){var a=e;if(e=a(),Cl){Aa(!0);try{a()}finally{Aa(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:dn,lastRenderedState:e},t}function am(e,t,a,l){return e.baseState=a,pc(e,Ie,typeof l=="function"?l:dn)}function rb(e,t,a,l,s){if(Is(e))throw Error(o(485));if(e=t.action,e!==null){var c={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(p){c.listeners.push(p)}};L.T!==null?a(!0):c.isTransition=!1,l(c),a=t.pending,a===null?(c.next=t.pending=c,nm(t,c)):(c.next=a.next,t.pending=a.next=c)}}function nm(e,t){var a=t.action,l=t.payload,s=e.state;if(t.isTransition){var c=L.T,p={};L.T=p;try{var b=a(s,l),E=L.S;E!==null&&E(p,b),lm(e,t,b)}catch(H){bc(e,t,H)}finally{L.T=c}}else try{c=a(s,l),lm(e,t,c)}catch(H){bc(e,t,H)}}function lm(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){rm(e,t,l)},function(l){return bc(e,t,l)}):rm(e,t,a)}function rm(e,t,a){t.status="fulfilled",t.value=a,im(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,nm(e,a)))}function bc(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,im(t),t=t.next;while(t!==l)}e.action=null}function im(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function sm(e,t){return t}function om(e,t){if(Pe){var a=at.formState;if(a!==null){e:{var l=Ae;if(Pe){if(ft){t:{for(var s=ft,c=Ya;s.nodeType!==8;){if(!c){s=null;break t}if(s=_a(s.nextSibling),s===null){s=null;break t}}c=s.data,s=c==="F!"||c==="F"?s:null}if(s){ft=_a(s.nextSibling),l=s.data==="F!";break e}}Tl(l)}l=!1}l&&(t=a[0])}}return a=$t(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:sm,lastRenderedState:t},a.queue=l,a=Rm.bind(null,Ae,l),l.dispatch=a,l=xc(!1),c=jc.bind(null,Ae,!1,l.queue),l=$t(),s={state:t,dispatch:null,action:e,pending:null},l.queue=s,a=rb.bind(null,Ae,s,c,a),s.dispatch=a,l.memoizedState=e,[t,a,!1]}function um(e){var t=xt();return cm(t,Ie,e)}function cm(e,t,a){if(t=pc(e,t,sm)[0],e=Zs(dn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=ji(t)}catch(p){throw p===xi?Vs:p}else l=t;t=xt();var s=t.queue,c=s.dispatch;return a!==t.memoizedState&&(Ae.flags|=2048,gr(9,$s(),ib.bind(null,s,a),null)),[l,c,e]}function ib(e,t){e.action=t}function dm(e){var t=xt(),a=Ie;if(a!==null)return cm(t,a,e);xt(),t=t.memoizedState,a=xt();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function gr(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=Ae.updateQueue,t===null&&(t=vc(),Ae.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function $s(){return{destroy:void 0,resource:void 0}}function fm(){return xt().memoizedState}function Js(e,t,a,l){var s=$t();l=l===void 0?null:l,Ae.flags|=e,s.memoizedState=gr(1|t,$s(),a,l)}function Ti(e,t,a,l){var s=xt();l=l===void 0?null:l;var c=s.memoizedState.inst;Ie!==null&&l!==null&&cc(l,Ie.memoizedState.deps)?s.memoizedState=gr(t,c,a,l):(Ae.flags|=e,s.memoizedState=gr(1|t,c,a,l))}function hm(e,t){Js(8390656,8,e,t)}function mm(e,t){Ti(2048,8,e,t)}function vm(e,t){return Ti(4,2,e,t)}function gm(e,t){return Ti(4,4,e,t)}function pm(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ym(e,t,a){a=a!=null?a.concat([e]):null,Ti(4,4,pm.bind(null,t,e),a)}function wc(){}function xm(e,t){var a=xt();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&cc(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function bm(e,t){var a=xt();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&cc(t,l[1]))return l[0];if(l=e(),Cl){Aa(!0);try{e()}finally{Aa(!1)}}return a.memoizedState=[l,t],l}function Sc(e,t,a){return a===void 0||(_n&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=Nv(),Ae.lanes|=e,Gn|=e,a)}function wm(e,t,a,l){return ta(a,t)?a:hr.current!==null?(e=Sc(e,a,l),ta(e,t)||(jt=!0),e):(_n&42)===0?(jt=!0,e.memoizedState=a):(e=Nv(),Ae.lanes|=e,Gn|=e,t)}function Sm(e,t,a,l,s){var c=ae.p;ae.p=c!==0&&8>c?c:8;var p=L.T,b={};L.T=b,jc(e,!1,t,a);try{var E=s(),H=L.S;if(H!==null&&H(b,E),E!==null&&typeof E=="object"&&typeof E.then=="function"){var Q=ab(E,l);Ri(e,t,Q,ia(e))}else Ri(e,t,l,ia(e))}catch($){Ri(e,t,{then:function(){},status:"rejected",reason:$},ia())}finally{ae.p=c,L.T=p}}function sb(){}function Nc(e,t,a,l){if(e.tag!==5)throw Error(o(476));var s=Nm(e).queue;Sm(e,s,t,J,a===null?sb:function(){return Em(e),a(l)})}function Nm(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:J,baseState:J,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:dn,lastRenderedState:J},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:dn,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Em(e){var t=Nm(e).next.queue;Ri(e,t,{},ia())}function Ec(){return _t(Ki)}function jm(){return xt().memoizedState}function Tm(){return xt().memoizedState}function ob(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=ia();e=On(a);var l=zn(t,e,a);l!==null&&(sa(l,t,a),wi(l,t,a)),t={cache:ec()},e.payload=t;return}t=t.return}}function ub(e,t,a){var l=ia();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Is(e)?Mm(t,a):(a=Qu(e,t,a,l),a!==null&&(sa(a,e,l),Am(a,t,l)))}function Rm(e,t,a){var l=ia();Ri(e,t,a,l)}function Ri(e,t,a,l){var s={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Is(e))Mm(t,s);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var p=t.lastRenderedState,b=c(p,a);if(s.hasEagerState=!0,s.eagerState=b,ta(b,p))return ks(e,t,s,0),at===null&&_s(),!1}catch{}finally{}if(a=Qu(e,t,s,l),a!==null)return sa(a,e,l),Am(a,t,l),!0}return!1}function jc(e,t,a,l){if(l={lane:2,revertLane:nd(),action:l,hasEagerState:!1,eagerState:null,next:null},Is(e)){if(t)throw Error(o(479))}else t=Qu(e,a,l,2),t!==null&&sa(t,e,2)}function Is(e){var t=e.alternate;return e===Ae||t!==null&&t===Ae}function Mm(e,t){mr=Ps=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Am(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,pl(e,a)}}var Ws={readContext:_t,use:Fs,useCallback:vt,useContext:vt,useEffect:vt,useImperativeHandle:vt,useLayoutEffect:vt,useInsertionEffect:vt,useMemo:vt,useReducer:vt,useRef:vt,useState:vt,useDebugValue:vt,useDeferredValue:vt,useTransition:vt,useSyncExternalStore:vt,useId:vt,useHostTransitionStatus:vt,useFormState:vt,useActionState:vt,useOptimistic:vt,useMemoCache:vt,useCacheRefresh:vt},Cm={readContext:_t,use:Fs,useCallback:function(e,t){return $t().memoizedState=[e,t===void 0?null:t],e},useContext:_t,useEffect:hm,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Js(4194308,4,pm.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Js(4194308,4,e,t)},useInsertionEffect:function(e,t){Js(4,2,e,t)},useMemo:function(e,t){var a=$t();t=t===void 0?null:t;var l=e();if(Cl){Aa(!0);try{e()}finally{Aa(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=$t();if(a!==void 0){var s=a(t);if(Cl){Aa(!0);try{a(t)}finally{Aa(!1)}}}else s=t;return l.memoizedState=l.baseState=s,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:s},l.queue=e,e=e.dispatch=ub.bind(null,Ae,e),[l.memoizedState,e]},useRef:function(e){var t=$t();return e={current:e},t.memoizedState=e},useState:function(e){e=xc(e);var t=e.queue,a=Rm.bind(null,Ae,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:wc,useDeferredValue:function(e,t){var a=$t();return Sc(a,e,t)},useTransition:function(){var e=xc(!1);return e=Sm.bind(null,Ae,e.queue,!0,!1),$t().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=Ae,s=$t();if(Pe){if(a===void 0)throw Error(o(407));a=a()}else{if(a=t(),at===null)throw Error(o(349));(Be&124)!==0||Jh(l,t,a)}s.memoizedState=a;var c={value:a,getSnapshot:t};return s.queue=c,hm(Wh.bind(null,l,c,e),[e]),l.flags|=2048,gr(9,$s(),Ih.bind(null,l,c,a,t),null),a},useId:function(){var e=$t(),t=at.identifierPrefix;if(Pe){var a=on,l=sn;a=(l&~(1<<32-At(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Xs++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=nb++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ec,useFormState:om,useActionState:om,useOptimistic:function(e){var t=$t();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=jc.bind(null,Ae,!0,a),a.dispatch=t,[e,t]},useMemoCache:gc,useCacheRefresh:function(){return $t().memoizedState=ob.bind(null,Ae)}},Dm={readContext:_t,use:Fs,useCallback:xm,useContext:_t,useEffect:mm,useImperativeHandle:ym,useInsertionEffect:vm,useLayoutEffect:gm,useMemo:bm,useReducer:Zs,useRef:fm,useState:function(){return Zs(dn)},useDebugValue:wc,useDeferredValue:function(e,t){var a=xt();return wm(a,Ie.memoizedState,e,t)},useTransition:function(){var e=Zs(dn)[0],t=xt().memoizedState;return[typeof e=="boolean"?e:ji(e),t]},useSyncExternalStore:$h,useId:jm,useHostTransitionStatus:Ec,useFormState:um,useActionState:um,useOptimistic:function(e,t){var a=xt();return am(a,Ie,e,t)},useMemoCache:gc,useCacheRefresh:Tm},cb={readContext:_t,use:Fs,useCallback:xm,useContext:_t,useEffect:mm,useImperativeHandle:ym,useInsertionEffect:vm,useLayoutEffect:gm,useMemo:bm,useReducer:yc,useRef:fm,useState:function(){return yc(dn)},useDebugValue:wc,useDeferredValue:function(e,t){var a=xt();return Ie===null?Sc(a,e,t):wm(a,Ie.memoizedState,e,t)},useTransition:function(){var e=yc(dn)[0],t=xt().memoizedState;return[typeof e=="boolean"?e:ji(e),t]},useSyncExternalStore:$h,useId:jm,useHostTransitionStatus:Ec,useFormState:dm,useActionState:dm,useOptimistic:function(e,t){var a=xt();return Ie!==null?am(a,Ie,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:gc,useCacheRefresh:Tm},pr=null,Mi=0;function eo(e){var t=Mi;return Mi+=1,pr===null&&(pr=[]),Yh(pr,e,t)}function Ai(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function to(e,t){throw t.$$typeof===S?Error(o(525)):(e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Om(e){var t=e._init;return t(e._payload)}function zm(e){function t(z,D){if(e){var U=z.deletions;U===null?(z.deletions=[D],z.flags|=16):U.push(D)}}function a(z,D){if(!e)return null;for(;D!==null;)t(z,D),D=D.sibling;return null}function l(z){for(var D=new Map;z!==null;)z.key!==null?D.set(z.key,z):D.set(z.index,z),z=z.sibling;return D}function s(z,D){return z=rn(z,D),z.index=0,z.sibling=null,z}function c(z,D,U){return z.index=U,e?(U=z.alternate,U!==null?(U=U.index,U<D?(z.flags|=67108866,D):U):(z.flags|=67108866,D)):(z.flags|=1048576,D)}function p(z){return e&&z.alternate===null&&(z.flags|=67108866),z}function b(z,D,U,F){return D===null||D.tag!==6?(D=Pu(U,z.mode,F),D.return=z,D):(D=s(D,U),D.return=z,D)}function E(z,D,U,F){var se=U.type;return se===j?Q(z,D,U.props.children,F,U.key):D!==null&&(D.elementType===se||typeof se=="object"&&se!==null&&se.$$typeof===K&&Om(se)===D.type)?(D=s(D,U.props),Ai(D,U),D.return=z,D):(D=Us(U.type,U.key,U.props,null,z.mode,F),Ai(D,U),D.return=z,D)}function H(z,D,U,F){return D===null||D.tag!==4||D.stateNode.containerInfo!==U.containerInfo||D.stateNode.implementation!==U.implementation?(D=Xu(U,z.mode,F),D.return=z,D):(D=s(D,U.children||[]),D.return=z,D)}function Q(z,D,U,F,se){return D===null||D.tag!==7?(D=Sl(U,z.mode,F,se),D.return=z,D):(D=s(D,U),D.return=z,D)}function $(z,D,U){if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return D=Pu(""+D,z.mode,U),D.return=z,D;if(typeof D=="object"&&D!==null){switch(D.$$typeof){case w:return U=Us(D.type,D.key,D.props,null,z.mode,U),Ai(U,D),U.return=z,U;case T:return D=Xu(D,z.mode,U),D.return=z,D;case K:var F=D._init;return D=F(D._payload),$(z,D,U)}if(Ne(D)||qe(D))return D=Sl(D,z.mode,U,null),D.return=z,D;if(typeof D.then=="function")return $(z,eo(D),U);if(D.$$typeof===_)return $(z,Gs(z,D),U);to(z,D)}return null}function B(z,D,U,F){var se=D!==null?D.key:null;if(typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint")return se!==null?null:b(z,D,""+U,F);if(typeof U=="object"&&U!==null){switch(U.$$typeof){case w:return U.key===se?E(z,D,U,F):null;case T:return U.key===se?H(z,D,U,F):null;case K:return se=U._init,U=se(U._payload),B(z,D,U,F)}if(Ne(U)||qe(U))return se!==null?null:Q(z,D,U,F,null);if(typeof U.then=="function")return B(z,D,eo(U),F);if(U.$$typeof===_)return B(z,D,Gs(z,U),F);to(z,U)}return null}function G(z,D,U,F,se){if(typeof F=="string"&&F!==""||typeof F=="number"||typeof F=="bigint")return z=z.get(U)||null,b(D,z,""+F,se);if(typeof F=="object"&&F!==null){switch(F.$$typeof){case w:return z=z.get(F.key===null?U:F.key)||null,E(D,z,F,se);case T:return z=z.get(F.key===null?U:F.key)||null,H(D,z,F,se);case K:var Oe=F._init;return F=Oe(F._payload),G(z,D,U,F,se)}if(Ne(F)||qe(F))return z=z.get(U)||null,Q(D,z,F,se,null);if(typeof F.then=="function")return G(z,D,U,eo(F),se);if(F.$$typeof===_)return G(z,D,U,Gs(D,F),se);to(D,F)}return null}function we(z,D,U,F){for(var se=null,Oe=null,fe=D,xe=D=0,Rt=null;fe!==null&&xe<U.length;xe++){fe.index>xe?(Rt=fe,fe=null):Rt=fe.sibling;var Ve=B(z,fe,U[xe],F);if(Ve===null){fe===null&&(fe=Rt);break}e&&fe&&Ve.alternate===null&&t(z,fe),D=c(Ve,D,xe),Oe===null?se=Ve:Oe.sibling=Ve,Oe=Ve,fe=Rt}if(xe===U.length)return a(z,fe),Pe&&El(z,xe),se;if(fe===null){for(;xe<U.length;xe++)fe=$(z,U[xe],F),fe!==null&&(D=c(fe,D,xe),Oe===null?se=fe:Oe.sibling=fe,Oe=fe);return Pe&&El(z,xe),se}for(fe=l(fe);xe<U.length;xe++)Rt=G(fe,z,xe,U[xe],F),Rt!==null&&(e&&Rt.alternate!==null&&fe.delete(Rt.key===null?xe:Rt.key),D=c(Rt,D,xe),Oe===null?se=Rt:Oe.sibling=Rt,Oe=Rt);return e&&fe.forEach(function($n){return t(z,$n)}),Pe&&El(z,xe),se}function ye(z,D,U,F){if(U==null)throw Error(o(151));for(var se=null,Oe=null,fe=D,xe=D=0,Rt=null,Ve=U.next();fe!==null&&!Ve.done;xe++,Ve=U.next()){fe.index>xe?(Rt=fe,fe=null):Rt=fe.sibling;var $n=B(z,fe,Ve.value,F);if($n===null){fe===null&&(fe=Rt);break}e&&fe&&$n.alternate===null&&t(z,fe),D=c($n,D,xe),Oe===null?se=$n:Oe.sibling=$n,Oe=$n,fe=Rt}if(Ve.done)return a(z,fe),Pe&&El(z,xe),se;if(fe===null){for(;!Ve.done;xe++,Ve=U.next())Ve=$(z,Ve.value,F),Ve!==null&&(D=c(Ve,D,xe),Oe===null?se=Ve:Oe.sibling=Ve,Oe=Ve);return Pe&&El(z,xe),se}for(fe=l(fe);!Ve.done;xe++,Ve=U.next())Ve=G(fe,z,xe,Ve.value,F),Ve!==null&&(e&&Ve.alternate!==null&&fe.delete(Ve.key===null?xe:Ve.key),D=c(Ve,D,xe),Oe===null?se=Ve:Oe.sibling=Ve,Oe=Ve);return e&&fe.forEach(function(d1){return t(z,d1)}),Pe&&El(z,xe),se}function et(z,D,U,F){if(typeof U=="object"&&U!==null&&U.type===j&&U.key===null&&(U=U.props.children),typeof U=="object"&&U!==null){switch(U.$$typeof){case w:e:{for(var se=U.key;D!==null;){if(D.key===se){if(se=U.type,se===j){if(D.tag===7){a(z,D.sibling),F=s(D,U.props.children),F.return=z,z=F;break e}}else if(D.elementType===se||typeof se=="object"&&se!==null&&se.$$typeof===K&&Om(se)===D.type){a(z,D.sibling),F=s(D,U.props),Ai(F,U),F.return=z,z=F;break e}a(z,D);break}else t(z,D);D=D.sibling}U.type===j?(F=Sl(U.props.children,z.mode,F,U.key),F.return=z,z=F):(F=Us(U.type,U.key,U.props,null,z.mode,F),Ai(F,U),F.return=z,z=F)}return p(z);case T:e:{for(se=U.key;D!==null;){if(D.key===se)if(D.tag===4&&D.stateNode.containerInfo===U.containerInfo&&D.stateNode.implementation===U.implementation){a(z,D.sibling),F=s(D,U.children||[]),F.return=z,z=F;break e}else{a(z,D);break}else t(z,D);D=D.sibling}F=Xu(U,z.mode,F),F.return=z,z=F}return p(z);case K:return se=U._init,U=se(U._payload),et(z,D,U,F)}if(Ne(U))return we(z,D,U,F);if(qe(U)){if(se=qe(U),typeof se!="function")throw Error(o(150));return U=se.call(U),ye(z,D,U,F)}if(typeof U.then=="function")return et(z,D,eo(U),F);if(U.$$typeof===_)return et(z,D,Gs(z,U),F);to(z,U)}return typeof U=="string"&&U!==""||typeof U=="number"||typeof U=="bigint"?(U=""+U,D!==null&&D.tag===6?(a(z,D.sibling),F=s(D,U),F.return=z,z=F):(a(z,D),F=Pu(U,z.mode,F),F.return=z,z=F),p(z)):a(z,D)}return function(z,D,U,F){try{Mi=0;var se=et(z,D,U,F);return pr=null,se}catch(fe){if(fe===xi||fe===Vs)throw fe;var Oe=aa(29,fe,null,z.mode);return Oe.lanes=F,Oe.return=z,Oe}finally{}}}var yr=zm(!0),_m=zm(!1),pa=X(null),Va=null;function kn(e){var t=e.alternate;ee(St,St.current&1),ee(pa,e),Va===null&&(t===null||hr.current!==null||t.memoizedState!==null)&&(Va=e)}function km(e){if(e.tag===22){if(ee(St,St.current),ee(pa,e),Va===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Va=e)}}else Ln()}function Ln(){ee(St,St.current),ee(pa,pa.current)}function fn(e){le(pa),Va===e&&(Va=null),le(St)}var St=X(0);function ao(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||vd(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Tc(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:y({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var Rc={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=ia(),s=On(l);s.payload=t,a!=null&&(s.callback=a),t=zn(e,s,l),t!==null&&(sa(t,e,l),wi(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=ia(),s=On(l);s.tag=1,s.payload=t,a!=null&&(s.callback=a),t=zn(e,s,l),t!==null&&(sa(t,e,l),wi(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=ia(),l=On(a);l.tag=2,t!=null&&(l.callback=t),t=zn(e,l,a),t!==null&&(sa(t,e,a),wi(t,e,a))}};function Lm(e,t,a,l,s,c,p){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,c,p):t.prototype&&t.prototype.isPureReactComponent?!di(a,l)||!di(s,c):!0}function Um(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&Rc.enqueueReplaceState(t,t.state,null)}function Dl(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=y({},a));for(var s in e)a[s]===void 0&&(a[s]=e[s])}return a}var no=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Hm(e){no(e)}function Bm(e){console.error(e)}function qm(e){no(e)}function lo(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Gm(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function Mc(e,t,a){return a=On(a),a.tag=3,a.payload={element:null},a.callback=function(){lo(e,t)},a}function Ym(e){return e=On(e),e.tag=3,e}function Vm(e,t,a,l){var s=a.type.getDerivedStateFromError;if(typeof s=="function"){var c=l.value;e.payload=function(){return s(c)},e.callback=function(){Gm(t,a,l)}}var p=a.stateNode;p!==null&&typeof p.componentDidCatch=="function"&&(e.callback=function(){Gm(t,a,l),typeof s!="function"&&(Yn===null?Yn=new Set([this]):Yn.add(this));var b=l.stack;this.componentDidCatch(l.value,{componentStack:b!==null?b:""})})}function db(e,t,a,l,s){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&gi(t,a,s,!0),a=pa.current,a!==null){switch(a.tag){case 13:return Va===null?Ic():a.alternate===null&&ht===0&&(ht=3),a.flags&=-257,a.flags|=65536,a.lanes=s,l===nc?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),ed(e,l,s)),!1;case 22:return a.flags|=65536,l===nc?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),ed(e,l,s)),!1}throw Error(o(435,a.tag))}return ed(e,l,s),Ic(),!1}if(Pe)return t=pa.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=s,l!==$u&&(e=Error(o(422),{cause:l}),vi(ha(e,a)))):(l!==$u&&(t=Error(o(423),{cause:l}),vi(ha(t,a))),e=e.current.alternate,e.flags|=65536,s&=-s,e.lanes|=s,l=ha(l,a),s=Mc(e.stateNode,l,s),ic(e,s),ht!==4&&(ht=2)),!1;var c=Error(o(520),{cause:l});if(c=ha(c,a),Li===null?Li=[c]:Li.push(c),ht!==4&&(ht=2),t===null)return!0;l=ha(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=s&-s,a.lanes|=e,e=Mc(a.stateNode,l,e),ic(a,e),!1;case 1:if(t=a.type,c=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(Yn===null||!Yn.has(c))))return a.flags|=65536,s&=-s,a.lanes|=s,s=Ym(s),Vm(s,e,a,l),ic(a,s),!1}a=a.return}while(a!==null);return!1}var Qm=Error(o(461)),jt=!1;function Ct(e,t,a,l){t.child=e===null?_m(t,null,a,l):yr(t,e.child,a,l)}function Km(e,t,a,l,s){a=a.render;var c=t.ref;if("ref"in l){var p={};for(var b in l)b!=="ref"&&(p[b]=l[b])}else p=l;return Ml(t),l=dc(e,t,a,p,c,s),b=fc(),e!==null&&!jt?(hc(e,t,s),hn(e,t,s)):(Pe&&b&&Fu(t),t.flags|=1,Ct(e,t,l,s),t.child)}function Pm(e,t,a,l,s){if(e===null){var c=a.type;return typeof c=="function"&&!Ku(c)&&c.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=c,Xm(e,t,c,l,s)):(e=Us(a.type,null,l,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!Lc(e,s)){var p=c.memoizedProps;if(a=a.compare,a=a!==null?a:di,a(p,l)&&e.ref===t.ref)return hn(e,t,s)}return t.flags|=1,e=rn(c,l),e.ref=t.ref,e.return=t,t.child=e}function Xm(e,t,a,l,s){if(e!==null){var c=e.memoizedProps;if(di(c,l)&&e.ref===t.ref)if(jt=!1,t.pendingProps=l=c,Lc(e,s))(e.flags&131072)!==0&&(jt=!0);else return t.lanes=e.lanes,hn(e,t,s)}return Ac(e,t,a,l,s)}function Fm(e,t,a){var l=t.pendingProps,s=l.children,c=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=c!==null?c.baseLanes|a:a,e!==null){for(s=t.child=e.child,c=0;s!==null;)c=c|s.lanes|s.childLanes,s=s.sibling;t.childLanes=c&~l}else t.childLanes=0,t.child=null;return Zm(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ys(t,c!==null?c.cachePool:null),c!==null?Xh(t,c):oc(),km(t);else return t.lanes=t.childLanes=536870912,Zm(e,t,c!==null?c.baseLanes|a:a,a)}else c!==null?(Ys(t,c.cachePool),Xh(t,c),Ln(),t.memoizedState=null):(e!==null&&Ys(t,null),oc(),Ln());return Ct(e,t,s,a),t.child}function Zm(e,t,a,l){var s=ac();return s=s===null?null:{parent:wt._currentValue,pool:s},t.memoizedState={baseLanes:a,cachePool:s},e!==null&&Ys(t,null),oc(),km(t),e!==null&&gi(e,t,l,!0),null}function ro(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(o(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function Ac(e,t,a,l,s){return Ml(t),a=dc(e,t,a,l,void 0,s),l=fc(),e!==null&&!jt?(hc(e,t,s),hn(e,t,s)):(Pe&&l&&Fu(t),t.flags|=1,Ct(e,t,a,s),t.child)}function $m(e,t,a,l,s,c){return Ml(t),t.updateQueue=null,a=Zh(t,l,a,s),Fh(e),l=fc(),e!==null&&!jt?(hc(e,t,c),hn(e,t,c)):(Pe&&l&&Fu(t),t.flags|=1,Ct(e,t,a,c),t.child)}function Jm(e,t,a,l,s){if(Ml(t),t.stateNode===null){var c=or,p=a.contextType;typeof p=="object"&&p!==null&&(c=_t(p)),c=new a(l,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=Rc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=l,c.state=t.memoizedState,c.refs={},lc(t),p=a.contextType,c.context=typeof p=="object"&&p!==null?_t(p):or,c.state=t.memoizedState,p=a.getDerivedStateFromProps,typeof p=="function"&&(Tc(t,a,p,l),c.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(p=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),p!==c.state&&Rc.enqueueReplaceState(c,c.state,null),Ni(t,l,c,s),Si(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){c=t.stateNode;var b=t.memoizedProps,E=Dl(a,b);c.props=E;var H=c.context,Q=a.contextType;p=or,typeof Q=="object"&&Q!==null&&(p=_t(Q));var $=a.getDerivedStateFromProps;Q=typeof $=="function"||typeof c.getSnapshotBeforeUpdate=="function",b=t.pendingProps!==b,Q||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(b||H!==p)&&Um(t,c,l,p),Dn=!1;var B=t.memoizedState;c.state=B,Ni(t,l,c,s),Si(),H=t.memoizedState,b||B!==H||Dn?(typeof $=="function"&&(Tc(t,a,$,l),H=t.memoizedState),(E=Dn||Lm(t,a,E,l,B,H,p))?(Q||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=H),c.props=l,c.state=H,c.context=p,l=E):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{c=t.stateNode,rc(e,t),p=t.memoizedProps,Q=Dl(a,p),c.props=Q,$=t.pendingProps,B=c.context,H=a.contextType,E=or,typeof H=="object"&&H!==null&&(E=_t(H)),b=a.getDerivedStateFromProps,(H=typeof b=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(p!==$||B!==E)&&Um(t,c,l,E),Dn=!1,B=t.memoizedState,c.state=B,Ni(t,l,c,s),Si();var G=t.memoizedState;p!==$||B!==G||Dn||e!==null&&e.dependencies!==null&&qs(e.dependencies)?(typeof b=="function"&&(Tc(t,a,b,l),G=t.memoizedState),(Q=Dn||Lm(t,a,Q,l,B,G,E)||e!==null&&e.dependencies!==null&&qs(e.dependencies))?(H||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,G,E),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,G,E)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||p===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||p===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=G),c.props=l,c.state=G,c.context=E,l=Q):(typeof c.componentDidUpdate!="function"||p===e.memoizedProps&&B===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||p===e.memoizedProps&&B===e.memoizedState||(t.flags|=1024),l=!1)}return c=l,ro(e,t),l=(t.flags&128)!==0,c||l?(c=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&l?(t.child=yr(t,e.child,null,s),t.child=yr(t,null,a,s)):Ct(e,t,a,s),t.memoizedState=c.state,e=t.child):e=hn(e,t,s),e}function Im(e,t,a,l){return mi(),t.flags|=256,Ct(e,t,a,l),t.child}var Cc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Dc(e){return{baseLanes:e,cachePool:Bh()}}function Oc(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=ya),e}function Wm(e,t,a){var l=t.pendingProps,s=!1,c=(t.flags&128)!==0,p;if((p=c)||(p=e!==null&&e.memoizedState===null?!1:(St.current&2)!==0),p&&(s=!0,t.flags&=-129),p=(t.flags&32)!==0,t.flags&=-33,e===null){if(Pe){if(s?kn(t):Ln(),Pe){var b=ft,E;if(E=b){e:{for(E=b,b=Ya;E.nodeType!==8;){if(!b){b=null;break e}if(E=_a(E.nextSibling),E===null){b=null;break e}}b=E}b!==null?(t.memoizedState={dehydrated:b,treeContext:Nl!==null?{id:sn,overflow:on}:null,retryLane:536870912,hydrationErrors:null},E=aa(18,null,null,0),E.stateNode=b,E.return=t,t.child=E,Gt=t,ft=null,E=!0):E=!1}E||Tl(t)}if(b=t.memoizedState,b!==null&&(b=b.dehydrated,b!==null))return vd(b)?t.lanes=32:t.lanes=536870912,null;fn(t)}return b=l.children,l=l.fallback,s?(Ln(),s=t.mode,b=io({mode:"hidden",children:b},s),l=Sl(l,s,a,null),b.return=t,l.return=t,b.sibling=l,t.child=b,s=t.child,s.memoizedState=Dc(a),s.childLanes=Oc(e,p,a),t.memoizedState=Cc,l):(kn(t),zc(t,b))}if(E=e.memoizedState,E!==null&&(b=E.dehydrated,b!==null)){if(c)t.flags&256?(kn(t),t.flags&=-257,t=_c(e,t,a)):t.memoizedState!==null?(Ln(),t.child=e.child,t.flags|=128,t=null):(Ln(),s=l.fallback,b=t.mode,l=io({mode:"visible",children:l.children},b),s=Sl(s,b,a,null),s.flags|=2,l.return=t,s.return=t,l.sibling=s,t.child=l,yr(t,e.child,null,a),l=t.child,l.memoizedState=Dc(a),l.childLanes=Oc(e,p,a),t.memoizedState=Cc,t=s);else if(kn(t),vd(b)){if(p=b.nextSibling&&b.nextSibling.dataset,p)var H=p.dgst;p=H,l=Error(o(419)),l.stack="",l.digest=p,vi({value:l,source:null,stack:null}),t=_c(e,t,a)}else if(jt||gi(e,t,a,!1),p=(a&e.childLanes)!==0,jt||p){if(p=at,p!==null&&(l=a&-a,l=(l&42)!==0?1:ei(l),l=(l&(p.suspendedLanes|a))!==0?0:l,l!==0&&l!==E.retryLane))throw E.retryLane=l,sr(e,l),sa(p,e,l),Qm;b.data==="$?"||Ic(),t=_c(e,t,a)}else b.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=E.treeContext,ft=_a(b.nextSibling),Gt=t,Pe=!0,jl=null,Ya=!1,e!==null&&(va[ga++]=sn,va[ga++]=on,va[ga++]=Nl,sn=e.id,on=e.overflow,Nl=t),t=zc(t,l.children),t.flags|=4096);return t}return s?(Ln(),s=l.fallback,b=t.mode,E=e.child,H=E.sibling,l=rn(E,{mode:"hidden",children:l.children}),l.subtreeFlags=E.subtreeFlags&65011712,H!==null?s=rn(H,s):(s=Sl(s,b,a,null),s.flags|=2),s.return=t,l.return=t,l.sibling=s,t.child=l,l=s,s=t.child,b=e.child.memoizedState,b===null?b=Dc(a):(E=b.cachePool,E!==null?(H=wt._currentValue,E=E.parent!==H?{parent:H,pool:H}:E):E=Bh(),b={baseLanes:b.baseLanes|a,cachePool:E}),s.memoizedState=b,s.childLanes=Oc(e,p,a),t.memoizedState=Cc,l):(kn(t),a=e.child,e=a.sibling,a=rn(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(p=t.deletions,p===null?(t.deletions=[e],t.flags|=16):p.push(e)),t.child=a,t.memoizedState=null,a)}function zc(e,t){return t=io({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function io(e,t){return e=aa(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function _c(e,t,a){return yr(t,e.child,null,a),e=zc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ev(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Iu(e.return,t,a)}function kc(e,t,a,l,s){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:s}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=a,c.tailMode=s)}function tv(e,t,a){var l=t.pendingProps,s=l.revealOrder,c=l.tail;if(Ct(e,t,l.children,a),l=St.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ev(e,a,t);else if(e.tag===19)ev(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(ee(St,l),s){case"forwards":for(a=t.child,s=null;a!==null;)e=a.alternate,e!==null&&ao(e)===null&&(s=a),a=a.sibling;a=s,a===null?(s=t.child,t.child=null):(s=a.sibling,a.sibling=null),kc(t,!1,s,a,c);break;case"backwards":for(a=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&ao(e)===null){t.child=s;break}e=s.sibling,s.sibling=a,a=s,s=e}kc(t,!0,a,null,c);break;case"together":kc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function hn(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Gn|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(gi(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,a=rn(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=rn(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function Lc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&qs(e)))}function fb(e,t,a){switch(t.tag){case 3:Ze(t,t.stateNode.containerInfo),Cn(t,wt,e.memoizedState.cache),mi();break;case 27:case 5:it(t);break;case 4:Ze(t,t.stateNode.containerInfo);break;case 10:Cn(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(kn(t),t.flags|=128,null):(a&t.child.childLanes)!==0?Wm(e,t,a):(kn(t),e=hn(e,t,a),e!==null?e.sibling:null);kn(t);break;case 19:var s=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(gi(e,t,a,!1),l=(a&t.childLanes)!==0),s){if(l)return tv(e,t,a);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),ee(St,St.current),l)break;return null;case 22:case 23:return t.lanes=0,Fm(e,t,a);case 24:Cn(t,wt,e.memoizedState.cache)}return hn(e,t,a)}function av(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)jt=!0;else{if(!Lc(e,a)&&(t.flags&128)===0)return jt=!1,fb(e,t,a);jt=(e.flags&131072)!==0}else jt=!1,Pe&&(t.flags&1048576)!==0&&Oh(t,Bs,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,s=l._init;if(l=s(l._payload),t.type=l,typeof l=="function")Ku(l)?(e=Dl(l,e),t.tag=1,t=Jm(null,t,l,e,a)):(t.tag=0,t=Ac(null,t,l,e,a));else{if(l!=null){if(s=l.$$typeof,s===V){t.tag=11,t=Km(null,t,l,e,a);break e}else if(s===te){t.tag=14,t=Pm(null,t,l,e,a);break e}}throw t=Me(l)||l,Error(o(306,t,""))}}return t;case 0:return Ac(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,s=Dl(l,t.pendingProps),Jm(e,t,l,s,a);case 3:e:{if(Ze(t,t.stateNode.containerInfo),e===null)throw Error(o(387));l=t.pendingProps;var c=t.memoizedState;s=c.element,rc(e,t),Ni(t,l,null,a);var p=t.memoizedState;if(l=p.cache,Cn(t,wt,l),l!==c.cache&&Wu(t,[wt],a,!0),Si(),l=p.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:p.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=Im(e,t,l,a);break e}else if(l!==s){s=ha(Error(o(424)),t),vi(s),t=Im(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(ft=_a(e.firstChild),Gt=t,Pe=!0,jl=null,Ya=!0,a=_m(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(mi(),l===s){t=hn(e,t,a);break e}Ct(e,t,l,a)}t=t.child}return t;case 26:return ro(e,t),e===null?(a=i0(t.type,null,t.pendingProps,null))?t.memoizedState=a:Pe||(a=t.type,e=t.pendingProps,l=wo(he.current).createElement(a),l[I]=t,l[ne]=e,Ot(l,a,e),Ye(l),t.stateNode=l):t.memoizedState=i0(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return it(t),e===null&&Pe&&(l=t.stateNode=n0(t.type,t.pendingProps,he.current),Gt=t,Ya=!0,s=ft,Kn(t.type)?(gd=s,ft=_a(l.firstChild)):ft=s),Ct(e,t,t.pendingProps.children,a),ro(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Pe&&((s=l=ft)&&(l=qb(l,t.type,t.pendingProps,Ya),l!==null?(t.stateNode=l,Gt=t,ft=_a(l.firstChild),Ya=!1,s=!0):s=!1),s||Tl(t)),it(t),s=t.type,c=t.pendingProps,p=e!==null?e.memoizedProps:null,l=c.children,fd(s,c)?l=null:p!==null&&fd(s,p)&&(t.flags|=32),t.memoizedState!==null&&(s=dc(e,t,lb,null,null,a),Ki._currentValue=s),ro(e,t),Ct(e,t,l,a),t.child;case 6:return e===null&&Pe&&((e=a=ft)&&(a=Gb(a,t.pendingProps,Ya),a!==null?(t.stateNode=a,Gt=t,ft=null,e=!0):e=!1),e||Tl(t)),null;case 13:return Wm(e,t,a);case 4:return Ze(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=yr(t,null,l,a):Ct(e,t,l,a),t.child;case 11:return Km(e,t,t.type,t.pendingProps,a);case 7:return Ct(e,t,t.pendingProps,a),t.child;case 8:return Ct(e,t,t.pendingProps.children,a),t.child;case 12:return Ct(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,Cn(t,t.type,l.value),Ct(e,t,l.children,a),t.child;case 9:return s=t.type._context,l=t.pendingProps.children,Ml(t),s=_t(s),l=l(s),t.flags|=1,Ct(e,t,l,a),t.child;case 14:return Pm(e,t,t.type,t.pendingProps,a);case 15:return Xm(e,t,t.type,t.pendingProps,a);case 19:return tv(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=io(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=rn(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return Fm(e,t,a);case 24:return Ml(t),l=_t(wt),e===null?(s=ac(),s===null&&(s=at,c=ec(),s.pooledCache=c,c.refCount++,c!==null&&(s.pooledCacheLanes|=a),s=c),t.memoizedState={parent:l,cache:s},lc(t),Cn(t,wt,s)):((e.lanes&a)!==0&&(rc(e,t),Ni(t,null,null,a),Si()),s=e.memoizedState,c=t.memoizedState,s.parent!==l?(s={parent:l,cache:l},t.memoizedState=s,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=s),Cn(t,wt,l)):(l=c.cache,Cn(t,wt,l),l!==s.cache&&Wu(t,[wt],a,!0))),Ct(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function mn(e){e.flags|=4}function nv(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!d0(t)){if(t=pa.current,t!==null&&((Be&4194048)===Be?Va!==null:(Be&62914560)!==Be&&(Be&536870912)===0||t!==Va))throw bi=nc,qh;e.flags|=8192}}function so(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Ns():536870912,e.lanes|=t,Sr|=t)}function Ci(e,t){if(!Pe)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function dt(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var s=e.child;s!==null;)a|=s.lanes|s.childLanes,l|=s.subtreeFlags&65011712,l|=s.flags&65011712,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)a|=s.lanes|s.childLanes,l|=s.subtreeFlags,l|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function hb(e,t,a){var l=t.pendingProps;switch(Zu(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return dt(t),null;case 1:return dt(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),cn(wt),Ht(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(hi(t)?mn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,kh())),dt(t),null;case 26:return a=t.memoizedState,e===null?(mn(t),a!==null?(dt(t),nv(t,a)):(dt(t),t.flags&=-16777217)):a?a!==e.memoizedState?(mn(t),dt(t),nv(t,a)):(dt(t),t.flags&=-16777217):(e.memoizedProps!==l&&mn(t),dt(t),t.flags&=-16777217),null;case 27:Bt(t),a=he.current;var s=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&mn(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return dt(t),null}e=re.current,hi(t)?zh(t):(e=n0(s,l,a),t.stateNode=e,mn(t))}return dt(t),null;case 5:if(Bt(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&mn(t);else{if(!l){if(t.stateNode===null)throw Error(o(166));return dt(t),null}if(e=re.current,hi(t))zh(t);else{switch(s=wo(he.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?s.createElement("select",{is:l.is}):s.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?s.createElement(a,{is:l.is}):s.createElement(a)}}e[I]=t,e[ne]=l;e:for(s=t.child;s!==null;){if(s.tag===5||s.tag===6)e.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===t)break e;for(;s.sibling===null;){if(s.return===null||s.return===t)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}t.stateNode=e;e:switch(Ot(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&mn(t)}}return dt(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&mn(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(o(166));if(e=he.current,hi(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,s=Gt,s!==null)switch(s.tag){case 27:case 5:l=s.memoizedProps}e[I]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||$v(e.nodeValue,a)),e||Tl(t)}else e=wo(e).createTextNode(l),e[I]=t,t.stateNode=e}return dt(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(s=hi(t),l!==null&&l.dehydrated!==null){if(e===null){if(!s)throw Error(o(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(o(317));s[I]=t}else mi(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;dt(t),s=!1}else s=kh(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return t.flags&256?(fn(t),t):(fn(t),null)}if(fn(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,s=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(s=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==s&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),so(t,t.updateQueue),dt(t),null;case 4:return Ht(),e===null&&sd(t.stateNode.containerInfo),dt(t),null;case 10:return cn(t.type),dt(t),null;case 19:if(le(St),s=t.memoizedState,s===null)return dt(t),null;if(l=(t.flags&128)!==0,c=s.rendering,c===null)if(l)Ci(s,!1);else{if(ht!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=ao(e),c!==null){for(t.flags|=128,Ci(s,!1),e=c.updateQueue,t.updateQueue=e,so(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Dh(a,e),a=a.sibling;return ee(St,St.current&1|2),t.child}e=e.sibling}s.tail!==null&&ea()>co&&(t.flags|=128,l=!0,Ci(s,!1),t.lanes=4194304)}else{if(!l)if(e=ao(c),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,so(t,e),Ci(s,!0),s.tail===null&&s.tailMode==="hidden"&&!c.alternate&&!Pe)return dt(t),null}else 2*ea()-s.renderingStartTime>co&&a!==536870912&&(t.flags|=128,l=!0,Ci(s,!1),t.lanes=4194304);s.isBackwards?(c.sibling=t.child,t.child=c):(e=s.last,e!==null?e.sibling=c:t.child=c,s.last=c)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=ea(),t.sibling=null,e=St.current,ee(St,l?e&1|2:e&1),t):(dt(t),null);case 22:case 23:return fn(t),uc(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(dt(t),t.subtreeFlags&6&&(t.flags|=8192)):dt(t),a=t.updateQueue,a!==null&&so(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&le(Al),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),cn(wt),dt(t),null;case 25:return null;case 30:return null}throw Error(o(156,t.tag))}function mb(e,t){switch(Zu(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return cn(wt),Ht(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Bt(t),null;case 13:if(fn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));mi()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return le(St),null;case 4:return Ht(),null;case 10:return cn(t.type),null;case 22:case 23:return fn(t),uc(),e!==null&&le(Al),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return cn(wt),null;case 25:return null;default:return null}}function lv(e,t){switch(Zu(t),t.tag){case 3:cn(wt),Ht();break;case 26:case 27:case 5:Bt(t);break;case 4:Ht();break;case 13:fn(t);break;case 19:le(St);break;case 10:cn(t.type);break;case 22:case 23:fn(t),uc(),e!==null&&le(Al);break;case 24:cn(wt)}}function Di(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var s=l.next;a=s;do{if((a.tag&e)===e){l=void 0;var c=a.create,p=a.inst;l=c(),p.destroy=l}a=a.next}while(a!==s)}}catch(b){tt(t,t.return,b)}}function Un(e,t,a){try{var l=t.updateQueue,s=l!==null?l.lastEffect:null;if(s!==null){var c=s.next;l=c;do{if((l.tag&e)===e){var p=l.inst,b=p.destroy;if(b!==void 0){p.destroy=void 0,s=t;var E=a,H=b;try{H()}catch(Q){tt(s,E,Q)}}}l=l.next}while(l!==c)}}catch(Q){tt(t,t.return,Q)}}function rv(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Ph(t,a)}catch(l){tt(e,e.return,l)}}}function iv(e,t,a){a.props=Dl(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){tt(e,t,l)}}function Oi(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(s){tt(e,t,s)}}function Qa(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(s){tt(e,t,s)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(s){tt(e,t,s)}else a.current=null}function sv(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(s){tt(e,e.return,s)}}function Uc(e,t,a){try{var l=e.stateNode;kb(l,e.type,a,t),l[ne]=t}catch(s){tt(e,e.return,s)}}function ov(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Kn(e.type)||e.tag===4}function Hc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ov(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Kn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Bc(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=bo));else if(l!==4&&(l===27&&Kn(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(Bc(e,t,a),e=e.sibling;e!==null;)Bc(e,t,a),e=e.sibling}function oo(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Kn(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(oo(e,t,a),e=e.sibling;e!==null;)oo(e,t,a),e=e.sibling}function uv(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,s=t.attributes;s.length;)t.removeAttributeNode(s[0]);Ot(t,l,a),t[I]=e,t[ne]=a}catch(c){tt(e,e.return,c)}}var vn=!1,gt=!1,qc=!1,cv=typeof WeakSet=="function"?WeakSet:Set,Tt=null;function vb(e,t){if(e=e.containerInfo,cd=Ro,e=wh(e),Hu(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var s=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{a.nodeType,c.nodeType}catch{a=null;break e}var p=0,b=-1,E=-1,H=0,Q=0,$=e,B=null;t:for(;;){for(var G;$!==a||s!==0&&$.nodeType!==3||(b=p+s),$!==c||l!==0&&$.nodeType!==3||(E=p+l),$.nodeType===3&&(p+=$.nodeValue.length),(G=$.firstChild)!==null;)B=$,$=G;for(;;){if($===e)break t;if(B===a&&++H===s&&(b=p),B===c&&++Q===l&&(E=p),(G=$.nextSibling)!==null)break;$=B,B=$.parentNode}$=G}a=b===-1||E===-1?null:{start:b,end:E}}else a=null}a=a||{start:0,end:0}}else a=null;for(dd={focusedElem:e,selectionRange:a},Ro=!1,Tt=t;Tt!==null;)if(t=Tt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Tt=e;else for(;Tt!==null;){switch(t=Tt,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,a=t,s=c.memoizedProps,c=c.memoizedState,l=a.stateNode;try{var we=Dl(a.type,s,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(we,c),l.__reactInternalSnapshotBeforeUpdate=e}catch(ye){tt(a,a.return,ye)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)md(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":md(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(o(163))}if(e=t.sibling,e!==null){e.return=t.return,Tt=e;break}Tt=t.return}}function dv(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:Hn(e,a),l&4&&Di(5,a);break;case 1:if(Hn(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(p){tt(a,a.return,p)}else{var s=Dl(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(s,t,e.__reactInternalSnapshotBeforeUpdate)}catch(p){tt(a,a.return,p)}}l&64&&rv(a),l&512&&Oi(a,a.return);break;case 3:if(Hn(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Ph(e,t)}catch(p){tt(a,a.return,p)}}break;case 27:t===null&&l&4&&uv(a);case 26:case 5:Hn(e,a),t===null&&l&4&&sv(a),l&512&&Oi(a,a.return);break;case 12:Hn(e,a);break;case 13:Hn(e,a),l&4&&mv(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Eb.bind(null,a),Yb(e,a))));break;case 22:if(l=a.memoizedState!==null||vn,!l){t=t!==null&&t.memoizedState!==null||gt,s=vn;var c=gt;vn=l,(gt=t)&&!c?Bn(e,a,(a.subtreeFlags&8772)!==0):Hn(e,a),vn=s,gt=c}break;case 30:break;default:Hn(e,a)}}function fv(e){var t=e.alternate;t!==null&&(e.alternate=null,fv(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&de(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ot=null,Jt=!1;function gn(e,t,a){for(a=a.child;a!==null;)hv(e,t,a),a=a.sibling}function hv(e,t,a){if(ut&&typeof ut.onCommitFiberUnmount=="function")try{ut.onCommitFiberUnmount(Pt,a)}catch{}switch(a.tag){case 26:gt||Qa(a,t),gn(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:gt||Qa(a,t);var l=ot,s=Jt;Kn(a.type)&&(ot=a.stateNode,Jt=!1),gn(e,t,a),Gi(a.stateNode),ot=l,Jt=s;break;case 5:gt||Qa(a,t);case 6:if(l=ot,s=Jt,ot=null,gn(e,t,a),ot=l,Jt=s,ot!==null)if(Jt)try{(ot.nodeType===9?ot.body:ot.nodeName==="HTML"?ot.ownerDocument.body:ot).removeChild(a.stateNode)}catch(c){tt(a,t,c)}else try{ot.removeChild(a.stateNode)}catch(c){tt(a,t,c)}break;case 18:ot!==null&&(Jt?(e=ot,t0(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Zi(e)):t0(ot,a.stateNode));break;case 4:l=ot,s=Jt,ot=a.stateNode.containerInfo,Jt=!0,gn(e,t,a),ot=l,Jt=s;break;case 0:case 11:case 14:case 15:gt||Un(2,a,t),gt||Un(4,a,t),gn(e,t,a);break;case 1:gt||(Qa(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&iv(a,t,l)),gn(e,t,a);break;case 21:gn(e,t,a);break;case 22:gt=(l=gt)||a.memoizedState!==null,gn(e,t,a),gt=l;break;default:gn(e,t,a)}}function mv(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Zi(e)}catch(a){tt(t,t.return,a)}}function gb(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new cv),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new cv),t;default:throw Error(o(435,e.tag))}}function Gc(e,t){var a=gb(e);t.forEach(function(l){var s=jb.bind(null,e,l);a.has(l)||(a.add(l),l.then(s,s))})}function na(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var s=a[l],c=e,p=t,b=p;e:for(;b!==null;){switch(b.tag){case 27:if(Kn(b.type)){ot=b.stateNode,Jt=!1;break e}break;case 5:ot=b.stateNode,Jt=!1;break e;case 3:case 4:ot=b.stateNode.containerInfo,Jt=!0;break e}b=b.return}if(ot===null)throw Error(o(160));hv(c,p,s),ot=null,Jt=!1,c=s.alternate,c!==null&&(c.return=null),s.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)vv(t,e),t=t.sibling}var za=null;function vv(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:na(t,e),la(e),l&4&&(Un(3,e,e.return),Di(3,e),Un(5,e,e.return));break;case 1:na(t,e),la(e),l&512&&(gt||a===null||Qa(a,a.return)),l&64&&vn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var s=za;if(na(t,e),la(e),l&512&&(gt||a===null||Qa(a,a.return)),l&4){var c=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,s=s.ownerDocument||s;t:switch(l){case"title":c=s.getElementsByTagName("title")[0],(!c||c[be]||c[I]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=s.createElement(l),s.head.insertBefore(c,s.querySelector("head > title"))),Ot(c,l,a),c[I]=e,Ye(c),l=c;break e;case"link":var p=u0("link","href",s).get(l+(a.href||""));if(p){for(var b=0;b<p.length;b++)if(c=p[b],c.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&c.getAttribute("rel")===(a.rel==null?null:a.rel)&&c.getAttribute("title")===(a.title==null?null:a.title)&&c.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){p.splice(b,1);break t}}c=s.createElement(l),Ot(c,l,a),s.head.appendChild(c);break;case"meta":if(p=u0("meta","content",s).get(l+(a.content||""))){for(b=0;b<p.length;b++)if(c=p[b],c.getAttribute("content")===(a.content==null?null:""+a.content)&&c.getAttribute("name")===(a.name==null?null:a.name)&&c.getAttribute("property")===(a.property==null?null:a.property)&&c.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&c.getAttribute("charset")===(a.charSet==null?null:a.charSet)){p.splice(b,1);break t}}c=s.createElement(l),Ot(c,l,a),s.head.appendChild(c);break;default:throw Error(o(468,l))}c[I]=e,Ye(c),l=c}e.stateNode=l}else c0(s,e.type,e.stateNode);else e.stateNode=o0(s,l,e.memoizedProps);else c!==l?(c===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):c.count--,l===null?c0(s,e.type,e.stateNode):o0(s,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Uc(e,e.memoizedProps,a.memoizedProps)}break;case 27:na(t,e),la(e),l&512&&(gt||a===null||Qa(a,a.return)),a!==null&&l&4&&Uc(e,e.memoizedProps,a.memoizedProps);break;case 5:if(na(t,e),la(e),l&512&&(gt||a===null||Qa(a,a.return)),e.flags&32){s=e.stateNode;try{er(s,"")}catch(G){tt(e,e.return,G)}}l&4&&e.stateNode!=null&&(s=e.memoizedProps,Uc(e,s,a!==null?a.memoizedProps:s)),l&1024&&(qc=!0);break;case 6:if(na(t,e),la(e),l&4){if(e.stateNode===null)throw Error(o(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(G){tt(e,e.return,G)}}break;case 3:if(Eo=null,s=za,za=So(t.containerInfo),na(t,e),za=s,la(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Zi(t.containerInfo)}catch(G){tt(e,e.return,G)}qc&&(qc=!1,gv(e));break;case 4:l=za,za=So(e.stateNode.containerInfo),na(t,e),la(e),za=l;break;case 12:na(t,e),la(e);break;case 13:na(t,e),la(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Xc=ea()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Gc(e,l)));break;case 22:s=e.memoizedState!==null;var E=a!==null&&a.memoizedState!==null,H=vn,Q=gt;if(vn=H||s,gt=Q||E,na(t,e),gt=Q,vn=H,la(e),l&8192)e:for(t=e.stateNode,t._visibility=s?t._visibility&-2:t._visibility|1,s&&(a===null||E||vn||gt||Ol(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){E=a=t;try{if(c=E.stateNode,s)p=c.style,typeof p.setProperty=="function"?p.setProperty("display","none","important"):p.display="none";else{b=E.stateNode;var $=E.memoizedProps.style,B=$!=null&&$.hasOwnProperty("display")?$.display:null;b.style.display=B==null||typeof B=="boolean"?"":(""+B).trim()}}catch(G){tt(E,E.return,G)}}}else if(t.tag===6){if(a===null){E=t;try{E.stateNode.nodeValue=s?"":E.memoizedProps}catch(G){tt(E,E.return,G)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Gc(e,a))));break;case 19:na(t,e),la(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Gc(e,l)));break;case 30:break;case 21:break;default:na(t,e),la(e)}}function la(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(ov(l)){a=l;break}l=l.return}if(a==null)throw Error(o(160));switch(a.tag){case 27:var s=a.stateNode,c=Hc(e);oo(e,c,s);break;case 5:var p=a.stateNode;a.flags&32&&(er(p,""),a.flags&=-33);var b=Hc(e);oo(e,b,p);break;case 3:case 4:var E=a.stateNode.containerInfo,H=Hc(e);Bc(e,H,E);break;default:throw Error(o(161))}}catch(Q){tt(e,e.return,Q)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function gv(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;gv(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Hn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)dv(e,t.alternate,t),t=t.sibling}function Ol(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Un(4,t,t.return),Ol(t);break;case 1:Qa(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&iv(t,t.return,a),Ol(t);break;case 27:Gi(t.stateNode);case 26:case 5:Qa(t,t.return),Ol(t);break;case 22:t.memoizedState===null&&Ol(t);break;case 30:Ol(t);break;default:Ol(t)}e=e.sibling}}function Bn(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,s=e,c=t,p=c.flags;switch(c.tag){case 0:case 11:case 15:Bn(s,c,a),Di(4,c);break;case 1:if(Bn(s,c,a),l=c,s=l.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(H){tt(l,l.return,H)}if(l=c,s=l.updateQueue,s!==null){var b=l.stateNode;try{var E=s.shared.hiddenCallbacks;if(E!==null)for(s.shared.hiddenCallbacks=null,s=0;s<E.length;s++)Kh(E[s],b)}catch(H){tt(l,l.return,H)}}a&&p&64&&rv(c),Oi(c,c.return);break;case 27:uv(c);case 26:case 5:Bn(s,c,a),a&&l===null&&p&4&&sv(c),Oi(c,c.return);break;case 12:Bn(s,c,a);break;case 13:Bn(s,c,a),a&&p&4&&mv(s,c);break;case 22:c.memoizedState===null&&Bn(s,c,a),Oi(c,c.return);break;case 30:break;default:Bn(s,c,a)}t=t.sibling}}function Yc(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&pi(a))}function Vc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&pi(e))}function Ka(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)pv(e,t,a,l),t=t.sibling}function pv(e,t,a,l){var s=t.flags;switch(t.tag){case 0:case 11:case 15:Ka(e,t,a,l),s&2048&&Di(9,t);break;case 1:Ka(e,t,a,l);break;case 3:Ka(e,t,a,l),s&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&pi(e)));break;case 12:if(s&2048){Ka(e,t,a,l),e=t.stateNode;try{var c=t.memoizedProps,p=c.id,b=c.onPostCommit;typeof b=="function"&&b(p,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(E){tt(t,t.return,E)}}else Ka(e,t,a,l);break;case 13:Ka(e,t,a,l);break;case 23:break;case 22:c=t.stateNode,p=t.alternate,t.memoizedState!==null?c._visibility&2?Ka(e,t,a,l):zi(e,t):c._visibility&2?Ka(e,t,a,l):(c._visibility|=2,xr(e,t,a,l,(t.subtreeFlags&10256)!==0)),s&2048&&Yc(p,t);break;case 24:Ka(e,t,a,l),s&2048&&Vc(t.alternate,t);break;default:Ka(e,t,a,l)}}function xr(e,t,a,l,s){for(s=s&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,p=t,b=a,E=l,H=p.flags;switch(p.tag){case 0:case 11:case 15:xr(c,p,b,E,s),Di(8,p);break;case 23:break;case 22:var Q=p.stateNode;p.memoizedState!==null?Q._visibility&2?xr(c,p,b,E,s):zi(c,p):(Q._visibility|=2,xr(c,p,b,E,s)),s&&H&2048&&Yc(p.alternate,p);break;case 24:xr(c,p,b,E,s),s&&H&2048&&Vc(p.alternate,p);break;default:xr(c,p,b,E,s)}t=t.sibling}}function zi(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,s=l.flags;switch(l.tag){case 22:zi(a,l),s&2048&&Yc(l.alternate,l);break;case 24:zi(a,l),s&2048&&Vc(l.alternate,l);break;default:zi(a,l)}t=t.sibling}}var _i=8192;function br(e){if(e.subtreeFlags&_i)for(e=e.child;e!==null;)yv(e),e=e.sibling}function yv(e){switch(e.tag){case 26:br(e),e.flags&_i&&e.memoizedState!==null&&t1(za,e.memoizedState,e.memoizedProps);break;case 5:br(e);break;case 3:case 4:var t=za;za=So(e.stateNode.containerInfo),br(e),za=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=_i,_i=16777216,br(e),_i=t):br(e));break;default:br(e)}}function xv(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function ki(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Tt=l,wv(l,e)}xv(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)bv(e),e=e.sibling}function bv(e){switch(e.tag){case 0:case 11:case 15:ki(e),e.flags&2048&&Un(9,e,e.return);break;case 3:ki(e);break;case 12:ki(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,uo(e)):ki(e);break;default:ki(e)}}function uo(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];Tt=l,wv(l,e)}xv(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Un(8,t,t.return),uo(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,uo(t));break;default:uo(t)}e=e.sibling}}function wv(e,t){for(;Tt!==null;){var a=Tt;switch(a.tag){case 0:case 11:case 15:Un(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:pi(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Tt=l;else e:for(a=e;Tt!==null;){l=Tt;var s=l.sibling,c=l.return;if(fv(l),l===a){Tt=null;break e}if(s!==null){s.return=c,Tt=s;break e}Tt=c}}}var pb={getCacheForType:function(e){var t=_t(wt),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},yb=typeof WeakMap=="function"?WeakMap:Map,$e=0,at=null,ze=null,Be=0,Je=0,ra=null,qn=!1,wr=!1,Qc=!1,pn=0,ht=0,Gn=0,zl=0,Kc=0,ya=0,Sr=0,Li=null,It=null,Pc=!1,Xc=0,co=1/0,fo=null,Yn=null,Dt=0,Vn=null,Nr=null,Er=0,Fc=0,Zc=null,Sv=null,Ui=0,$c=null;function ia(){if(($e&2)!==0&&Be!==0)return Be&-Be;if(L.T!==null){var e=dr;return e!==0?e:nd()}return A()}function Nv(){ya===0&&(ya=(Be&536870912)===0||Pe?Jl():536870912);var e=pa.current;return e!==null&&(e.flags|=32),ya}function sa(e,t,a){(e===at&&(Je===2||Je===9)||e.cancelPendingCommit!==null)&&(jr(e,0),Qn(e,Be,ya,!1)),vl(e,a),(($e&2)===0||e!==at)&&(e===at&&(($e&2)===0&&(zl|=a),ht===4&&Qn(e,Be,ya,!1)),Pa(e))}function Ev(e,t,a){if(($e&6)!==0)throw Error(o(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||Ga(e,t),s=l?wb(e,t):Wc(e,t,!0),c=l;do{if(s===0){wr&&!l&&Qn(e,t,0,!1);break}else{if(a=e.current.alternate,c&&!xb(a)){s=Wc(e,t,!1),c=!1;continue}if(s===2){if(c=t,e.errorRecoveryDisabledLanes&c)var p=0;else p=e.pendingLanes&-536870913,p=p!==0?p:p&536870912?536870912:0;if(p!==0){t=p;e:{var b=e;s=Li;var E=b.current.memoizedState.isDehydrated;if(E&&(jr(b,p).flags|=256),p=Wc(b,p,!1),p!==2){if(Qc&&!E){b.errorRecoveryDisabledLanes|=c,zl|=c,s=4;break e}c=It,It=s,c!==null&&(It===null?It=c:It.push.apply(It,c))}s=p}if(c=!1,s!==2)continue}}if(s===1){jr(e,0),Qn(e,t,0,!0);break}e:{switch(l=e,c=s,c){case 0:case 1:throw Error(o(345));case 4:if((t&4194048)!==t)break;case 6:Qn(l,t,ya,!qn);break e;case 2:It=null;break;case 3:case 5:break;default:throw Error(o(329))}if((t&62914560)===t&&(s=Xc+300-ea(),10<s)){if(Qn(l,t,ya,!qn),ml(l,0,!0)!==0)break e;l.timeoutHandle=Wv(jv.bind(null,l,a,It,fo,Pc,t,ya,zl,Sr,qn,c,2,-0,0),s);break e}jv(l,a,It,fo,Pc,t,ya,zl,Sr,qn,c,0,-0,0)}}break}while(!0);Pa(e)}function jv(e,t,a,l,s,c,p,b,E,H,Q,$,B,G){if(e.timeoutHandle=-1,$=t.subtreeFlags,($&8192||($&16785408)===16785408)&&(Qi={stylesheets:null,count:0,unsuspend:e1},yv(t),$=a1(),$!==null)){e.cancelPendingCommit=$(Ov.bind(null,e,t,c,a,l,s,p,b,E,Q,1,B,G)),Qn(e,c,p,!H);return}Ov(e,t,c,a,l,s,p,b,E)}function xb(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var s=a[l],c=s.getSnapshot;s=s.value;try{if(!ta(c(),s))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Qn(e,t,a,l){t&=~Kc,t&=~zl,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var s=t;0<s;){var c=31-At(s),p=1<<c;l[c]=-1,s&=~p}a!==0&&gl(e,a,t)}function ho(){return($e&6)===0?(Hi(0),!1):!0}function Jc(){if(ze!==null){if(Je===0)var e=ze.return;else e=ze,un=Rl=null,mc(e),pr=null,Mi=0,e=ze;for(;e!==null;)lv(e.alternate,e),e=e.return;ze=null}}function jr(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,Ub(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Jc(),at=e,ze=a=rn(e.current,null),Be=t,Je=0,ra=null,qn=!1,wr=Ga(e,t),Qc=!1,Sr=ya=Kc=zl=Gn=ht=0,It=Li=null,Pc=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var s=31-At(l),c=1<<s;t|=e[s],l&=~c}return pn=t,_s(),a}function Tv(e,t){Ae=null,L.H=Ws,t===xi||t===Vs?(t=Vh(),Je=3):t===qh?(t=Vh(),Je=4):Je=t===Qm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,ra=t,ze===null&&(ht=1,lo(e,ha(t,e.current)))}function Rv(){var e=L.H;return L.H=Ws,e===null?Ws:e}function Mv(){var e=L.A;return L.A=pb,e}function Ic(){ht=4,qn||(Be&4194048)!==Be&&pa.current!==null||(wr=!0),(Gn&134217727)===0&&(zl&134217727)===0||at===null||Qn(at,Be,ya,!1)}function Wc(e,t,a){var l=$e;$e|=2;var s=Rv(),c=Mv();(at!==e||Be!==t)&&(fo=null,jr(e,t)),t=!1;var p=ht;e:do try{if(Je!==0&&ze!==null){var b=ze,E=ra;switch(Je){case 8:Jc(),p=6;break e;case 3:case 2:case 9:case 6:pa.current===null&&(t=!0);var H=Je;if(Je=0,ra=null,Tr(e,b,E,H),a&&wr){p=0;break e}break;default:H=Je,Je=0,ra=null,Tr(e,b,E,H)}}bb(),p=ht;break}catch(Q){Tv(e,Q)}while(!0);return t&&e.shellSuspendCounter++,un=Rl=null,$e=l,L.H=s,L.A=c,ze===null&&(at=null,Be=0,_s()),p}function bb(){for(;ze!==null;)Av(ze)}function wb(e,t){var a=$e;$e|=2;var l=Rv(),s=Mv();at!==e||Be!==t?(fo=null,co=ea()+500,jr(e,t)):wr=Ga(e,t);e:do try{if(Je!==0&&ze!==null){t=ze;var c=ra;t:switch(Je){case 1:Je=0,ra=null,Tr(e,t,c,1);break;case 2:case 9:if(Gh(c)){Je=0,ra=null,Cv(t);break}t=function(){Je!==2&&Je!==9||at!==e||(Je=7),Pa(e)},c.then(t,t);break e;case 3:Je=7;break e;case 4:Je=5;break e;case 7:Gh(c)?(Je=0,ra=null,Cv(t)):(Je=0,ra=null,Tr(e,t,c,7));break;case 5:var p=null;switch(ze.tag){case 26:p=ze.memoizedState;case 5:case 27:var b=ze;if(!p||d0(p)){Je=0,ra=null;var E=b.sibling;if(E!==null)ze=E;else{var H=b.return;H!==null?(ze=H,mo(H)):ze=null}break t}}Je=0,ra=null,Tr(e,t,c,5);break;case 6:Je=0,ra=null,Tr(e,t,c,6);break;case 8:Jc(),ht=6;break e;default:throw Error(o(462))}}Sb();break}catch(Q){Tv(e,Q)}while(!0);return un=Rl=null,L.H=l,L.A=s,$e=a,ze!==null?0:(at=null,Be=0,_s(),ht)}function Sb(){for(;ze!==null&&!xu();)Av(ze)}function Av(e){var t=av(e.alternate,e,pn);e.memoizedProps=e.pendingProps,t===null?mo(e):ze=t}function Cv(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=$m(a,t,t.pendingProps,t.type,void 0,Be);break;case 11:t=$m(a,t,t.pendingProps,t.type.render,t.ref,Be);break;case 5:mc(t);default:lv(a,t),t=ze=Dh(t,pn),t=av(a,t,pn)}e.memoizedProps=e.pendingProps,t===null?mo(e):ze=t}function Tr(e,t,a,l){un=Rl=null,mc(t),pr=null,Mi=0;var s=t.return;try{if(db(e,s,t,a,Be)){ht=1,lo(e,ha(a,e.current)),ze=null;return}}catch(c){if(s!==null)throw ze=s,c;ht=1,lo(e,ha(a,e.current)),ze=null;return}t.flags&32768?(Pe||l===1?e=!0:wr||(Be&536870912)!==0?e=!1:(qn=e=!0,(l===2||l===9||l===3||l===6)&&(l=pa.current,l!==null&&l.tag===13&&(l.flags|=16384))),Dv(t,e)):mo(t)}function mo(e){var t=e;do{if((t.flags&32768)!==0){Dv(t,qn);return}e=t.return;var a=hb(t.alternate,t,pn);if(a!==null){ze=a;return}if(t=t.sibling,t!==null){ze=t;return}ze=t=e}while(t!==null);ht===0&&(ht=5)}function Dv(e,t){do{var a=mb(e.alternate,e);if(a!==null){a.flags&=32767,ze=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){ze=e;return}ze=e=a}while(e!==null);ht=6,ze=null}function Ov(e,t,a,l,s,c,p,b,E){e.cancelPendingCommit=null;do vo();while(Dt!==0);if(($e&6)!==0)throw Error(o(327));if(t!==null){if(t===e.current)throw Error(o(177));if(c=t.lanes|t.childLanes,c|=Vu,Es(e,a,c,p,b,E),e===at&&(ze=at=null,Be=0),Nr=t,Vn=e,Er=a,Fc=c,Zc=s,Sv=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Tb($l,function(){return Uv(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=L.T,L.T=null,s=ae.p,ae.p=2,p=$e,$e|=4;try{vb(e,t,a)}finally{$e=p,ae.p=s,L.T=l}}Dt=1,zv(),_v(),kv()}}function zv(){if(Dt===1){Dt=0;var e=Vn,t=Nr,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=L.T,L.T=null;var l=ae.p;ae.p=2;var s=$e;$e|=4;try{vv(t,e);var c=dd,p=wh(e.containerInfo),b=c.focusedElem,E=c.selectionRange;if(p!==b&&b&&b.ownerDocument&&bh(b.ownerDocument.documentElement,b)){if(E!==null&&Hu(b)){var H=E.start,Q=E.end;if(Q===void 0&&(Q=H),"selectionStart"in b)b.selectionStart=H,b.selectionEnd=Math.min(Q,b.value.length);else{var $=b.ownerDocument||document,B=$&&$.defaultView||window;if(B.getSelection){var G=B.getSelection(),we=b.textContent.length,ye=Math.min(E.start,we),et=E.end===void 0?ye:Math.min(E.end,we);!G.extend&&ye>et&&(p=et,et=ye,ye=p);var z=xh(b,ye),D=xh(b,et);if(z&&D&&(G.rangeCount!==1||G.anchorNode!==z.node||G.anchorOffset!==z.offset||G.focusNode!==D.node||G.focusOffset!==D.offset)){var U=$.createRange();U.setStart(z.node,z.offset),G.removeAllRanges(),ye>et?(G.addRange(U),G.extend(D.node,D.offset)):(U.setEnd(D.node,D.offset),G.addRange(U))}}}}for($=[],G=b;G=G.parentNode;)G.nodeType===1&&$.push({element:G,left:G.scrollLeft,top:G.scrollTop});for(typeof b.focus=="function"&&b.focus(),b=0;b<$.length;b++){var F=$[b];F.element.scrollLeft=F.left,F.element.scrollTop=F.top}}Ro=!!cd,dd=cd=null}finally{$e=s,ae.p=l,L.T=a}}e.current=t,Dt=2}}function _v(){if(Dt===2){Dt=0;var e=Vn,t=Nr,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=L.T,L.T=null;var l=ae.p;ae.p=2;var s=$e;$e|=4;try{dv(e,t.alternate,t)}finally{$e=s,ae.p=l,L.T=a}}Dt=3}}function kv(){if(Dt===4||Dt===3){Dt=0,bu();var e=Vn,t=Nr,a=Er,l=Sv;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Dt=5:(Dt=0,Nr=Vn=null,Lv(e,e.pendingLanes));var s=e.pendingLanes;if(s===0&&(Yn=null),ti(a),t=t.stateNode,ut&&typeof ut.onCommitFiberRoot=="function")try{ut.onCommitFiberRoot(Pt,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=L.T,s=ae.p,ae.p=2,L.T=null;try{for(var c=e.onRecoverableError,p=0;p<l.length;p++){var b=l[p];c(b.value,{componentStack:b.stack})}}finally{L.T=t,ae.p=s}}(Er&3)!==0&&vo(),Pa(e),s=e.pendingLanes,(a&4194090)!==0&&(s&42)!==0?e===$c?Ui++:(Ui=0,$c=e):Ui=0,Hi(0)}}function Lv(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,pi(t)))}function vo(e){return zv(),_v(),kv(),Uv()}function Uv(){if(Dt!==5)return!1;var e=Vn,t=Fc;Fc=0;var a=ti(Er),l=L.T,s=ae.p;try{ae.p=32>a?32:a,L.T=null,a=Zc,Zc=null;var c=Vn,p=Er;if(Dt=0,Nr=Vn=null,Er=0,($e&6)!==0)throw Error(o(331));var b=$e;if($e|=4,bv(c.current),pv(c,c.current,p,a),$e=b,Hi(0,!1),ut&&typeof ut.onPostCommitFiberRoot=="function")try{ut.onPostCommitFiberRoot(Pt,c)}catch{}return!0}finally{ae.p=s,L.T=l,Lv(e,t)}}function Hv(e,t,a){t=ha(a,t),t=Mc(e.stateNode,t,2),e=zn(e,t,2),e!==null&&(vl(e,2),Pa(e))}function tt(e,t,a){if(e.tag===3)Hv(e,e,a);else for(;t!==null;){if(t.tag===3){Hv(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Yn===null||!Yn.has(l))){e=ha(a,e),a=Ym(2),l=zn(t,a,2),l!==null&&(Vm(a,l,t,e),vl(l,2),Pa(l));break}}t=t.return}}function ed(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new yb;var s=new Set;l.set(t,s)}else s=l.get(t),s===void 0&&(s=new Set,l.set(t,s));s.has(a)||(Qc=!0,s.add(a),e=Nb.bind(null,e,t,a),t.then(e,e))}function Nb(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,at===e&&(Be&a)===a&&(ht===4||ht===3&&(Be&62914560)===Be&&300>ea()-Xc?($e&2)===0&&jr(e,0):Kc|=a,Sr===Be&&(Sr=0)),Pa(e)}function Bv(e,t){t===0&&(t=Ns()),e=sr(e,t),e!==null&&(vl(e,t),Pa(e))}function Eb(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Bv(e,a)}function jb(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,s=e.memoizedState;s!==null&&(a=s.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(o(314))}l!==null&&l.delete(t),Bv(e,a)}function Tb(e,t){return Ir(e,t)}var go=null,Rr=null,td=!1,po=!1,ad=!1,_l=0;function Pa(e){e!==Rr&&e.next===null&&(Rr===null?go=Rr=e:Rr=Rr.next=e),po=!0,td||(td=!0,Mb())}function Hi(e,t){if(!ad&&po){ad=!0;do for(var a=!1,l=go;l!==null;){if(e!==0){var s=l.pendingLanes;if(s===0)var c=0;else{var p=l.suspendedLanes,b=l.pingedLanes;c=(1<<31-At(42|e)+1)-1,c&=s&~(p&~b),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(a=!0,Vv(l,c))}else c=Be,c=ml(l,l===at?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||Ga(l,c)||(a=!0,Vv(l,c));l=l.next}while(a);ad=!1}}function Rb(){qv()}function qv(){po=td=!1;var e=0;_l!==0&&(Lb()&&(e=_l),_l=0);for(var t=ea(),a=null,l=go;l!==null;){var s=l.next,c=Gv(l,t);c===0?(l.next=null,a===null?go=s:a.next=s,s===null&&(Rr=a)):(a=l,(e!==0||(c&3)!==0)&&(po=!0)),l=s}Hi(e)}function Gv(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,s=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var p=31-At(c),b=1<<p,E=s[p];E===-1?((b&a)===0||(b&l)!==0)&&(s[p]=Ss(b,t)):E<=t&&(e.expiredLanes|=b),c&=~b}if(t=at,a=Be,a=ml(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(Je===2||Je===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Ma(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||Ga(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&Ma(l),ti(a)){case 2:case 8:a=xs;break;case 32:a=$l;break;case 268435456:a=En;break;default:a=$l}return l=Yv.bind(null,e),a=Ir(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&Ma(l),e.callbackPriority=2,e.callbackNode=null,2}function Yv(e,t){if(Dt!==0&&Dt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(vo()&&e.callbackNode!==a)return null;var l=Be;return l=ml(e,e===at?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Ev(e,l,t),Gv(e,ea()),e.callbackNode!=null&&e.callbackNode===a?Yv.bind(null,e):null)}function Vv(e,t){if(vo())return null;Ev(e,t,!0)}function Mb(){Hb(function(){($e&6)!==0?Ir(ys,Rb):qv()})}function nd(){return _l===0&&(_l=Jl()),_l}function Qv(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Rs(""+e)}function Kv(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Ab(e,t,a,l,s){if(t==="submit"&&a&&a.stateNode===s){var c=Qv((s[ne]||null).action),p=l.submitter;p&&(t=(t=p[ne]||null)?Qv(t.formAction):p.getAttribute("formAction"),t!==null&&(c=t,p=null));var b=new Ds("action","action",null,l,s);e.push({event:b,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(_l!==0){var E=p?Kv(s,p):new FormData(s);Nc(a,{pending:!0,data:E,method:s.method,action:c},null,E)}}else typeof c=="function"&&(b.preventDefault(),E=p?Kv(s,p):new FormData(s),Nc(a,{pending:!0,data:E,method:s.method,action:c},c,E))},currentTarget:s}]})}}for(var ld=0;ld<Yu.length;ld++){var rd=Yu[ld],Cb=rd.toLowerCase(),Db=rd[0].toUpperCase()+rd.slice(1);Oa(Cb,"on"+Db)}Oa(Eh,"onAnimationEnd"),Oa(jh,"onAnimationIteration"),Oa(Th,"onAnimationStart"),Oa("dblclick","onDoubleClick"),Oa("focusin","onFocus"),Oa("focusout","onBlur"),Oa(Fx,"onTransitionRun"),Oa(Zx,"onTransitionStart"),Oa($x,"onTransitionCancel"),Oa(Rh,"onTransitionEnd"),ca("onMouseEnter",["mouseout","mouseover"]),ca("onMouseLeave",["mouseout","mouseover"]),ca("onPointerEnter",["pointerout","pointerover"]),ca("onPointerLeave",["pointerout","pointerover"]),qt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),qt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),qt("onBeforeInput",["compositionend","keypress","textInput","paste"]),qt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),qt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),qt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Bi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ob=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Bi));function Pv(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],s=l.event;l=l.listeners;e:{var c=void 0;if(t)for(var p=l.length-1;0<=p;p--){var b=l[p],E=b.instance,H=b.currentTarget;if(b=b.listener,E!==c&&s.isPropagationStopped())break e;c=b,s.currentTarget=H;try{c(s)}catch(Q){no(Q)}s.currentTarget=null,c=E}else for(p=0;p<l.length;p++){if(b=l[p],E=b.instance,H=b.currentTarget,b=b.listener,E!==c&&s.isPropagationStopped())break e;c=b,s.currentTarget=H;try{c(s)}catch(Q){no(Q)}s.currentTarget=null,c=E}}}}function _e(e,t){var a=t[pe];a===void 0&&(a=t[pe]=new Set);var l=e+"__bubble";a.has(l)||(Xv(t,e,2,!1),a.add(l))}function id(e,t,a){var l=0;t&&(l|=4),Xv(a,e,l,t)}var yo="_reactListening"+Math.random().toString(36).slice(2);function sd(e){if(!e[yo]){e[yo]=!0,Ue.forEach(function(a){a!=="selectionchange"&&(Ob.has(a)||id(a,!1,e),id(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[yo]||(t[yo]=!0,id("selectionchange",!1,t))}}function Xv(e,t,a,l){switch(p0(t)){case 2:var s=r1;break;case 8:s=i1;break;default:s=wd}a=s.bind(null,t,a,e),s=void 0,!Au||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),l?s!==void 0?e.addEventListener(t,a,{capture:!0,passive:s}):e.addEventListener(t,a,!0):s!==void 0?e.addEventListener(t,a,{passive:s}):e.addEventListener(t,a,!1)}function od(e,t,a,l,s){var c=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var p=l.tag;if(p===3||p===4){var b=l.stateNode.containerInfo;if(b===s)break;if(p===4)for(p=l.return;p!==null;){var E=p.tag;if((E===3||E===4)&&p.stateNode.containerInfo===s)return;p=p.return}for(;b!==null;){if(p=Se(b),p===null)return;if(E=p.tag,E===5||E===6||E===26||E===27){l=c=p;continue e}b=b.parentNode}}l=l.return}eh(function(){var H=c,Q=Ru(a),$=[];e:{var B=Mh.get(e);if(B!==void 0){var G=Ds,we=e;switch(e){case"keypress":if(As(a)===0)break e;case"keydown":case"keyup":G=Tx;break;case"focusin":we="focus",G=zu;break;case"focusout":we="blur",G=zu;break;case"beforeblur":case"afterblur":G=zu;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":G=nh;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":G=mx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":G=Ax;break;case Eh:case jh:case Th:G=px;break;case Rh:G=Dx;break;case"scroll":case"scrollend":G=fx;break;case"wheel":G=zx;break;case"copy":case"cut":case"paste":G=xx;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":G=rh;break;case"toggle":case"beforetoggle":G=kx}var ye=(t&4)!==0,et=!ye&&(e==="scroll"||e==="scrollend"),z=ye?B!==null?B+"Capture":null:B;ye=[];for(var D=H,U;D!==null;){var F=D;if(U=F.stateNode,F=F.tag,F!==5&&F!==26&&F!==27||U===null||z===null||(F=li(D,z),F!=null&&ye.push(qi(D,F,U))),et)break;D=D.return}0<ye.length&&(B=new G(B,we,null,a,Q),$.push({event:B,listeners:ye}))}}if((t&7)===0){e:{if(B=e==="mouseover"||e==="pointerover",G=e==="mouseout"||e==="pointerout",B&&a!==Tu&&(we=a.relatedTarget||a.fromElement)&&(Se(we)||we[ce]))break e;if((G||B)&&(B=Q.window===Q?Q:(B=Q.ownerDocument)?B.defaultView||B.parentWindow:window,G?(we=a.relatedTarget||a.toElement,G=H,we=we?Se(we):null,we!==null&&(et=f(we),ye=we.tag,we!==et||ye!==5&&ye!==27&&ye!==6)&&(we=null)):(G=null,we=H),G!==we)){if(ye=nh,F="onMouseLeave",z="onMouseEnter",D="mouse",(e==="pointerout"||e==="pointerover")&&(ye=rh,F="onPointerLeave",z="onPointerEnter",D="pointer"),et=G==null?B:lt(G),U=we==null?B:lt(we),B=new ye(F,D+"leave",G,a,Q),B.target=et,B.relatedTarget=U,F=null,Se(Q)===H&&(ye=new ye(z,D+"enter",we,a,Q),ye.target=U,ye.relatedTarget=et,F=ye),et=F,G&&we)t:{for(ye=G,z=we,D=0,U=ye;U;U=Mr(U))D++;for(U=0,F=z;F;F=Mr(F))U++;for(;0<D-U;)ye=Mr(ye),D--;for(;0<U-D;)z=Mr(z),U--;for(;D--;){if(ye===z||z!==null&&ye===z.alternate)break t;ye=Mr(ye),z=Mr(z)}ye=null}else ye=null;G!==null&&Fv($,B,G,ye,!1),we!==null&&et!==null&&Fv($,et,we,ye,!0)}}e:{if(B=H?lt(H):window,G=B.nodeName&&B.nodeName.toLowerCase(),G==="select"||G==="input"&&B.type==="file")var se=hh;else if(dh(B))if(mh)se=Kx;else{se=Vx;var Oe=Yx}else G=B.nodeName,!G||G.toLowerCase()!=="input"||B.type!=="checkbox"&&B.type!=="radio"?H&&ju(H.elementType)&&(se=hh):se=Qx;if(se&&(se=se(e,H))){fh($,se,a,Q);break e}Oe&&Oe(e,B,H),e==="focusout"&&H&&B.type==="number"&&H.memoizedProps.value!=null&&Eu(B,"number",B.value)}switch(Oe=H?lt(H):window,e){case"focusin":(dh(Oe)||Oe.contentEditable==="true")&&(lr=Oe,Bu=H,fi=null);break;case"focusout":fi=Bu=lr=null;break;case"mousedown":qu=!0;break;case"contextmenu":case"mouseup":case"dragend":qu=!1,Sh($,a,Q);break;case"selectionchange":if(Xx)break;case"keydown":case"keyup":Sh($,a,Q)}var fe;if(ku)e:{switch(e){case"compositionstart":var xe="onCompositionStart";break e;case"compositionend":xe="onCompositionEnd";break e;case"compositionupdate":xe="onCompositionUpdate";break e}xe=void 0}else nr?uh(e,a)&&(xe="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(xe="onCompositionStart");xe&&(ih&&a.locale!=="ko"&&(nr||xe!=="onCompositionStart"?xe==="onCompositionEnd"&&nr&&(fe=th()):(An=Q,Cu="value"in An?An.value:An.textContent,nr=!0)),Oe=xo(H,xe),0<Oe.length&&(xe=new lh(xe,e,null,a,Q),$.push({event:xe,listeners:Oe}),fe?xe.data=fe:(fe=ch(a),fe!==null&&(xe.data=fe)))),(fe=Ux?Hx(e,a):Bx(e,a))&&(xe=xo(H,"onBeforeInput"),0<xe.length&&(Oe=new lh("onBeforeInput","beforeinput",null,a,Q),$.push({event:Oe,listeners:xe}),Oe.data=fe)),Ab($,e,H,a,Q)}Pv($,t)})}function qi(e,t,a){return{instance:e,listener:t,currentTarget:a}}function xo(e,t){for(var a=t+"Capture",l=[];e!==null;){var s=e,c=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||c===null||(s=li(e,a),s!=null&&l.unshift(qi(e,s,c)),s=li(e,t),s!=null&&l.push(qi(e,s,c))),e.tag===3)return l;e=e.return}return[]}function Mr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Fv(e,t,a,l,s){for(var c=t._reactName,p=[];a!==null&&a!==l;){var b=a,E=b.alternate,H=b.stateNode;if(b=b.tag,E!==null&&E===l)break;b!==5&&b!==26&&b!==27||H===null||(E=H,s?(H=li(a,c),H!=null&&p.unshift(qi(a,H,E))):s||(H=li(a,c),H!=null&&p.push(qi(a,H,E)))),a=a.return}p.length!==0&&e.push({event:t,listeners:p})}var zb=/\r\n?/g,_b=/\u0000|\uFFFD/g;function Zv(e){return(typeof e=="string"?e:""+e).replace(zb,`
`).replace(_b,"")}function $v(e,t){return t=Zv(t),Zv(e)===t}function bo(){}function We(e,t,a,l,s,c){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||er(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&er(e,""+l);break;case"className":nn(e,"class",l);break;case"tabIndex":nn(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":nn(e,a,l);break;case"style":If(e,l,c);break;case"data":if(t!=="object"){nn(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=Rs(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(a==="formAction"?(t!=="input"&&We(e,t,"name",s.name,s,null),We(e,t,"formEncType",s.formEncType,s,null),We(e,t,"formMethod",s.formMethod,s,null),We(e,t,"formTarget",s.formTarget,s,null)):(We(e,t,"encType",s.encType,s,null),We(e,t,"method",s.method,s,null),We(e,t,"target",s.target,s,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=Rs(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=bo);break;case"onScroll":l!=null&&_e("scroll",e);break;case"onScrollEnd":l!=null&&_e("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(a=l.__html,a!=null){if(s.children!=null)throw Error(o(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=Rs(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":_e("beforetoggle",e),_e("toggle",e),da(e,"popover",l);break;case"xlinkActuate":De(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":De(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":De(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":De(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":De(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":De(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":De(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":De(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":De(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":da(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=cx.get(a)||a,da(e,a,l))}}function ud(e,t,a,l,s,c){switch(a){case"style":If(e,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(a=l.__html,a!=null){if(s.children!=null)throw Error(o(60));e.innerHTML=a}}break;case"children":typeof l=="string"?er(e,l):(typeof l=="number"||typeof l=="bigint")&&er(e,""+l);break;case"onScroll":l!=null&&_e("scroll",e);break;case"onScrollEnd":l!=null&&_e("scrollend",e);break;case"onClick":l!=null&&(e.onclick=bo);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!jn.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(s=a.endsWith("Capture"),t=a.slice(2,s?a.length-7:void 0),c=e[ne]||null,c=c!=null?c[a]:null,typeof c=="function"&&e.removeEventListener(t,c,s),typeof l=="function")){typeof c!="function"&&c!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,s);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):da(e,a,l)}}}function Ot(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":_e("error",e),_e("load",e);var l=!1,s=!1,c;for(c in a)if(a.hasOwnProperty(c)){var p=a[c];if(p!=null)switch(c){case"src":l=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:We(e,t,c,p,a,null)}}s&&We(e,t,"srcSet",a.srcSet,a,null),l&&We(e,t,"src",a.src,a,null);return;case"input":_e("invalid",e);var b=c=p=s=null,E=null,H=null;for(l in a)if(a.hasOwnProperty(l)){var Q=a[l];if(Q!=null)switch(l){case"name":s=Q;break;case"type":p=Q;break;case"checked":E=Q;break;case"defaultChecked":H=Q;break;case"value":c=Q;break;case"defaultValue":b=Q;break;case"children":case"dangerouslySetInnerHTML":if(Q!=null)throw Error(o(137,t));break;default:We(e,t,l,Q,a,null)}}Ff(e,c,b,E,H,p,s,!1),js(e);return;case"select":_e("invalid",e),l=p=c=null;for(s in a)if(a.hasOwnProperty(s)&&(b=a[s],b!=null))switch(s){case"value":c=b;break;case"defaultValue":p=b;break;case"multiple":l=b;default:We(e,t,s,b,a,null)}t=c,a=p,e.multiple=!!l,t!=null?Wl(e,!!l,t,!1):a!=null&&Wl(e,!!l,a,!0);return;case"textarea":_e("invalid",e),c=s=l=null;for(p in a)if(a.hasOwnProperty(p)&&(b=a[p],b!=null))switch(p){case"value":l=b;break;case"defaultValue":s=b;break;case"children":c=b;break;case"dangerouslySetInnerHTML":if(b!=null)throw Error(o(91));break;default:We(e,t,p,b,a,null)}$f(e,l,s,c),js(e);return;case"option":for(E in a)if(a.hasOwnProperty(E)&&(l=a[E],l!=null))switch(E){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:We(e,t,E,l,a,null)}return;case"dialog":_e("beforetoggle",e),_e("toggle",e),_e("cancel",e),_e("close",e);break;case"iframe":case"object":_e("load",e);break;case"video":case"audio":for(l=0;l<Bi.length;l++)_e(Bi[l],e);break;case"image":_e("error",e),_e("load",e);break;case"details":_e("toggle",e);break;case"embed":case"source":case"link":_e("error",e),_e("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(H in a)if(a.hasOwnProperty(H)&&(l=a[H],l!=null))switch(H){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:We(e,t,H,l,a,null)}return;default:if(ju(t)){for(Q in a)a.hasOwnProperty(Q)&&(l=a[Q],l!==void 0&&ud(e,t,Q,l,a,void 0));return}}for(b in a)a.hasOwnProperty(b)&&(l=a[b],l!=null&&We(e,t,b,l,a,null))}function kb(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,c=null,p=null,b=null,E=null,H=null,Q=null;for(G in a){var $=a[G];if(a.hasOwnProperty(G)&&$!=null)switch(G){case"checked":break;case"value":break;case"defaultValue":E=$;default:l.hasOwnProperty(G)||We(e,t,G,null,l,$)}}for(var B in l){var G=l[B];if($=a[B],l.hasOwnProperty(B)&&(G!=null||$!=null))switch(B){case"type":c=G;break;case"name":s=G;break;case"checked":H=G;break;case"defaultChecked":Q=G;break;case"value":p=G;break;case"defaultValue":b=G;break;case"children":case"dangerouslySetInnerHTML":if(G!=null)throw Error(o(137,t));break;default:G!==$&&We(e,t,B,G,l,$)}}Nu(e,p,b,E,H,Q,c,s);return;case"select":G=p=b=B=null;for(c in a)if(E=a[c],a.hasOwnProperty(c)&&E!=null)switch(c){case"value":break;case"multiple":G=E;default:l.hasOwnProperty(c)||We(e,t,c,null,l,E)}for(s in l)if(c=l[s],E=a[s],l.hasOwnProperty(s)&&(c!=null||E!=null))switch(s){case"value":B=c;break;case"defaultValue":b=c;break;case"multiple":p=c;default:c!==E&&We(e,t,s,c,l,E)}t=b,a=p,l=G,B!=null?Wl(e,!!a,B,!1):!!l!=!!a&&(t!=null?Wl(e,!!a,t,!0):Wl(e,!!a,a?[]:"",!1));return;case"textarea":G=B=null;for(b in a)if(s=a[b],a.hasOwnProperty(b)&&s!=null&&!l.hasOwnProperty(b))switch(b){case"value":break;case"children":break;default:We(e,t,b,null,l,s)}for(p in l)if(s=l[p],c=a[p],l.hasOwnProperty(p)&&(s!=null||c!=null))switch(p){case"value":B=s;break;case"defaultValue":G=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(o(91));break;default:s!==c&&We(e,t,p,s,l,c)}Zf(e,B,G);return;case"option":for(var we in a)if(B=a[we],a.hasOwnProperty(we)&&B!=null&&!l.hasOwnProperty(we))switch(we){case"selected":e.selected=!1;break;default:We(e,t,we,null,l,B)}for(E in l)if(B=l[E],G=a[E],l.hasOwnProperty(E)&&B!==G&&(B!=null||G!=null))switch(E){case"selected":e.selected=B&&typeof B!="function"&&typeof B!="symbol";break;default:We(e,t,E,B,l,G)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ye in a)B=a[ye],a.hasOwnProperty(ye)&&B!=null&&!l.hasOwnProperty(ye)&&We(e,t,ye,null,l,B);for(H in l)if(B=l[H],G=a[H],l.hasOwnProperty(H)&&B!==G&&(B!=null||G!=null))switch(H){case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(o(137,t));break;default:We(e,t,H,B,l,G)}return;default:if(ju(t)){for(var et in a)B=a[et],a.hasOwnProperty(et)&&B!==void 0&&!l.hasOwnProperty(et)&&ud(e,t,et,void 0,l,B);for(Q in l)B=l[Q],G=a[Q],!l.hasOwnProperty(Q)||B===G||B===void 0&&G===void 0||ud(e,t,Q,B,l,G);return}}for(var z in a)B=a[z],a.hasOwnProperty(z)&&B!=null&&!l.hasOwnProperty(z)&&We(e,t,z,null,l,B);for($ in l)B=l[$],G=a[$],!l.hasOwnProperty($)||B===G||B==null&&G==null||We(e,t,$,B,l,G)}var cd=null,dd=null;function wo(e){return e.nodeType===9?e:e.ownerDocument}function Jv(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Iv(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function fd(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var hd=null;function Lb(){var e=window.event;return e&&e.type==="popstate"?e===hd?!1:(hd=e,!0):(hd=null,!1)}var Wv=typeof setTimeout=="function"?setTimeout:void 0,Ub=typeof clearTimeout=="function"?clearTimeout:void 0,e0=typeof Promise=="function"?Promise:void 0,Hb=typeof queueMicrotask=="function"?queueMicrotask:typeof e0<"u"?function(e){return e0.resolve(null).then(e).catch(Bb)}:Wv;function Bb(e){setTimeout(function(){throw e})}function Kn(e){return e==="head"}function t0(e,t){var a=t,l=0,s=0;do{var c=a.nextSibling;if(e.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(0<l&&8>l){a=l;var p=e.ownerDocument;if(a&1&&Gi(p.documentElement),a&2&&Gi(p.body),a&4)for(a=p.head,Gi(a),p=a.firstChild;p;){var b=p.nextSibling,E=p.nodeName;p[be]||E==="SCRIPT"||E==="STYLE"||E==="LINK"&&p.rel.toLowerCase()==="stylesheet"||a.removeChild(p),p=b}}if(s===0){e.removeChild(c),Zi(t);return}s--}else a==="$"||a==="$?"||a==="$!"?s++:l=a.charCodeAt(0)-48;else l=0;a=c}while(a);Zi(t)}function md(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":md(a),de(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function qb(e,t,a,l){for(;e.nodeType===1;){var s=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[be])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==s.rel||e.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||e.getAttribute("title")!==(s.title==null?null:s.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(s.src==null?null:s.src)||e.getAttribute("type")!==(s.type==null?null:s.type)||e.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=s.name==null?null:""+s.name;if(s.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=_a(e.nextSibling),e===null)break}return null}function Gb(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=_a(e.nextSibling),e===null))return null;return e}function vd(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Yb(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function _a(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var gd=null;function a0(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function n0(e,t,a){switch(t=wo(a),e){case"html":if(e=t.documentElement,!e)throw Error(o(452));return e;case"head":if(e=t.head,!e)throw Error(o(453));return e;case"body":if(e=t.body,!e)throw Error(o(454));return e;default:throw Error(o(451))}}function Gi(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);de(e)}var xa=new Map,l0=new Set;function So(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var yn=ae.d;ae.d={f:Vb,r:Qb,D:Kb,C:Pb,L:Xb,m:Fb,X:$b,S:Zb,M:Jb};function Vb(){var e=yn.f(),t=ho();return e||t}function Qb(e){var t=Ge(e);t!==null&&t.tag===5&&t.type==="form"?Em(t):yn.r(e)}var Ar=typeof document>"u"?null:document;function r0(e,t,a){var l=Ar;if(l&&typeof t=="string"&&t){var s=fa(t);s='link[rel="'+e+'"][href="'+s+'"]',typeof a=="string"&&(s+='[crossorigin="'+a+'"]'),l0.has(s)||(l0.add(s),e={rel:e,crossOrigin:a,href:t},l.querySelector(s)===null&&(t=l.createElement("link"),Ot(t,"link",e),Ye(t),l.head.appendChild(t)))}}function Kb(e){yn.D(e),r0("dns-prefetch",e,null)}function Pb(e,t){yn.C(e,t),r0("preconnect",e,t)}function Xb(e,t,a){yn.L(e,t,a);var l=Ar;if(l&&e&&t){var s='link[rel="preload"][as="'+fa(t)+'"]';t==="image"&&a&&a.imageSrcSet?(s+='[imagesrcset="'+fa(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(s+='[imagesizes="'+fa(a.imageSizes)+'"]')):s+='[href="'+fa(e)+'"]';var c=s;switch(t){case"style":c=Cr(e);break;case"script":c=Dr(e)}xa.has(c)||(e=y({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),xa.set(c,e),l.querySelector(s)!==null||t==="style"&&l.querySelector(Yi(c))||t==="script"&&l.querySelector(Vi(c))||(t=l.createElement("link"),Ot(t,"link",e),Ye(t),l.head.appendChild(t)))}}function Fb(e,t){yn.m(e,t);var a=Ar;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",s='link[rel="modulepreload"][as="'+fa(l)+'"][href="'+fa(e)+'"]',c=s;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Dr(e)}if(!xa.has(c)&&(e=y({rel:"modulepreload",href:e},t),xa.set(c,e),a.querySelector(s)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Vi(c)))return}l=a.createElement("link"),Ot(l,"link",e),Ye(l),a.head.appendChild(l)}}}function Zb(e,t,a){yn.S(e,t,a);var l=Ar;if(l&&e){var s=ct(l).hoistableStyles,c=Cr(e);t=t||"default";var p=s.get(c);if(!p){var b={loading:0,preload:null};if(p=l.querySelector(Yi(c)))b.loading=5;else{e=y({rel:"stylesheet",href:e,"data-precedence":t},a),(a=xa.get(c))&&pd(e,a);var E=p=l.createElement("link");Ye(E),Ot(E,"link",e),E._p=new Promise(function(H,Q){E.onload=H,E.onerror=Q}),E.addEventListener("load",function(){b.loading|=1}),E.addEventListener("error",function(){b.loading|=2}),b.loading|=4,No(p,t,l)}p={type:"stylesheet",instance:p,count:1,state:b},s.set(c,p)}}}function $b(e,t){yn.X(e,t);var a=Ar;if(a&&e){var l=ct(a).hoistableScripts,s=Dr(e),c=l.get(s);c||(c=a.querySelector(Vi(s)),c||(e=y({src:e,async:!0},t),(t=xa.get(s))&&yd(e,t),c=a.createElement("script"),Ye(c),Ot(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(s,c))}}function Jb(e,t){yn.M(e,t);var a=Ar;if(a&&e){var l=ct(a).hoistableScripts,s=Dr(e),c=l.get(s);c||(c=a.querySelector(Vi(s)),c||(e=y({src:e,async:!0,type:"module"},t),(t=xa.get(s))&&yd(e,t),c=a.createElement("script"),Ye(c),Ot(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(s,c))}}function i0(e,t,a,l){var s=(s=he.current)?So(s):null;if(!s)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Cr(a.href),a=ct(s).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Cr(a.href);var c=ct(s).hoistableStyles,p=c.get(e);if(p||(s=s.ownerDocument||s,p={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,p),(c=s.querySelector(Yi(e)))&&!c._p&&(p.instance=c,p.state.loading=5),xa.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},xa.set(e,a),c||Ib(s,e,a,p.state))),t&&l===null)throw Error(o(528,""));return p}if(t&&l!==null)throw Error(o(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Dr(a),a=ct(s).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function Cr(e){return'href="'+fa(e)+'"'}function Yi(e){return'link[rel="stylesheet"]['+e+"]"}function s0(e){return y({},e,{"data-precedence":e.precedence,precedence:null})}function Ib(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),Ot(t,"link",a),Ye(t),e.head.appendChild(t))}function Dr(e){return'[src="'+fa(e)+'"]'}function Vi(e){return"script[async]"+e}function o0(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+fa(a.href)+'"]');if(l)return t.instance=l,Ye(l),l;var s=y({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ye(l),Ot(l,"style",s),No(l,a.precedence,e),t.instance=l;case"stylesheet":s=Cr(a.href);var c=e.querySelector(Yi(s));if(c)return t.state.loading|=4,t.instance=c,Ye(c),c;l=s0(a),(s=xa.get(s))&&pd(l,s),c=(e.ownerDocument||e).createElement("link"),Ye(c);var p=c;return p._p=new Promise(function(b,E){p.onload=b,p.onerror=E}),Ot(c,"link",l),t.state.loading|=4,No(c,a.precedence,e),t.instance=c;case"script":return c=Dr(a.src),(s=e.querySelector(Vi(c)))?(t.instance=s,Ye(s),s):(l=a,(s=xa.get(c))&&(l=y({},a),yd(l,s)),e=e.ownerDocument||e,s=e.createElement("script"),Ye(s),Ot(s,"link",l),e.head.appendChild(s),t.instance=s);case"void":return null;default:throw Error(o(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,No(l,a.precedence,e));return t.instance}function No(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=l.length?l[l.length-1]:null,c=s,p=0;p<l.length;p++){var b=l[p];if(b.dataset.precedence===t)c=b;else if(c!==s)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function pd(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function yd(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Eo=null;function u0(e,t,a){if(Eo===null){var l=new Map,s=Eo=new Map;s.set(a,l)}else s=Eo,l=s.get(a),l||(l=new Map,s.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),s=0;s<a.length;s++){var c=a[s];if(!(c[be]||c[I]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var p=c.getAttribute(t)||"";p=e+p;var b=l.get(p);b?b.push(c):l.set(p,[c])}}return l}function c0(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Wb(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function d0(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Qi=null;function e1(){}function t1(e,t,a){if(Qi===null)throw Error(o(475));var l=Qi;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var s=Cr(a.href),c=e.querySelector(Yi(s));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=jo.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=c,Ye(c);return}c=e.ownerDocument||e,a=s0(a),(s=xa.get(s))&&pd(a,s),c=c.createElement("link"),Ye(c);var p=c;p._p=new Promise(function(b,E){p.onload=b,p.onerror=E}),Ot(c,"link",a),t.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=jo.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function a1(){if(Qi===null)throw Error(o(475));var e=Qi;return e.stylesheets&&e.count===0&&xd(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&xd(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function jo(){if(this.count--,this.count===0){if(this.stylesheets)xd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var To=null;function xd(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,To=new Map,t.forEach(n1,e),To=null,jo.call(e))}function n1(e,t){if(!(t.state.loading&4)){var a=To.get(e);if(a)var l=a.get(null);else{a=new Map,To.set(e,a);for(var s=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<s.length;c++){var p=s[c];(p.nodeName==="LINK"||p.getAttribute("media")!=="not all")&&(a.set(p.dataset.precedence,p),l=p)}l&&a.set(null,l)}s=t.instance,p=s.getAttribute("data-precedence"),c=a.get(p)||l,c===l&&a.set(null,s),a.set(p,s),this.count++,l=jo.bind(this),s.addEventListener("load",l),s.addEventListener("error",l),c?c.parentNode.insertBefore(s,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(s,e.firstChild)),t.state.loading|=4}}var Ki={$$typeof:_,Provider:null,Consumer:null,_currentValue:J,_currentValue2:J,_threadCount:0};function l1(e,t,a,l,s,c,p,b){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Il(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Il(0),this.hiddenUpdates=Il(null),this.identifierPrefix=l,this.onUncaughtError=s,this.onCaughtError=c,this.onRecoverableError=p,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=b,this.incompleteTransitions=new Map}function f0(e,t,a,l,s,c,p,b,E,H,Q,$){return e=new l1(e,t,a,p,b,E,H,$),t=1,c===!0&&(t|=24),c=aa(3,null,null,t),e.current=c,c.stateNode=e,t=ec(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:l,isDehydrated:a,cache:t},lc(c),e}function h0(e){return e?(e=or,e):or}function m0(e,t,a,l,s,c){s=h0(s),l.context===null?l.context=s:l.pendingContext=s,l=On(t),l.payload={element:a},c=c===void 0?null:c,c!==null&&(l.callback=c),a=zn(e,l,t),a!==null&&(sa(a,e,t),wi(a,e,t))}function v0(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function bd(e,t){v0(e,t),(e=e.alternate)&&v0(e,t)}function g0(e){if(e.tag===13){var t=sr(e,67108864);t!==null&&sa(t,e,67108864),bd(e,67108864)}}var Ro=!0;function r1(e,t,a,l){var s=L.T;L.T=null;var c=ae.p;try{ae.p=2,wd(e,t,a,l)}finally{ae.p=c,L.T=s}}function i1(e,t,a,l){var s=L.T;L.T=null;var c=ae.p;try{ae.p=8,wd(e,t,a,l)}finally{ae.p=c,L.T=s}}function wd(e,t,a,l){if(Ro){var s=Sd(l);if(s===null)od(e,t,l,Mo,a),y0(e,l);else if(o1(s,e,t,a,l))l.stopPropagation();else if(y0(e,l),t&4&&-1<s1.indexOf(e)){for(;s!==null;){var c=Ge(s);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var p=an(c.pendingLanes);if(p!==0){var b=c;for(b.pendingLanes|=2,b.entangledLanes|=2;p;){var E=1<<31-At(p);b.entanglements[1]|=E,p&=~E}Pa(c),($e&6)===0&&(co=ea()+500,Hi(0))}}break;case 13:b=sr(c,2),b!==null&&sa(b,c,2),ho(),bd(c,2)}if(c=Sd(l),c===null&&od(e,t,l,Mo,a),c===s)break;s=c}s!==null&&l.stopPropagation()}else od(e,t,l,null,a)}}function Sd(e){return e=Ru(e),Nd(e)}var Mo=null;function Nd(e){if(Mo=null,e=Se(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=m(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Mo=e,null}function p0(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(wu()){case ys:return 2;case xs:return 8;case $l:case tn:return 32;case En:return 268435456;default:return 32}default:return 32}}var Ed=!1,Pn=null,Xn=null,Fn=null,Pi=new Map,Xi=new Map,Zn=[],s1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function y0(e,t){switch(e){case"focusin":case"focusout":Pn=null;break;case"dragenter":case"dragleave":Xn=null;break;case"mouseover":case"mouseout":Fn=null;break;case"pointerover":case"pointerout":Pi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Xi.delete(t.pointerId)}}function Fi(e,t,a,l,s,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:c,targetContainers:[s]},t!==null&&(t=Ge(t),t!==null&&g0(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function o1(e,t,a,l,s){switch(t){case"focusin":return Pn=Fi(Pn,e,t,a,l,s),!0;case"dragenter":return Xn=Fi(Xn,e,t,a,l,s),!0;case"mouseover":return Fn=Fi(Fn,e,t,a,l,s),!0;case"pointerover":var c=s.pointerId;return Pi.set(c,Fi(Pi.get(c)||null,e,t,a,l,s)),!0;case"gotpointercapture":return c=s.pointerId,Xi.set(c,Fi(Xi.get(c)||null,e,t,a,l,s)),!0}return!1}function x0(e){var t=Se(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=m(a),t!==null){e.blockedOn=t,k(e.priority,function(){if(a.tag===13){var l=ia();l=ei(l);var s=sr(a,l);s!==null&&sa(s,a,l),bd(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ao(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Sd(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);Tu=l,a.target.dispatchEvent(l),Tu=null}else return t=Ge(a),t!==null&&g0(t),e.blockedOn=a,!1;t.shift()}return!0}function b0(e,t,a){Ao(e)&&a.delete(t)}function u1(){Ed=!1,Pn!==null&&Ao(Pn)&&(Pn=null),Xn!==null&&Ao(Xn)&&(Xn=null),Fn!==null&&Ao(Fn)&&(Fn=null),Pi.forEach(b0),Xi.forEach(b0)}function Co(e,t){e.blockedOn===t&&(e.blockedOn=null,Ed||(Ed=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,u1)))}var Do=null;function w0(e){Do!==e&&(Do=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Do===e&&(Do=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],s=e[t+2];if(typeof l!="function"){if(Nd(l||a)===null)continue;break}var c=Ge(a);c!==null&&(e.splice(t,3),t-=3,Nc(c,{pending:!0,data:s,method:a.method,action:l},l,s))}}))}function Zi(e){function t(E){return Co(E,e)}Pn!==null&&Co(Pn,e),Xn!==null&&Co(Xn,e),Fn!==null&&Co(Fn,e),Pi.forEach(t),Xi.forEach(t);for(var a=0;a<Zn.length;a++){var l=Zn[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Zn.length&&(a=Zn[0],a.blockedOn===null);)x0(a),a.blockedOn===null&&Zn.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var s=a[l],c=a[l+1],p=s[ne]||null;if(typeof c=="function")p||w0(a);else if(p){var b=null;if(c&&c.hasAttribute("formAction")){if(s=c,p=c[ne]||null)b=p.formAction;else if(Nd(s)!==null)continue}else b=p.action;typeof b=="function"?a[l+1]=b:(a.splice(l,3),l-=3),w0(a)}}}function jd(e){this._internalRoot=e}Oo.prototype.render=jd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));var a=t.current,l=ia();m0(a,l,e,t,null,null)},Oo.prototype.unmount=jd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;m0(e.current,2,null,e,null,null),ho(),t[ce]=null}};function Oo(e){this._internalRoot=e}Oo.prototype.unstable_scheduleHydration=function(e){if(e){var t=A();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Zn.length&&t!==0&&t<Zn[a].priority;a++);Zn.splice(a,0,e),a===0&&x0(e)}};var S0=r.version;if(S0!=="19.1.0")throw Error(o(527,S0,"19.1.0"));ae.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=v(t),e=e!==null?g(e):null,e=e===null?null:e.stateNode,e};var c1={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:L,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var zo=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!zo.isDisabled&&zo.supportsFiber)try{Pt=zo.inject(c1),ut=zo}catch{}}return Ji.createRoot=function(e,t){if(!d(e))throw Error(o(299));var a=!1,l="",s=Hm,c=Bm,p=qm,b=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(s=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(p=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(b=t.unstable_transitionCallbacks)),t=f0(e,1,!1,null,null,a,l,s,c,p,b,null),e[ce]=t.current,sd(e),new jd(t)},Ji.hydrateRoot=function(e,t,a){if(!d(e))throw Error(o(299));var l=!1,s="",c=Hm,p=Bm,b=qm,E=null,H=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(s=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(p=a.onCaughtError),a.onRecoverableError!==void 0&&(b=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(E=a.unstable_transitionCallbacks),a.formState!==void 0&&(H=a.formState)),t=f0(e,1,!0,t,a??null,l,s,c,p,b,E,H),t.context=h0(null),a=t.current,l=ia(),l=ei(l),s=On(l),s.callback=null,zn(a,s,l),a=l,t.current.lanes=a,vl(t,a),Pa(t),e[ce]=t.current,sd(e),new Oo(t)},Ji.version="19.1.0",Ji}var z0;function b1(){if(z0)return Md.exports;z0=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(r){console.error(r)}}return n(),Md.exports=x1(),Md.exports}var w1=b1(),fu=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(n){return this.listeners.add(n),this.onSubscribe(),()=>{this.listeners.delete(n),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},hu=typeof window>"u"||"Deno"in globalThis;function ka(){}function S1(n,r){return typeof n=="function"?n(r):n}function N1(n){return typeof n=="number"&&n>=0&&n!==1/0}function E1(n,r){return Math.max(n+(r||0)-Date.now(),0)}function Jd(n,r){return typeof n=="function"?n(r):n}function j1(n,r){return typeof n=="function"?n(r):n}function _0(n,r){const{type:i="all",exact:o,fetchStatus:d,predicate:f,queryKey:m,stale:h}=n;if(m){if(o){if(r.queryHash!==pf(m,r.options))return!1}else if(!is(r.queryKey,m))return!1}if(i!=="all"){const v=r.isActive();if(i==="active"&&!v||i==="inactive"&&v)return!1}return!(typeof h=="boolean"&&r.isStale()!==h||d&&d!==r.state.fetchStatus||f&&!f(r))}function k0(n,r){const{exact:i,status:o,predicate:d,mutationKey:f}=n;if(f){if(!r.options.mutationKey)return!1;if(i){if(rs(r.options.mutationKey)!==rs(f))return!1}else if(!is(r.options.mutationKey,f))return!1}return!(o&&r.state.status!==o||d&&!d(r))}function pf(n,r){return((r==null?void 0:r.queryKeyHashFn)||rs)(n)}function rs(n){return JSON.stringify(n,(r,i)=>Id(i)?Object.keys(i).sort().reduce((o,d)=>(o[d]=i[d],o),{}):i)}function is(n,r){return n===r?!0:typeof n!=typeof r?!1:n&&r&&typeof n=="object"&&typeof r=="object"?Object.keys(r).every(i=>is(n[i],r[i])):!1}function Zg(n,r){if(n===r)return n;const i=L0(n)&&L0(r);if(i||Id(n)&&Id(r)){const o=i?n:Object.keys(n),d=o.length,f=i?r:Object.keys(r),m=f.length,h=i?[]:{},v=new Set(o);let g=0;for(let y=0;y<m;y++){const S=i?y:f[y];(!i&&v.has(S)||i)&&n[S]===void 0&&r[S]===void 0?(h[S]=void 0,g++):(h[S]=Zg(n[S],r[S]),h[S]===n[S]&&n[S]!==void 0&&g++)}return d===m&&g===d?n:h}return r}function L0(n){return Array.isArray(n)&&n.length===Object.keys(n).length}function Id(n){if(!U0(n))return!1;const r=n.constructor;if(r===void 0)return!0;const i=r.prototype;return!(!U0(i)||!i.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(n)!==Object.prototype)}function U0(n){return Object.prototype.toString.call(n)==="[object Object]"}function T1(n){return new Promise(r=>{setTimeout(r,n)})}function R1(n,r,i){return typeof i.structuralSharing=="function"?i.structuralSharing(n,r):i.structuralSharing!==!1?Zg(n,r):r}function M1(n,r,i=0){const o=[...n,r];return i&&o.length>i?o.slice(1):o}function A1(n,r,i=0){const o=[r,...n];return i&&o.length>i?o.slice(0,-1):o}var yf=Symbol();function $g(n,r){return!n.queryFn&&(r!=null&&r.initialPromise)?()=>r.initialPromise:!n.queryFn||n.queryFn===yf?()=>Promise.reject(new Error(`Missing queryFn: '${n.queryHash}'`)):n.queryFn}var Bl,ll,qr,Bg,C1=(Bg=class extends fu{constructor(){super();Qe(this,Bl);Qe(this,ll);Qe(this,qr);je(this,qr,r=>{if(!hu&&window.addEventListener){const i=()=>r();return window.addEventListener("visibilitychange",i,!1),()=>{window.removeEventListener("visibilitychange",i)}}})}onSubscribe(){Z(this,ll)||this.setEventListener(Z(this,qr))}onUnsubscribe(){var r;this.hasListeners()||((r=Z(this,ll))==null||r.call(this),je(this,ll,void 0))}setEventListener(r){var i;je(this,qr,r),(i=Z(this,ll))==null||i.call(this),je(this,ll,r(o=>{typeof o=="boolean"?this.setFocused(o):this.onFocus()}))}setFocused(r){Z(this,Bl)!==r&&(je(this,Bl,r),this.onFocus())}onFocus(){const r=this.isFocused();this.listeners.forEach(i=>{i(r)})}isFocused(){var r;return typeof Z(this,Bl)=="boolean"?Z(this,Bl):((r=globalThis.document)==null?void 0:r.visibilityState)!=="hidden"}},Bl=new WeakMap,ll=new WeakMap,qr=new WeakMap,Bg),Jg=new C1,Gr,rl,Yr,qg,D1=(qg=class extends fu{constructor(){super();Qe(this,Gr,!0);Qe(this,rl);Qe(this,Yr);je(this,Yr,r=>{if(!hu&&window.addEventListener){const i=()=>r(!0),o=()=>r(!1);return window.addEventListener("online",i,!1),window.addEventListener("offline",o,!1),()=>{window.removeEventListener("online",i),window.removeEventListener("offline",o)}}})}onSubscribe(){Z(this,rl)||this.setEventListener(Z(this,Yr))}onUnsubscribe(){var r;this.hasListeners()||((r=Z(this,rl))==null||r.call(this),je(this,rl,void 0))}setEventListener(r){var i;je(this,Yr,r),(i=Z(this,rl))==null||i.call(this),je(this,rl,r(this.setOnline.bind(this)))}setOnline(r){Z(this,Gr)!==r&&(je(this,Gr,r),this.listeners.forEach(o=>{o(r)}))}isOnline(){return Z(this,Gr)}},Gr=new WeakMap,rl=new WeakMap,Yr=new WeakMap,qg),eu=new D1;function O1(){let n,r;const i=new Promise((d,f)=>{n=d,r=f});i.status="pending",i.catch(()=>{});function o(d){Object.assign(i,d),delete i.resolve,delete i.reject}return i.resolve=d=>{o({status:"fulfilled",value:d}),n(d)},i.reject=d=>{o({status:"rejected",reason:d}),r(d)},i}function z1(n){return Math.min(1e3*2**n,3e4)}function Ig(n){return(n??"online")==="online"?eu.isOnline():!0}var Wg=class extends Error{constructor(n){super("CancelledError"),this.revert=n==null?void 0:n.revert,this.silent=n==null?void 0:n.silent}};function zd(n){return n instanceof Wg}function ep(n){let r=!1,i=0,o=!1,d;const f=O1(),m=N=>{var C;o||(w(new Wg(N)),(C=n.abort)==null||C.call(n))},h=()=>{r=!0},v=()=>{r=!1},g=()=>Jg.isFocused()&&(n.networkMode==="always"||eu.isOnline())&&n.canRun(),y=()=>Ig(n.networkMode)&&n.canRun(),S=N=>{var C;o||(o=!0,(C=n.onSuccess)==null||C.call(n,N),d==null||d(),f.resolve(N))},w=N=>{var C;o||(o=!0,(C=n.onError)==null||C.call(n,N),d==null||d(),f.reject(N))},T=()=>new Promise(N=>{var C;d=M=>{(o||g())&&N(M)},(C=n.onPause)==null||C.call(n)}).then(()=>{var N;d=void 0,o||(N=n.onContinue)==null||N.call(n)}),j=()=>{if(o)return;let N;const C=i===0?n.initialPromise:void 0;try{N=C??n.fn()}catch(M){N=Promise.reject(M)}Promise.resolve(N).then(S).catch(M=>{var O;if(o)return;const P=n.retry??(hu?0:3),_=n.retryDelay??z1,V=typeof _=="function"?_(i,M):_,q=P===!0||typeof P=="number"&&i<P||typeof P=="function"&&P(i,M);if(r||!q){w(M);return}i++,(O=n.onFail)==null||O.call(n,i,M),T1(V).then(()=>g()?void 0:T()).then(()=>{r?w(M):j()})})};return{promise:f,cancel:m,continue:()=>(d==null||d(),f),cancelRetry:h,continueRetry:v,canStart:y,start:()=>(y()?j():T().then(j),f)}}var _1=n=>setTimeout(n,0);function k1(){let n=[],r=0,i=h=>{h()},o=h=>{h()},d=_1;const f=h=>{r?n.push(h):d(()=>{i(h)})},m=()=>{const h=n;n=[],h.length&&d(()=>{o(()=>{h.forEach(v=>{i(v)})})})};return{batch:h=>{let v;r++;try{v=h()}finally{r--,r||m()}return v},batchCalls:h=>(...v)=>{f(()=>{h(...v)})},schedule:f,setNotifyFunction:h=>{i=h},setBatchNotifyFunction:h=>{o=h},setScheduler:h=>{d=h}}}var Vt=k1(),ql,Gg,tp=(Gg=class{constructor(){Qe(this,ql)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),N1(this.gcTime)&&je(this,ql,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(n){this.gcTime=Math.max(this.gcTime||0,n??(hu?1/0:5*60*1e3))}clearGcTimeout(){Z(this,ql)&&(clearTimeout(Z(this,ql)),je(this,ql,void 0))}},ql=new WeakMap,Gg),Vr,Gl,ba,Yl,Ut,cs,Vl,La,bn,Yg,L1=(Yg=class extends tp{constructor(r){super();Qe(this,La);Qe(this,Vr);Qe(this,Gl);Qe(this,ba);Qe(this,Yl);Qe(this,Ut);Qe(this,cs);Qe(this,Vl);je(this,Vl,!1),je(this,cs,r.defaultOptions),this.setOptions(r.options),this.observers=[],je(this,Yl,r.client),je(this,ba,Z(this,Yl).getQueryCache()),this.queryKey=r.queryKey,this.queryHash=r.queryHash,je(this,Vr,H1(this.options)),this.state=r.state??Z(this,Vr),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var r;return(r=Z(this,Ut))==null?void 0:r.promise}setOptions(r){this.options={...Z(this,cs),...r},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&Z(this,ba).remove(this)}setData(r,i){const o=R1(this.state.data,r,this.options);return kt(this,La,bn).call(this,{data:o,type:"success",dataUpdatedAt:i==null?void 0:i.updatedAt,manual:i==null?void 0:i.manual}),o}setState(r,i){kt(this,La,bn).call(this,{type:"setState",state:r,setStateOptions:i})}cancel(r){var o,d;const i=(o=Z(this,Ut))==null?void 0:o.promise;return(d=Z(this,Ut))==null||d.cancel(r),i?i.then(ka).catch(ka):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(Z(this,Vr))}isActive(){return this.observers.some(r=>j1(r.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===yf||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(r=>Jd(r.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(r=>r.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(r=0){return this.state.data===void 0?!0:r==="static"?!1:this.state.isInvalidated?!0:!E1(this.state.dataUpdatedAt,r)}onFocus(){var i;const r=this.observers.find(o=>o.shouldFetchOnWindowFocus());r==null||r.refetch({cancelRefetch:!1}),(i=Z(this,Ut))==null||i.continue()}onOnline(){var i;const r=this.observers.find(o=>o.shouldFetchOnReconnect());r==null||r.refetch({cancelRefetch:!1}),(i=Z(this,Ut))==null||i.continue()}addObserver(r){this.observers.includes(r)||(this.observers.push(r),this.clearGcTimeout(),Z(this,ba).notify({type:"observerAdded",query:this,observer:r}))}removeObserver(r){this.observers.includes(r)&&(this.observers=this.observers.filter(i=>i!==r),this.observers.length||(Z(this,Ut)&&(Z(this,Vl)?Z(this,Ut).cancel({revert:!0}):Z(this,Ut).cancelRetry()),this.scheduleGc()),Z(this,ba).notify({type:"observerRemoved",query:this,observer:r}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||kt(this,La,bn).call(this,{type:"invalidate"})}fetch(r,i){var g,y,S;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(i!=null&&i.cancelRefetch))this.cancel({silent:!0});else if(Z(this,Ut))return Z(this,Ut).continueRetry(),Z(this,Ut).promise}if(r&&this.setOptions(r),!this.options.queryFn){const w=this.observers.find(T=>T.options.queryFn);w&&this.setOptions(w.options)}const o=new AbortController,d=w=>{Object.defineProperty(w,"signal",{enumerable:!0,get:()=>(je(this,Vl,!0),o.signal)})},f=()=>{const w=$g(this.options,i),j=(()=>{const N={client:Z(this,Yl),queryKey:this.queryKey,meta:this.meta};return d(N),N})();return je(this,Vl,!1),this.options.persister?this.options.persister(w,j,this):w(j)},h=(()=>{const w={fetchOptions:i,options:this.options,queryKey:this.queryKey,client:Z(this,Yl),state:this.state,fetchFn:f};return d(w),w})();(g=this.options.behavior)==null||g.onFetch(h,this),je(this,Gl,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((y=h.fetchOptions)==null?void 0:y.meta))&&kt(this,La,bn).call(this,{type:"fetch",meta:(S=h.fetchOptions)==null?void 0:S.meta});const v=w=>{var T,j,N,C;zd(w)&&w.silent||kt(this,La,bn).call(this,{type:"error",error:w}),zd(w)||((j=(T=Z(this,ba).config).onError)==null||j.call(T,w,this),(C=(N=Z(this,ba).config).onSettled)==null||C.call(N,this.state.data,w,this)),this.scheduleGc()};return je(this,Ut,ep({initialPromise:i==null?void 0:i.initialPromise,fn:h.fetchFn,abort:o.abort.bind(o),onSuccess:w=>{var T,j,N,C;if(w===void 0){v(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(w)}catch(M){v(M);return}(j=(T=Z(this,ba).config).onSuccess)==null||j.call(T,w,this),(C=(N=Z(this,ba).config).onSettled)==null||C.call(N,w,this.state.error,this),this.scheduleGc()},onError:v,onFail:(w,T)=>{kt(this,La,bn).call(this,{type:"failed",failureCount:w,error:T})},onPause:()=>{kt(this,La,bn).call(this,{type:"pause"})},onContinue:()=>{kt(this,La,bn).call(this,{type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0})),Z(this,Ut).start()}},Vr=new WeakMap,Gl=new WeakMap,ba=new WeakMap,Yl=new WeakMap,Ut=new WeakMap,cs=new WeakMap,Vl=new WeakMap,La=new WeakSet,bn=function(r){const i=o=>{switch(r.type){case"failed":return{...o,fetchFailureCount:r.failureCount,fetchFailureReason:r.error};case"pause":return{...o,fetchStatus:"paused"};case"continue":return{...o,fetchStatus:"fetching"};case"fetch":return{...o,...U1(o.data,this.options),fetchMeta:r.meta??null};case"success":return je(this,Gl,void 0),{...o,data:r.data,dataUpdateCount:o.dataUpdateCount+1,dataUpdatedAt:r.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!r.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const d=r.error;return zd(d)&&d.revert&&Z(this,Gl)?{...Z(this,Gl),fetchStatus:"idle"}:{...o,error:d,errorUpdateCount:o.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:o.fetchFailureCount+1,fetchFailureReason:d,fetchStatus:"idle",status:"error"};case"invalidate":return{...o,isInvalidated:!0};case"setState":return{...o,...r.state}}};this.state=i(this.state),Vt.batch(()=>{this.observers.forEach(o=>{o.onQueryUpdate()}),Z(this,ba).notify({query:this,type:"updated",action:r})})},Yg);function U1(n,r){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ig(r.networkMode)?"fetching":"paused",...n===void 0&&{error:null,status:"pending"}}}function H1(n){const r=typeof n.initialData=="function"?n.initialData():n.initialData,i=r!==void 0,o=i?typeof n.initialDataUpdatedAt=="function"?n.initialDataUpdatedAt():n.initialDataUpdatedAt:0;return{data:r,dataUpdateCount:0,dataUpdatedAt:i?o??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}var Fa,Vg,B1=(Vg=class extends fu{constructor(r={}){super();Qe(this,Fa);this.config=r,je(this,Fa,new Map)}build(r,i,o){const d=i.queryKey,f=i.queryHash??pf(d,i);let m=this.get(f);return m||(m=new L1({client:r,queryKey:d,queryHash:f,options:r.defaultQueryOptions(i),state:o,defaultOptions:r.getQueryDefaults(d)}),this.add(m)),m}add(r){Z(this,Fa).has(r.queryHash)||(Z(this,Fa).set(r.queryHash,r),this.notify({type:"added",query:r}))}remove(r){const i=Z(this,Fa).get(r.queryHash);i&&(r.destroy(),i===r&&Z(this,Fa).delete(r.queryHash),this.notify({type:"removed",query:r}))}clear(){Vt.batch(()=>{this.getAll().forEach(r=>{this.remove(r)})})}get(r){return Z(this,Fa).get(r)}getAll(){return[...Z(this,Fa).values()]}find(r){const i={exact:!0,...r};return this.getAll().find(o=>_0(i,o))}findAll(r={}){const i=this.getAll();return Object.keys(r).length>0?i.filter(o=>_0(r,o)):i}notify(r){Vt.batch(()=>{this.listeners.forEach(i=>{i(r)})})}onFocus(){Vt.batch(()=>{this.getAll().forEach(r=>{r.onFocus()})})}onOnline(){Vt.batch(()=>{this.getAll().forEach(r=>{r.onOnline()})})}},Fa=new WeakMap,Vg),Za,Yt,Ql,$a,Wn,Qg,q1=(Qg=class extends tp{constructor(r){super();Qe(this,$a);Qe(this,Za);Qe(this,Yt);Qe(this,Ql);this.mutationId=r.mutationId,je(this,Yt,r.mutationCache),je(this,Za,[]),this.state=r.state||G1(),this.setOptions(r.options),this.scheduleGc()}setOptions(r){this.options=r,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(r){Z(this,Za).includes(r)||(Z(this,Za).push(r),this.clearGcTimeout(),Z(this,Yt).notify({type:"observerAdded",mutation:this,observer:r}))}removeObserver(r){je(this,Za,Z(this,Za).filter(i=>i!==r)),this.scheduleGc(),Z(this,Yt).notify({type:"observerRemoved",mutation:this,observer:r})}optionalRemove(){Z(this,Za).length||(this.state.status==="pending"?this.scheduleGc():Z(this,Yt).remove(this))}continue(){var r;return((r=Z(this,Ql))==null?void 0:r.continue())??this.execute(this.state.variables)}async execute(r){var f,m,h,v,g,y,S,w,T,j,N,C,M,P,_,V,q,O,te,K;const i=()=>{kt(this,$a,Wn).call(this,{type:"continue"})};je(this,Ql,ep({fn:()=>this.options.mutationFn?this.options.mutationFn(r):Promise.reject(new Error("No mutationFn found")),onFail:(W,ge)=>{kt(this,$a,Wn).call(this,{type:"failed",failureCount:W,error:ge})},onPause:()=>{kt(this,$a,Wn).call(this,{type:"pause"})},onContinue:i,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>Z(this,Yt).canRun(this)}));const o=this.state.status==="pending",d=!Z(this,Ql).canStart();try{if(o)i();else{kt(this,$a,Wn).call(this,{type:"pending",variables:r,isPaused:d}),await((m=(f=Z(this,Yt).config).onMutate)==null?void 0:m.call(f,r,this));const ge=await((v=(h=this.options).onMutate)==null?void 0:v.call(h,r));ge!==this.state.context&&kt(this,$a,Wn).call(this,{type:"pending",context:ge,variables:r,isPaused:d})}const W=await Z(this,Ql).start();return await((y=(g=Z(this,Yt).config).onSuccess)==null?void 0:y.call(g,W,r,this.state.context,this)),await((w=(S=this.options).onSuccess)==null?void 0:w.call(S,W,r,this.state.context)),await((j=(T=Z(this,Yt).config).onSettled)==null?void 0:j.call(T,W,null,this.state.variables,this.state.context,this)),await((C=(N=this.options).onSettled)==null?void 0:C.call(N,W,null,r,this.state.context)),kt(this,$a,Wn).call(this,{type:"success",data:W}),W}catch(W){try{throw await((P=(M=Z(this,Yt).config).onError)==null?void 0:P.call(M,W,r,this.state.context,this)),await((V=(_=this.options).onError)==null?void 0:V.call(_,W,r,this.state.context)),await((O=(q=Z(this,Yt).config).onSettled)==null?void 0:O.call(q,void 0,W,this.state.variables,this.state.context,this)),await((K=(te=this.options).onSettled)==null?void 0:K.call(te,void 0,W,r,this.state.context)),W}finally{kt(this,$a,Wn).call(this,{type:"error",error:W})}}finally{Z(this,Yt).runNext(this)}}},Za=new WeakMap,Yt=new WeakMap,Ql=new WeakMap,$a=new WeakSet,Wn=function(r){const i=o=>{switch(r.type){case"failed":return{...o,failureCount:r.failureCount,failureReason:r.error};case"pause":return{...o,isPaused:!0};case"continue":return{...o,isPaused:!1};case"pending":return{...o,context:r.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:r.isPaused,status:"pending",variables:r.variables,submittedAt:Date.now()};case"success":return{...o,data:r.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...o,data:void 0,error:r.error,failureCount:o.failureCount+1,failureReason:r.error,isPaused:!1,status:"error"}}};this.state=i(this.state),Vt.batch(()=>{Z(this,Za).forEach(o=>{o.onMutationUpdate(r)}),Z(this,Yt).notify({mutation:this,type:"updated",action:r})})},Qg);function G1(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Sn,Ua,ds,Kg,Y1=(Kg=class extends fu{constructor(r={}){super();Qe(this,Sn);Qe(this,Ua);Qe(this,ds);this.config=r,je(this,Sn,new Set),je(this,Ua,new Map),je(this,ds,0)}build(r,i,o){const d=new q1({mutationCache:this,mutationId:++_o(this,ds)._,options:r.defaultMutationOptions(i),state:o});return this.add(d),d}add(r){Z(this,Sn).add(r);const i=ko(r);if(typeof i=="string"){const o=Z(this,Ua).get(i);o?o.push(r):Z(this,Ua).set(i,[r])}this.notify({type:"added",mutation:r})}remove(r){if(Z(this,Sn).delete(r)){const i=ko(r);if(typeof i=="string"){const o=Z(this,Ua).get(i);if(o)if(o.length>1){const d=o.indexOf(r);d!==-1&&o.splice(d,1)}else o[0]===r&&Z(this,Ua).delete(i)}}this.notify({type:"removed",mutation:r})}canRun(r){const i=ko(r);if(typeof i=="string"){const o=Z(this,Ua).get(i),d=o==null?void 0:o.find(f=>f.state.status==="pending");return!d||d===r}else return!0}runNext(r){var o;const i=ko(r);if(typeof i=="string"){const d=(o=Z(this,Ua).get(i))==null?void 0:o.find(f=>f!==r&&f.state.isPaused);return(d==null?void 0:d.continue())??Promise.resolve()}else return Promise.resolve()}clear(){Vt.batch(()=>{Z(this,Sn).forEach(r=>{this.notify({type:"removed",mutation:r})}),Z(this,Sn).clear(),Z(this,Ua).clear()})}getAll(){return Array.from(Z(this,Sn))}find(r){const i={exact:!0,...r};return this.getAll().find(o=>k0(i,o))}findAll(r={}){return this.getAll().filter(i=>k0(r,i))}notify(r){Vt.batch(()=>{this.listeners.forEach(i=>{i(r)})})}resumePausedMutations(){const r=this.getAll().filter(i=>i.state.isPaused);return Vt.batch(()=>Promise.all(r.map(i=>i.continue().catch(ka))))}},Sn=new WeakMap,Ua=new WeakMap,ds=new WeakMap,Kg);function ko(n){var r;return(r=n.options.scope)==null?void 0:r.id}function H0(n){return{onFetch:(r,i)=>{var y,S,w,T,j;const o=r.options,d=(w=(S=(y=r.fetchOptions)==null?void 0:y.meta)==null?void 0:S.fetchMore)==null?void 0:w.direction,f=((T=r.state.data)==null?void 0:T.pages)||[],m=((j=r.state.data)==null?void 0:j.pageParams)||[];let h={pages:[],pageParams:[]},v=0;const g=async()=>{let N=!1;const C=_=>{Object.defineProperty(_,"signal",{enumerable:!0,get:()=>(r.signal.aborted?N=!0:r.signal.addEventListener("abort",()=>{N=!0}),r.signal)})},M=$g(r.options,r.fetchOptions),P=async(_,V,q)=>{if(N)return Promise.reject();if(V==null&&_.pages.length)return Promise.resolve(_);const te=(()=>{const Le={client:r.client,queryKey:r.queryKey,pageParam:V,direction:q?"backward":"forward",meta:r.options.meta};return C(Le),Le})(),K=await M(te),{maxPages:W}=r.options,ge=q?A1:M1;return{pages:ge(_.pages,K,W),pageParams:ge(_.pageParams,V,W)}};if(d&&f.length){const _=d==="backward",V=_?V1:B0,q={pages:f,pageParams:m},O=V(o,q);h=await P(q,O,_)}else{const _=n??f.length;do{const V=v===0?m[0]??o.initialPageParam:B0(o,h);if(v>0&&V==null)break;h=await P(h,V),v++}while(v<_)}return h};r.options.persister?r.fetchFn=()=>{var N,C;return(C=(N=r.options).persister)==null?void 0:C.call(N,g,{client:r.client,queryKey:r.queryKey,meta:r.options.meta,signal:r.signal},i)}:r.fetchFn=g}}}function B0(n,{pages:r,pageParams:i}){const o=r.length-1;return r.length>0?n.getNextPageParam(r[o],r,i[o],i):void 0}function V1(n,{pages:r,pageParams:i}){var o;return r.length>0?(o=n.getPreviousPageParam)==null?void 0:o.call(n,r[0],r,i[0],i):void 0}var mt,il,sl,Qr,Kr,ol,Pr,Xr,Pg,Q1=(Pg=class{constructor(n={}){Qe(this,mt);Qe(this,il);Qe(this,sl);Qe(this,Qr);Qe(this,Kr);Qe(this,ol);Qe(this,Pr);Qe(this,Xr);je(this,mt,n.queryCache||new B1),je(this,il,n.mutationCache||new Y1),je(this,sl,n.defaultOptions||{}),je(this,Qr,new Map),je(this,Kr,new Map),je(this,ol,0)}mount(){_o(this,ol)._++,Z(this,ol)===1&&(je(this,Pr,Jg.subscribe(async n=>{n&&(await this.resumePausedMutations(),Z(this,mt).onFocus())})),je(this,Xr,eu.subscribe(async n=>{n&&(await this.resumePausedMutations(),Z(this,mt).onOnline())})))}unmount(){var n,r;_o(this,ol)._--,Z(this,ol)===0&&((n=Z(this,Pr))==null||n.call(this),je(this,Pr,void 0),(r=Z(this,Xr))==null||r.call(this),je(this,Xr,void 0))}isFetching(n){return Z(this,mt).findAll({...n,fetchStatus:"fetching"}).length}isMutating(n){return Z(this,il).findAll({...n,status:"pending"}).length}getQueryData(n){var i;const r=this.defaultQueryOptions({queryKey:n});return(i=Z(this,mt).get(r.queryHash))==null?void 0:i.state.data}ensureQueryData(n){const r=this.defaultQueryOptions(n),i=Z(this,mt).build(this,r),o=i.state.data;return o===void 0?this.fetchQuery(n):(n.revalidateIfStale&&i.isStaleByTime(Jd(r.staleTime,i))&&this.prefetchQuery(r),Promise.resolve(o))}getQueriesData(n){return Z(this,mt).findAll(n).map(({queryKey:r,state:i})=>{const o=i.data;return[r,o]})}setQueryData(n,r,i){const o=this.defaultQueryOptions({queryKey:n}),d=Z(this,mt).get(o.queryHash),f=d==null?void 0:d.state.data,m=S1(r,f);if(m!==void 0)return Z(this,mt).build(this,o).setData(m,{...i,manual:!0})}setQueriesData(n,r,i){return Vt.batch(()=>Z(this,mt).findAll(n).map(({queryKey:o})=>[o,this.setQueryData(o,r,i)]))}getQueryState(n){var i;const r=this.defaultQueryOptions({queryKey:n});return(i=Z(this,mt).get(r.queryHash))==null?void 0:i.state}removeQueries(n){const r=Z(this,mt);Vt.batch(()=>{r.findAll(n).forEach(i=>{r.remove(i)})})}resetQueries(n,r){const i=Z(this,mt);return Vt.batch(()=>(i.findAll(n).forEach(o=>{o.reset()}),this.refetchQueries({type:"active",...n},r)))}cancelQueries(n,r={}){const i={revert:!0,...r},o=Vt.batch(()=>Z(this,mt).findAll(n).map(d=>d.cancel(i)));return Promise.all(o).then(ka).catch(ka)}invalidateQueries(n,r={}){return Vt.batch(()=>(Z(this,mt).findAll(n).forEach(i=>{i.invalidate()}),(n==null?void 0:n.refetchType)==="none"?Promise.resolve():this.refetchQueries({...n,type:(n==null?void 0:n.refetchType)??(n==null?void 0:n.type)??"active"},r)))}refetchQueries(n,r={}){const i={...r,cancelRefetch:r.cancelRefetch??!0},o=Vt.batch(()=>Z(this,mt).findAll(n).filter(d=>!d.isDisabled()&&!d.isStatic()).map(d=>{let f=d.fetch(void 0,i);return i.throwOnError||(f=f.catch(ka)),d.state.fetchStatus==="paused"?Promise.resolve():f}));return Promise.all(o).then(ka)}fetchQuery(n){const r=this.defaultQueryOptions(n);r.retry===void 0&&(r.retry=!1);const i=Z(this,mt).build(this,r);return i.isStaleByTime(Jd(r.staleTime,i))?i.fetch(r):Promise.resolve(i.state.data)}prefetchQuery(n){return this.fetchQuery(n).then(ka).catch(ka)}fetchInfiniteQuery(n){return n.behavior=H0(n.pages),this.fetchQuery(n)}prefetchInfiniteQuery(n){return this.fetchInfiniteQuery(n).then(ka).catch(ka)}ensureInfiniteQueryData(n){return n.behavior=H0(n.pages),this.ensureQueryData(n)}resumePausedMutations(){return eu.isOnline()?Z(this,il).resumePausedMutations():Promise.resolve()}getQueryCache(){return Z(this,mt)}getMutationCache(){return Z(this,il)}getDefaultOptions(){return Z(this,sl)}setDefaultOptions(n){je(this,sl,n)}setQueryDefaults(n,r){Z(this,Qr).set(rs(n),{queryKey:n,defaultOptions:r})}getQueryDefaults(n){const r=[...Z(this,Qr).values()],i={};return r.forEach(o=>{is(n,o.queryKey)&&Object.assign(i,o.defaultOptions)}),i}setMutationDefaults(n,r){Z(this,Kr).set(rs(n),{mutationKey:n,defaultOptions:r})}getMutationDefaults(n){const r=[...Z(this,Kr).values()],i={};return r.forEach(o=>{is(n,o.mutationKey)&&Object.assign(i,o.defaultOptions)}),i}defaultQueryOptions(n){if(n._defaulted)return n;const r={...Z(this,sl).queries,...this.getQueryDefaults(n.queryKey),...n,_defaulted:!0};return r.queryHash||(r.queryHash=pf(r.queryKey,r)),r.refetchOnReconnect===void 0&&(r.refetchOnReconnect=r.networkMode!=="always"),r.throwOnError===void 0&&(r.throwOnError=!!r.suspense),!r.networkMode&&r.persister&&(r.networkMode="offlineFirst"),r.queryFn===yf&&(r.enabled=!1),r}defaultMutationOptions(n){return n!=null&&n._defaulted?n:{...Z(this,sl).mutations,...(n==null?void 0:n.mutationKey)&&this.getMutationDefaults(n.mutationKey),...n,_defaulted:!0}}clear(){Z(this,mt).clear(),Z(this,il).clear()}},mt=new WeakMap,il=new WeakMap,sl=new WeakMap,Qr=new WeakMap,Kr=new WeakMap,ol=new WeakMap,Pr=new WeakMap,Xr=new WeakMap,Pg),x=du();const wn=Xg(x),ap=f1({__proto__:null,default:wn},[x]);var K1=x.createContext(void 0),P1=({client:n,children:r})=>(x.useEffect(()=>(n.mount(),()=>{n.unmount()}),[n]),u.jsx(K1.Provider,{value:n,children:r}));const X1=({children:n})=>{const[r]=x.useState(()=>new Q1({defaultOptions:{queries:{staleTime:6e4,gcTime:3e5,retry:(i,o)=>(o==null?void 0:o.status)===401||(o==null?void 0:o.status)===403?!1:i<3},mutations:{retry:!1}}}));return u.jsxs(P1,{client:r,children:[n,!1]})};var Ii={},q0;function F1(){if(q0)return Ii;q0=1,Object.defineProperty(Ii,"__esModule",{value:!0}),Ii.parse=m,Ii.serialize=g;const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,d=Object.prototype.toString,f=(()=>{const w=function(){};return w.prototype=Object.create(null),w})();function m(w,T){const j=new f,N=w.length;if(N<2)return j;const C=(T==null?void 0:T.decode)||y;let M=0;do{const P=w.indexOf("=",M);if(P===-1)break;const _=w.indexOf(";",M),V=_===-1?N:_;if(P>V){M=w.lastIndexOf(";",P-1)+1;continue}const q=h(w,M,P),O=v(w,P,q),te=w.slice(q,O);if(j[te]===void 0){let K=h(w,P+1,V),W=v(w,V,K);const ge=C(w.slice(K,W));j[te]=ge}M=V+1}while(M<N);return j}function h(w,T,j){do{const N=w.charCodeAt(T);if(N!==32&&N!==9)return T}while(++T<j);return j}function v(w,T,j){for(;T>j;){const N=w.charCodeAt(--T);if(N!==32&&N!==9)return T+1}return j}function g(w,T,j){const N=(j==null?void 0:j.encode)||encodeURIComponent;if(!n.test(w))throw new TypeError(`argument name is invalid: ${w}`);const C=N(T);if(!r.test(C))throw new TypeError(`argument val is invalid: ${T}`);let M=w+"="+C;if(!j)return M;if(j.maxAge!==void 0){if(!Number.isInteger(j.maxAge))throw new TypeError(`option maxAge is invalid: ${j.maxAge}`);M+="; Max-Age="+j.maxAge}if(j.domain){if(!i.test(j.domain))throw new TypeError(`option domain is invalid: ${j.domain}`);M+="; Domain="+j.domain}if(j.path){if(!o.test(j.path))throw new TypeError(`option path is invalid: ${j.path}`);M+="; Path="+j.path}if(j.expires){if(!S(j.expires)||!Number.isFinite(j.expires.valueOf()))throw new TypeError(`option expires is invalid: ${j.expires}`);M+="; Expires="+j.expires.toUTCString()}if(j.httpOnly&&(M+="; HttpOnly"),j.secure&&(M+="; Secure"),j.partitioned&&(M+="; Partitioned"),j.priority)switch(typeof j.priority=="string"?j.priority.toLowerCase():void 0){case"low":M+="; Priority=Low";break;case"medium":M+="; Priority=Medium";break;case"high":M+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${j.priority}`)}if(j.sameSite)switch(typeof j.sameSite=="string"?j.sameSite.toLowerCase():j.sameSite){case!0:case"strict":M+="; SameSite=Strict";break;case"lax":M+="; SameSite=Lax";break;case"none":M+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${j.sameSite}`)}return M}function y(w){if(w.indexOf("%")===-1)return w;try{return decodeURIComponent(w)}catch{return w}}function S(w){return d.call(w)==="[object Date]"}return Ii}F1();/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var np=n=>{throw TypeError(n)},Z1=(n,r,i)=>r.has(n)||np("Cannot "+i),_d=(n,r,i)=>(Z1(n,r,"read from private field"),i?i.call(n):r.get(n)),$1=(n,r,i)=>r.has(n)?np("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(n):r.set(n,i),G0="popstate";function J1(n={}){function r(o,d){let{pathname:f,search:m,hash:h}=o.location;return ss("",{pathname:f,search:m,hash:h},d.state&&d.state.usr||null,d.state&&d.state.key||"default")}function i(o,d){return typeof d=="string"?d:cl(d)}return W1(r,i,null,n)}function ke(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}function bt(n,r){if(!n){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function I1(){return Math.random().toString(36).substring(2,10)}function Y0(n,r){return{usr:n.state,key:n.key,idx:r}}function ss(n,r,i=null,o){return{pathname:typeof n=="string"?n:n.pathname,search:"",hash:"",...typeof r=="string"?dl(r):r,state:i,key:r&&r.key||o||I1()}}function cl({pathname:n="/",search:r="",hash:i=""}){return r&&r!=="?"&&(n+=r.charAt(0)==="?"?r:"?"+r),i&&i!=="#"&&(n+=i.charAt(0)==="#"?i:"#"+i),n}function dl(n){let r={};if(n){let i=n.indexOf("#");i>=0&&(r.hash=n.substring(i),n=n.substring(0,i));let o=n.indexOf("?");o>=0&&(r.search=n.substring(o),n=n.substring(0,o)),n&&(r.pathname=n)}return r}function W1(n,r,i,o={}){let{window:d=document.defaultView,v5Compat:f=!1}=o,m=d.history,h="POP",v=null,g=y();g==null&&(g=0,m.replaceState({...m.state,idx:g},""));function y(){return(m.state||{idx:null}).idx}function S(){h="POP";let C=y(),M=C==null?null:C-g;g=C,v&&v({action:h,location:N.location,delta:M})}function w(C,M){h="PUSH";let P=ss(N.location,C,M);g=y()+1;let _=Y0(P,g),V=N.createHref(P);try{m.pushState(_,"",V)}catch(q){if(q instanceof DOMException&&q.name==="DataCloneError")throw q;d.location.assign(V)}f&&v&&v({action:h,location:N.location,delta:1})}function T(C,M){h="REPLACE";let P=ss(N.location,C,M);g=y();let _=Y0(P,g),V=N.createHref(P);m.replaceState(_,"",V),f&&v&&v({action:h,location:N.location,delta:0})}function j(C){return lp(C)}let N={get action(){return h},get location(){return n(d,m)},listen(C){if(v)throw new Error("A history only accepts one active listener");return d.addEventListener(G0,S),v=C,()=>{d.removeEventListener(G0,S),v=null}},createHref(C){return r(d,C)},createURL:j,encodeLocation(C){let M=j(C);return{pathname:M.pathname,search:M.search,hash:M.hash}},push:w,replace:T,go(C){return m.go(C)}};return N}function lp(n,r=!1){let i="http://localhost";typeof window<"u"&&(i=window.location.origin!=="null"?window.location.origin:window.location.href),ke(i,"No window.location.(origin|href) available to create URL");let o=typeof n=="string"?n:cl(n);return o=o.replace(/ $/,"%20"),!r&&o.startsWith("//")&&(o=i+o),new URL(o,i)}var ns,V0=class{constructor(n){if($1(this,ns,new Map),n)for(let[r,i]of n)this.set(r,i)}get(n){if(_d(this,ns).has(n))return _d(this,ns).get(n);if(n.defaultValue!==void 0)return n.defaultValue;throw new Error("No value found for context")}set(n,r){_d(this,ns).set(n,r)}};ns=new WeakMap;var e2=new Set(["lazy","caseSensitive","path","id","index","children"]);function t2(n){return e2.has(n)}var a2=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function n2(n){return a2.has(n)}function l2(n){return n.index===!0}function tu(n,r,i=[],o={}){return n.map((d,f)=>{let m=[...i,String(f)],h=typeof d.id=="string"?d.id:m.join("-");if(ke(d.index!==!0||!d.children,"Cannot specify children on an index route"),ke(!o[h],`Found a route id collision on id "${h}".  Route id's must be globally unique within Data Router usages`),l2(d)){let v={...d,...r(d),id:h};return o[h]=v,v}else{let v={...d,...r(d),id:h,children:void 0};return o[h]=v,d.children&&(v.children=tu(d.children,r,m,o)),v}})}function al(n,r,i="/"){return Xo(n,r,i,!1)}function Xo(n,r,i,o){let d=typeof r=="string"?dl(r):r,f=ja(d.pathname||"/",i);if(f==null)return null;let m=rp(n);i2(m);let h=null;for(let v=0;h==null&&v<m.length;++v){let g=p2(f);h=v2(m[v],g,o)}return h}function r2(n,r){let{route:i,pathname:o,params:d}=n;return{id:i.id,pathname:o,params:d,data:r[i.id],handle:i.handle}}function rp(n,r=[],i=[],o=""){let d=(f,m,h)=>{let v={relativePath:h===void 0?f.path||"":h,caseSensitive:f.caseSensitive===!0,childrenIndex:m,route:f};v.relativePath.startsWith("/")&&(ke(v.relativePath.startsWith(o),`Absolute route path "${v.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),v.relativePath=v.relativePath.slice(o.length));let g=Ia([o,v.relativePath]),y=i.concat(v);f.children&&f.children.length>0&&(ke(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${g}".`),rp(f.children,r,y,g)),!(f.path==null&&!f.index)&&r.push({path:g,score:h2(g,f.index),routesMeta:y})};return n.forEach((f,m)=>{var h;if(f.path===""||!((h=f.path)!=null&&h.includes("?")))d(f,m);else for(let v of ip(f.path))d(f,m,v)}),r}function ip(n){let r=n.split("/");if(r.length===0)return[];let[i,...o]=r,d=i.endsWith("?"),f=i.replace(/\?$/,"");if(o.length===0)return d?[f,""]:[f];let m=ip(o.join("/")),h=[];return h.push(...m.map(v=>v===""?f:[f,v].join("/"))),d&&h.push(...m),h.map(v=>n.startsWith("/")&&v===""?"/":v)}function i2(n){n.sort((r,i)=>r.score!==i.score?i.score-r.score:m2(r.routesMeta.map(o=>o.childrenIndex),i.routesMeta.map(o=>o.childrenIndex)))}var s2=/^:[\w-]+$/,o2=3,u2=2,c2=1,d2=10,f2=-2,Q0=n=>n==="*";function h2(n,r){let i=n.split("/"),o=i.length;return i.some(Q0)&&(o+=f2),r&&(o+=u2),i.filter(d=>!Q0(d)).reduce((d,f)=>d+(s2.test(f)?o2:f===""?c2:d2),o)}function m2(n,r){return n.length===r.length&&n.slice(0,-1).every((o,d)=>o===r[d])?n[n.length-1]-r[r.length-1]:0}function v2(n,r,i=!1){let{routesMeta:o}=n,d={},f="/",m=[];for(let h=0;h<o.length;++h){let v=o[h],g=h===o.length-1,y=f==="/"?r:r.slice(f.length)||"/",S=au({path:v.relativePath,caseSensitive:v.caseSensitive,end:g},y),w=v.route;if(!S&&g&&i&&!o[o.length-1].route.index&&(S=au({path:v.relativePath,caseSensitive:v.caseSensitive,end:!1},y)),!S)return null;Object.assign(d,S.params),m.push({params:d,pathname:Ia([f,S.pathname]),pathnameBase:b2(Ia([f,S.pathnameBase])),route:w}),S.pathnameBase!=="/"&&(f=Ia([f,S.pathnameBase]))}return m}function au(n,r){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[i,o]=g2(n.path,n.caseSensitive,n.end),d=r.match(i);if(!d)return null;let f=d[0],m=f.replace(/(.)\/+$/,"$1"),h=d.slice(1);return{params:o.reduce((g,{paramName:y,isOptional:S},w)=>{if(y==="*"){let j=h[w]||"";m=f.slice(0,f.length-j.length).replace(/(.)\/+$/,"$1")}const T=h[w];return S&&!T?g[y]=void 0:g[y]=(T||"").replace(/%2F/g,"/"),g},{}),pathname:f,pathnameBase:m,pattern:n}}function g2(n,r=!1,i=!0){bt(n==="*"||!n.endsWith("*")||n.endsWith("/*"),`Route path "${n}" will be treated as if it were "${n.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${n.replace(/\*$/,"/*")}".`);let o=[],d="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(m,h,v)=>(o.push({paramName:h,isOptional:v!=null}),v?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(o.push({paramName:"*"}),d+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):i?d+="\\/*$":n!==""&&n!=="/"&&(d+="(?:(?=\\/|$))"),[new RegExp(d,r?void 0:"i"),o]}function p2(n){try{return n.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return bt(!1,`The URL path "${n}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),n}}function ja(n,r){if(r==="/")return n;if(!n.toLowerCase().startsWith(r.toLowerCase()))return null;let i=r.endsWith("/")?r.length-1:r.length,o=n.charAt(i);return o&&o!=="/"?null:n.slice(i)||"/"}function y2(n,r="/"){let{pathname:i,search:o="",hash:d=""}=typeof n=="string"?dl(n):n;return{pathname:i?i.startsWith("/")?i:x2(i,r):r,search:w2(o),hash:S2(d)}}function x2(n,r){let i=r.replace(/\/+$/,"").split("/");return n.split("/").forEach(d=>{d===".."?i.length>1&&i.pop():d!=="."&&i.push(d)}),i.length>1?i.join("/"):"/"}function kd(n,r,i,o){return`Cannot include a '${n}' character in a manually specified \`to.${r}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${i}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function sp(n){return n.filter((r,i)=>i===0||r.route.path&&r.route.path.length>0)}function xf(n){let r=sp(n);return r.map((i,o)=>o===r.length-1?i.pathname:i.pathnameBase)}function bf(n,r,i,o=!1){let d;typeof n=="string"?d=dl(n):(d={...n},ke(!d.pathname||!d.pathname.includes("?"),kd("?","pathname","search",d)),ke(!d.pathname||!d.pathname.includes("#"),kd("#","pathname","hash",d)),ke(!d.search||!d.search.includes("#"),kd("#","search","hash",d)));let f=n===""||d.pathname==="",m=f?"/":d.pathname,h;if(m==null)h=i;else{let S=r.length-1;if(!o&&m.startsWith("..")){let w=m.split("/");for(;w[0]==="..";)w.shift(),S-=1;d.pathname=w.join("/")}h=S>=0?r[S]:"/"}let v=y2(d,h),g=m&&m!=="/"&&m.endsWith("/"),y=(f||m===".")&&i.endsWith("/");return!v.pathname.endsWith("/")&&(g||y)&&(v.pathname+="/"),v}var Ia=n=>n.join("/").replace(/\/\/+/g,"/"),b2=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),w2=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,S2=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n,nu=class{constructor(n,r,i,o=!1){this.status=n,this.statusText=r||"",this.internal=o,i instanceof Error?(this.data=i.toString(),this.error=i):this.data=i}};function os(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}var op=["POST","PUT","PATCH","DELETE"],N2=new Set(op),E2=["GET",...op],j2=new Set(E2),T2=new Set([301,302,303,307,308]),R2=new Set([307,308]),Ld={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},M2={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Wi={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},wf=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,A2=n=>({hasErrorBoundary:!!n.hasErrorBoundary}),up="remix-router-transitions",cp=Symbol("ResetLoaderData");function C2(n){const r=n.window?n.window:typeof window<"u"?window:void 0,i=typeof r<"u"&&typeof r.document<"u"&&typeof r.document.createElement<"u";ke(n.routes.length>0,"You must provide a non-empty routes array to createRouter");let o=n.hydrationRouteProperties||[],d=n.mapRouteProperties||A2,f={},m=tu(n.routes,d,void 0,f),h,v=n.basename||"/",g=n.dataStrategy||k2,y={unstable_middleware:!1,...n.future},S=null,w=new Set,T=null,j=null,N=null,C=n.hydrationData!=null,M=al(m,n.history.location,v),P=!1,_=null,V;if(M==null&&!n.patchRoutesOnNavigation){let A=wa(404,{pathname:n.history.location.pathname}),{matches:k,route:Y}=ag(m);V=!0,M=k,_={[Y.id]:A}}else if(M&&!n.hydrationData&&gl(M,m,n.history.location.pathname).active&&(M=null),M)if(M.some(A=>A.route.lazy))V=!1;else if(!M.some(A=>A.route.loader))V=!0;else{let A=n.hydrationData?n.hydrationData.loaderData:null,k=n.hydrationData?n.hydrationData.errors:null;if(k){let Y=M.findIndex(I=>k[I.route.id]!==void 0);V=M.slice(0,Y+1).every(I=>!ef(I.route,A,k))}else V=M.every(Y=>!ef(Y.route,A,k))}else{V=!1,M=[];let A=gl(null,m,n.history.location.pathname);A.active&&A.matches&&(P=!0,M=A.matches)}let q,O={historyAction:n.history.action,location:n.history.location,matches:M,initialized:V,navigation:Ld,restoreScrollPosition:n.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:n.hydrationData&&n.hydrationData.loaderData||{},actionData:n.hydrationData&&n.hydrationData.actionData||null,errors:n.hydrationData&&n.hydrationData.errors||_,fetchers:new Map,blockers:new Map},te="POP",K=!1,W,ge=!1,Le=new Map,qe=null,pt=!1,Me=!1,Ne=new Set,L=new Map,ae=0,J=-1,Ee=new Map,R=new Set,X=new Map,le=new Map,ee=new Set,re=new Map,Te,he=null;function He(){if(S=n.history.listen(({action:A,location:k,delta:Y})=>{if(Te){Te(),Te=void 0;return}bt(re.size===0||Y!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let I=Ss({currentLocation:O.location,nextLocation:k,historyAction:A});if(I&&Y!=null){let ne=new Promise(ce=>{Te=ce});n.history.go(Y*-1),Ga(I,{state:"blocked",location:k,proceed(){Ga(I,{state:"proceeding",proceed:void 0,reset:void 0,location:k}),ne.then(()=>n.history.go(Y))},reset(){let ce=new Map(O.blockers);ce.set(I,Wi),it({blockers:ce})}});return}return Ma(A,k)}),i){P2(r,Le);let A=()=>X2(r,Le);r.addEventListener("pagehide",A),qe=()=>r.removeEventListener("pagehide",A)}return O.initialized||Ma("POP",O.location,{initialHydration:!0}),q}function Ze(){S&&S(),qe&&qe(),w.clear(),W&&W.abort(),O.fetchers.forEach((A,k)=>At(k)),O.blockers.forEach((A,k)=>ml(k))}function Ht(A){return w.add(A),()=>w.delete(A)}function it(A,k={}){O={...O,...A};let Y=[],I=[];O.fetchers.forEach((ne,ce)=>{ne.state==="idle"&&(ee.has(ce)?Y.push(ce):I.push(ce))}),ee.forEach(ne=>{!O.fetchers.has(ne)&&!L.has(ne)&&Y.push(ne)}),[...w].forEach(ne=>ne(O,{deletedFetchers:Y,viewTransitionOpts:k.viewTransitionOpts,flushSync:k.flushSync===!0})),Y.forEach(ne=>At(ne)),I.forEach(ne=>O.fetchers.delete(ne))}function Bt(A,k,{flushSync:Y}={}){var ve,be;let I=O.actionData!=null&&O.navigation.formMethod!=null&&ua(O.navigation.formMethod)&&O.navigation.state==="loading"&&((ve=A.state)==null?void 0:ve._isRedirect)!==!0,ne;k.actionData?Object.keys(k.actionData).length>0?ne=k.actionData:ne=null:I?ne=O.actionData:ne=null;let ce=k.loaderData?eg(O.loaderData,k.loaderData,k.matches||[],k.errors):O.loaderData,pe=O.blockers;pe.size>0&&(pe=new Map(pe),pe.forEach((de,Se)=>pe.set(Se,Wi)));let ie=K===!0||O.navigation.formMethod!=null&&ua(O.navigation.formMethod)&&((be=A.state)==null?void 0:be._isRedirect)!==!0;h&&(m=h,h=void 0),pt||te==="POP"||(te==="PUSH"?n.history.push(A,A.state):te==="REPLACE"&&n.history.replace(A,A.state));let me;if(te==="POP"){let de=Le.get(O.location.pathname);de&&de.has(A.pathname)?me={currentLocation:O.location,nextLocation:A}:Le.has(A.pathname)&&(me={currentLocation:A,nextLocation:O.location})}else if(ge){let de=Le.get(O.location.pathname);de?de.add(A.pathname):(de=new Set([A.pathname]),Le.set(O.location.pathname,de)),me={currentLocation:O.location,nextLocation:A}}it({...k,actionData:ne,loaderData:ce,historyAction:te,location:A,initialized:!0,navigation:Ld,revalidation:"idle",restoreScrollPosition:Es(A,k.matches||O.matches),preventScrollReset:ie,blockers:pe},{viewTransitionOpts:me,flushSync:Y===!0}),te="POP",K=!1,ge=!1,pt=!1,Me=!1,he==null||he.resolve(),he=null}async function Ra(A,k){if(typeof A=="number"){n.history.go(A);return}let Y=Wd(O.location,O.matches,v,A,k==null?void 0:k.fromRouteId,k==null?void 0:k.relative),{path:I,submission:ne,error:ce}=K0(!1,Y,k),pe=O.location,ie=ss(O.location,I,k&&k.state);ie={...ie,...n.history.encodeLocation(ie)};let me=k&&k.replace!=null?k.replace:void 0,ve="PUSH";me===!0?ve="REPLACE":me===!1||ne!=null&&ua(ne.formMethod)&&ne.formAction===O.location.pathname+O.location.search&&(ve="REPLACE");let be=k&&"preventScrollReset"in k?k.preventScrollReset===!0:void 0,de=(k&&k.flushSync)===!0,Se=Ss({currentLocation:pe,nextLocation:ie,historyAction:ve});if(Se){Ga(Se,{state:"blocked",location:ie,proceed(){Ga(Se,{state:"proceeding",proceed:void 0,reset:void 0,location:ie}),Ra(A,k)},reset(){let Ge=new Map(O.blockers);Ge.set(Se,Wi),it({blockers:Ge})}});return}await Ma(ve,ie,{submission:ne,pendingError:ce,preventScrollReset:be,replace:k&&k.replace,enableViewTransition:k&&k.viewTransition,flushSync:de})}function Ir(){he||(he=F2()),Wr(),it({revalidation:"loading"});let A=he.promise;return O.navigation.state==="submitting"?A:O.navigation.state==="idle"?(Ma(O.historyAction,O.location,{startUninterruptedRevalidation:!0}),A):(Ma(te||O.historyAction,O.navigation.location,{overrideNavigation:O.navigation,enableViewTransition:ge===!0}),A)}async function Ma(A,k,Y){W&&W.abort(),W=null,te=A,pt=(Y&&Y.startUninterruptedRevalidation)===!0,vl(O.location,O.matches),K=(Y&&Y.preventScrollReset)===!0,ge=(Y&&Y.enableViewTransition)===!0;let I=h||m,ne=Y&&Y.overrideNavigation,ce=Y!=null&&Y.initialHydration&&O.matches&&O.matches.length>0&&!P?O.matches:al(I,k,v),pe=(Y&&Y.flushSync)===!0;if(ce&&O.initialized&&!Me&&G2(O.location,k)&&!(Y&&Y.submission&&ua(Y.submission.formMethod))){Bt(k,{matches:ce},{flushSync:pe});return}let ie=gl(ce,I,k.pathname);if(ie.active&&ie.matches&&(ce=ie.matches),!ce){let{error:ct,notFoundMatches:Ye,route:Ue}=Jl(k.pathname);Bt(k,{matches:Ye,loaderData:{},errors:{[Ue.id]:ct}},{flushSync:pe});return}W=new AbortController;let me=Lr(n.history,k,W.signal,Y&&Y.submission),ve=new V0(n.unstable_getContext?await n.unstable_getContext():void 0),be;if(Y&&Y.pendingError)be=[Ll(ce).route.id,{type:"error",error:Y.pendingError}];else if(Y&&Y.submission&&ua(Y.submission.formMethod)){let ct=await xu(me,k,Y.submission,ce,ve,ie.active,Y&&Y.initialHydration===!0,{replace:Y.replace,flushSync:pe});if(ct.shortCircuited)return;if(ct.pendingActionResult){let[Ye,Ue]=ct.pendingActionResult;if(oa(Ue)&&os(Ue.error)&&Ue.error.status===404){W=null,Bt(k,{matches:ct.matches,loaderData:{},errors:{[Ye]:Ue.error}});return}}ce=ct.matches||ce,be=ct.pendingActionResult,ne=Ud(k,Y.submission),pe=!1,ie.active=!1,me=Lr(n.history,me.url,me.signal)}let{shortCircuited:de,matches:Se,loaderData:Ge,errors:lt}=await bu(me,k,ce,ve,ie.active,ne,Y&&Y.submission,Y&&Y.fetcherSubmission,Y&&Y.replace,Y&&Y.initialHydration===!0,pe,be);de||(W=null,Bt(k,{matches:Se||ce,...tg(be),loaderData:Ge,errors:lt}))}async function xu(A,k,Y,I,ne,ce,pe,ie={}){Wr();let me=Q2(k,Y);if(it({navigation:me},{flushSync:ie.flushSync===!0}),ce){let de=await pl(I,k.pathname,A.signal);if(de.type==="aborted")return{shortCircuited:!0};if(de.type==="error"){let Se=Ll(de.partialMatches).route.id;return{matches:de.partialMatches,pendingActionResult:[Se,{type:"error",error:de.error}]}}else if(de.matches)I=de.matches;else{let{notFoundMatches:Se,error:Ge,route:lt}=Jl(k.pathname);return{matches:Se,pendingActionResult:[lt.id,{type:"error",error:Ge}]}}}let ve,be=ls(I,k);if(!be.route.action&&!be.route.lazy)ve={type:"error",error:wa(405,{method:A.method,pathname:k.pathname,routeId:be.route.id})};else{let de=Hr(d,f,A,I,be,pe?[]:o,ne),Se=await En(A,de,ne,null);if(ve=Se[be.route.id],!ve){for(let Ge of I)if(Se[Ge.route.id]){ve=Se[Ge.route.id];break}}if(A.signal.aborted)return{shortCircuited:!0}}if(Ul(ve)){let de;return ie&&ie.replace!=null?de=ie.replace:de=J0(ve.response.headers.get("Location"),new URL(A.url),v)===O.location.pathname+O.location.search,await tn(A,ve,!0,{submission:Y,replace:de}),{shortCircuited:!0}}if(oa(ve)){let de=Ll(I,be.route.id);return(ie&&ie.replace)!==!0&&(te="PUSH"),{matches:I,pendingActionResult:[de.route.id,ve,be.route.id]}}return{matches:I,pendingActionResult:[be.route.id,ve]}}async function bu(A,k,Y,I,ne,ce,pe,ie,me,ve,be,de){let Se=ce||Ud(k,pe),Ge=pe||ie||lg(Se),lt=!pt&&!ve;if(ne){if(lt){let zt=ea(de);it({navigation:Se,...zt!==void 0?{actionData:zt}:{}},{flushSync:be})}let De=await pl(Y,k.pathname,A.signal);if(De.type==="aborted")return{shortCircuited:!0};if(De.type==="error"){let zt=Ll(De.partialMatches).route.id;return{matches:De.partialMatches,loaderData:{},errors:{[zt]:De.error}}}else if(De.matches)Y=De.matches;else{let{error:zt,notFoundMatches:Ca,route:Da}=Jl(k.pathname);return{matches:Ca,loaderData:{},errors:{[Da.id]:zt}}}}let ct=h||m,{dsMatches:Ye,revalidatingFetchers:Ue}=P0(A,I,d,f,n.history,O,Y,Ge,k,ve?[]:o,ve===!0,Me,Ne,ee,X,R,ct,v,n.patchRoutesOnNavigation!=null,de);if(J=++ae,!n.dataStrategy&&!Ye.some(De=>De.shouldLoad)&&Ue.length===0){let De=fl();return Bt(k,{matches:Y,loaderData:{},errors:de&&oa(de[1])?{[de[0]]:de[1].error}:null,...tg(de),...De?{fetchers:new Map(O.fetchers)}:{}},{flushSync:be}),{shortCircuited:!0}}if(lt){let De={};if(!ne){De.navigation=Se;let zt=ea(de);zt!==void 0&&(De.actionData=zt)}Ue.length>0&&(De.fetchers=wu(Ue)),it(De,{flushSync:be})}Ue.forEach(De=>{qa(De.key),De.controller&&L.set(De.key,De.controller)});let jn=()=>Ue.forEach(De=>qa(De.key));W&&W.signal.addEventListener("abort",jn);let{loaderResults:qt,fetcherResults:ca}=await bs(Ye,Ue,A,I);if(A.signal.aborted)return{shortCircuited:!0};W&&W.signal.removeEventListener("abort",jn),Ue.forEach(De=>L.delete(De.key));let Xt=Lo(qt);if(Xt)return await tn(A,Xt.result,!0,{replace:me}),{shortCircuited:!0};if(Xt=Lo(ca),Xt)return R.add(Xt.key),await tn(A,Xt.result,!0,{replace:me}),{shortCircuited:!0};let{loaderData:Tn,errors:Rn}=W0(O,Y,qt,de,Ue,ca);ve&&O.errors&&(Rn={...O.errors,...Rn});let ai=fl(),da=hl(J),nn=ai||da||Ue.length>0;return{matches:Y,loaderData:Tn,errors:Rn,...nn?{fetchers:new Map(O.fetchers)}:{}}}function ea(A){if(A&&!oa(A[1]))return{[A[0]]:A[1].data};if(O.actionData)return Object.keys(O.actionData).length===0?null:O.actionData}function wu(A){return A.forEach(k=>{let Y=O.fetchers.get(k.key),I=es(void 0,Y?Y.data:void 0);O.fetchers.set(k.key,I)}),new Map(O.fetchers)}async function ys(A,k,Y,I){qa(A);let ne=(I&&I.flushSync)===!0,ce=h||m,pe=Wd(O.location,O.matches,v,Y,k,I==null?void 0:I.relative),ie=al(ce,pe,v),me=gl(ie,ce,pe);if(me.active&&me.matches&&(ie=me.matches),!ie){ut(A,k,wa(404,{pathname:pe}),{flushSync:ne});return}let{path:ve,submission:be,error:de}=K0(!0,pe,I);if(de){ut(A,k,de,{flushSync:ne});return}let Se=ls(ie,ve),Ge=new V0(n.unstable_getContext?await n.unstable_getContext():void 0),lt=(I&&I.preventScrollReset)===!0;if(be&&ua(be.formMethod)){await xs(A,k,ve,Se,ie,Ge,me.active,ne,lt,be);return}X.set(A,{routeId:k,path:ve}),await $l(A,k,ve,Se,ie,Ge,me.active,ne,lt,be)}async function xs(A,k,Y,I,ne,ce,pe,ie,me,ve){Wr(),X.delete(A);function be(st){if(!st.route.action&&!st.route.lazy){let Mn=wa(405,{method:ve.formMethod,pathname:Y,routeId:k});return ut(A,k,Mn,{flushSync:ie}),!0}return!1}if(!pe&&be(I))return;let de=O.fetchers.get(A);Pt(A,K2(ve,de),{flushSync:ie});let Se=new AbortController,Ge=Lr(n.history,Y,Se.signal,ve);if(pe){let st=await pl(ne,Y,Ge.signal,A);if(st.type==="aborted")return;if(st.type==="error"){ut(A,k,st.error,{flushSync:ie});return}else if(st.matches){if(ne=st.matches,I=ls(ne,Y),be(I))return}else{ut(A,k,wa(404,{pathname:Y}),{flushSync:ie});return}}L.set(A,Se);let lt=ae,ct=Hr(d,f,Ge,ne,I,o,ce),Ue=(await En(Ge,ct,ce,A))[I.route.id];if(Ge.signal.aborted){L.get(A)===Se&&L.delete(A);return}if(ee.has(A)){if(Ul(Ue)||oa(Ue)){Pt(A,el(void 0));return}}else{if(Ul(Ue))if(L.delete(A),J>lt){Pt(A,el(void 0));return}else return R.add(A),Pt(A,es(ve)),tn(Ge,Ue,!1,{fetcherSubmission:ve,preventScrollReset:me});if(oa(Ue)){ut(A,k,Ue.error);return}}let jn=O.navigation.location||O.location,qt=Lr(n.history,jn,Se.signal),ca=h||m,Xt=O.navigation.state!=="idle"?al(ca,O.navigation.location,v):O.matches;ke(Xt,"Didn't find any matches after fetcher action");let Tn=++ae;Ee.set(A,Tn);let Rn=es(ve,Ue.data);O.fetchers.set(A,Rn);let{dsMatches:ai,revalidatingFetchers:da}=P0(qt,ce,d,f,n.history,O,Xt,ve,jn,o,!1,Me,Ne,ee,X,R,ca,v,n.patchRoutesOnNavigation!=null,[I.route.id,Ue]);da.filter(st=>st.key!==A).forEach(st=>{let Mn=st.key,ni=O.fetchers.get(Mn),Ft=es(void 0,ni?ni.data:void 0);O.fetchers.set(Mn,Ft),qa(Mn),st.controller&&L.set(Mn,st.controller)}),it({fetchers:new Map(O.fetchers)});let nn=()=>da.forEach(st=>qa(st.key));Se.signal.addEventListener("abort",nn);let{loaderResults:De,fetcherResults:zt}=await bs(ai,da,qt,ce);if(Se.signal.aborted)return;if(Se.signal.removeEventListener("abort",nn),Ee.delete(A),L.delete(A),da.forEach(st=>L.delete(st.key)),O.fetchers.has(A)){let st=el(Ue.data);O.fetchers.set(A,st)}let Ca=Lo(De);if(Ca)return tn(qt,Ca.result,!1,{preventScrollReset:me});if(Ca=Lo(zt),Ca)return R.add(Ca.key),tn(qt,Ca.result,!1,{preventScrollReset:me});let{loaderData:Da,errors:yl}=W0(O,Xt,De,void 0,da,zt);hl(Tn),O.navigation.state==="loading"&&Tn>J?(ke(te,"Expected pending action"),W&&W.abort(),Bt(O.navigation.location,{matches:Xt,loaderData:Da,errors:yl,fetchers:new Map(O.fetchers)})):(it({errors:yl,loaderData:eg(O.loaderData,Da,Xt,yl),fetchers:new Map(O.fetchers)}),Me=!1)}async function $l(A,k,Y,I,ne,ce,pe,ie,me,ve){let be=O.fetchers.get(A);Pt(A,es(ve,be?be.data:void 0),{flushSync:ie});let de=new AbortController,Se=Lr(n.history,Y,de.signal);if(pe){let Ue=await pl(ne,Y,Se.signal,A);if(Ue.type==="aborted")return;if(Ue.type==="error"){ut(A,k,Ue.error,{flushSync:ie});return}else if(Ue.matches)ne=Ue.matches,I=ls(ne,Y);else{ut(A,k,wa(404,{pathname:Y}),{flushSync:ie});return}}L.set(A,de);let Ge=ae,lt=Hr(d,f,Se,ne,I,o,ce),Ye=(await En(Se,lt,ce,A))[I.route.id];if(L.get(A)===de&&L.delete(A),!Se.signal.aborted){if(ee.has(A)){Pt(A,el(void 0));return}if(Ul(Ye))if(J>Ge){Pt(A,el(void 0));return}else{R.add(A),await tn(Se,Ye,!1,{preventScrollReset:me});return}if(oa(Ye)){ut(A,k,Ye.error);return}Pt(A,el(Ye.data))}}async function tn(A,k,Y,{submission:I,fetcherSubmission:ne,preventScrollReset:ce,replace:pe}={}){k.response.headers.has("X-Remix-Revalidate")&&(Me=!0);let ie=k.response.headers.get("Location");ke(ie,"Expected a Location header on the redirect Response"),ie=J0(ie,new URL(A.url),v);let me=ss(O.location,ie,{_isRedirect:!0});if(i){let lt=!1;if(k.response.headers.has("X-Remix-Reload-Document"))lt=!0;else if(wf.test(ie)){const ct=lp(ie,!0);lt=ct.origin!==r.location.origin||ja(ct.pathname,v)==null}if(lt){pe?r.location.replace(ie):r.location.assign(ie);return}}W=null;let ve=pe===!0||k.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:be,formAction:de,formEncType:Se}=O.navigation;!I&&!ne&&be&&de&&Se&&(I=lg(O.navigation));let Ge=I||ne;if(R2.has(k.response.status)&&Ge&&ua(Ge.formMethod))await Ma(ve,me,{submission:{...Ge,formAction:ie},preventScrollReset:ce||K,enableViewTransition:Y?ge:void 0});else{let lt=Ud(me,I);await Ma(ve,me,{overrideNavigation:lt,fetcherSubmission:ne,preventScrollReset:ce||K,enableViewTransition:Y?ge:void 0})}}async function En(A,k,Y,I){let ne,ce={};try{ne=await L2(g,A,k,I,Y,!1)}catch(pe){return k.filter(ie=>ie.shouldLoad).forEach(ie=>{ce[ie.route.id]={type:"error",error:pe}}),ce}if(A.signal.aborted)return ce;for(let[pe,ie]of Object.entries(ne))if(Y2(ie)){let me=ie.result;ce[pe]={type:"redirect",response:B2(me,A,pe,k,v)}}else ce[pe]=await H2(ie);return ce}async function bs(A,k,Y,I){let ne=En(Y,A,I,null),ce=Promise.all(k.map(async me=>{if(me.matches&&me.match&&me.request&&me.controller){let be=(await En(me.request,me.matches,I,me.key))[me.match.route.id];return{[me.key]:be}}else return Promise.resolve({[me.key]:{type:"error",error:wa(404,{pathname:me.path})}})})),pe=await ne,ie=(await ce).reduce((me,ve)=>Object.assign(me,ve),{});return{loaderResults:pe,fetcherResults:ie}}function Wr(){Me=!0,X.forEach((A,k)=>{L.has(k)&&Ne.add(k),qa(k)})}function Pt(A,k,Y={}){O.fetchers.set(A,k),it({fetchers:new Map(O.fetchers)},{flushSync:(Y&&Y.flushSync)===!0})}function ut(A,k,Y,I={}){let ne=Ll(O.matches,k);At(A),it({errors:{[ne.route.id]:Y},fetchers:new Map(O.fetchers)},{flushSync:(I&&I.flushSync)===!0})}function Aa(A){return le.set(A,(le.get(A)||0)+1),ee.has(A)&&ee.delete(A),O.fetchers.get(A)||M2}function At(A){let k=O.fetchers.get(A);L.has(A)&&!(k&&k.state==="loading"&&Ee.has(A))&&qa(A),X.delete(A),Ee.delete(A),R.delete(A),ee.delete(A),Ne.delete(A),O.fetchers.delete(A)}function Su(A){let k=(le.get(A)||0)-1;k<=0?(le.delete(A),ee.add(A)):le.set(A,k),it({fetchers:new Map(O.fetchers)})}function qa(A){let k=L.get(A);k&&(k.abort(),L.delete(A))}function ws(A){for(let k of A){let Y=Aa(k),I=el(Y.data);O.fetchers.set(k,I)}}function fl(){let A=[],k=!1;for(let Y of R){let I=O.fetchers.get(Y);ke(I,`Expected fetcher: ${Y}`),I.state==="loading"&&(R.delete(Y),A.push(Y),k=!0)}return ws(A),k}function hl(A){let k=[];for(let[Y,I]of Ee)if(I<A){let ne=O.fetchers.get(Y);ke(ne,`Expected fetcher: ${Y}`),ne.state==="loading"&&(qa(Y),Ee.delete(Y),k.push(Y))}return ws(k),k.length>0}function an(A,k){let Y=O.blockers.get(A)||Wi;return re.get(A)!==k&&re.set(A,k),Y}function ml(A){O.blockers.delete(A),re.delete(A)}function Ga(A,k){let Y=O.blockers.get(A)||Wi;ke(Y.state==="unblocked"&&k.state==="blocked"||Y.state==="blocked"&&k.state==="blocked"||Y.state==="blocked"&&k.state==="proceeding"||Y.state==="blocked"&&k.state==="unblocked"||Y.state==="proceeding"&&k.state==="unblocked",`Invalid blocker state transition: ${Y.state} -> ${k.state}`);let I=new Map(O.blockers);I.set(A,k),it({blockers:I})}function Ss({currentLocation:A,nextLocation:k,historyAction:Y}){if(re.size===0)return;re.size>1&&bt(!1,"A router only supports one blocker at a time");let I=Array.from(re.entries()),[ne,ce]=I[I.length-1],pe=O.blockers.get(ne);if(!(pe&&pe.state==="proceeding")&&ce({currentLocation:A,nextLocation:k,historyAction:Y}))return ne}function Jl(A){let k=wa(404,{pathname:A}),Y=h||m,{matches:I,route:ne}=ag(Y);return{notFoundMatches:I,route:ne,error:k}}function Ns(A,k,Y){if(T=A,N=k,j=Y||null,!C&&O.navigation===Ld){C=!0;let I=Es(O.location,O.matches);I!=null&&it({restoreScrollPosition:I})}return()=>{T=null,N=null,j=null}}function Il(A,k){return j&&j(A,k.map(I=>r2(I,O.loaderData)))||A.key}function vl(A,k){if(T&&N){let Y=Il(A,k);T[Y]=N()}}function Es(A,k){if(T){let Y=Il(A,k),I=T[Y];if(typeof I=="number")return I}return null}function gl(A,k,Y){if(n.patchRoutesOnNavigation)if(A){if(Object.keys(A[0].params).length>0)return{active:!0,matches:Xo(k,Y,v,!0)}}else return{active:!0,matches:Xo(k,Y,v,!0)||[]};return{active:!1,matches:null}}async function pl(A,k,Y,I){if(!n.patchRoutesOnNavigation)return{type:"success",matches:A};let ne=A;for(;;){let ce=h==null,pe=h||m,ie=f;try{await n.patchRoutesOnNavigation({signal:Y,path:k,matches:ne,fetcherKey:I,patch:(be,de)=>{Y.aborted||X0(be,de,pe,ie,d)}})}catch(be){return{type:"error",error:be,partialMatches:ne}}finally{ce&&!Y.aborted&&(m=[...m])}if(Y.aborted)return{type:"aborted"};let me=al(pe,k,v);if(me)return{type:"success",matches:me};let ve=Xo(pe,k,v,!0);if(!ve||ne.length===ve.length&&ne.every((be,de)=>be.route.id===ve[de].route.id))return{type:"success",matches:null};ne=ve}}function ei(A){f={},h=tu(A,d,void 0,f)}function ti(A,k){let Y=h==null;X0(A,k,h||m,f,d),Y&&(m=[...m],it({}))}return q={get basename(){return v},get future(){return y},get state(){return O},get routes(){return m},get window(){return r},initialize:He,subscribe:Ht,enableScrollRestoration:Ns,navigate:Ra,fetch:ys,revalidate:Ir,createHref:A=>n.history.createHref(A),encodeLocation:A=>n.history.encodeLocation(A),getFetcher:Aa,deleteFetcher:Su,dispose:Ze,getBlocker:an,deleteBlocker:ml,patchRoutes:ti,_internalFetchControllers:L,_internalSetRoutes:ei},q}function D2(n){return n!=null&&("formData"in n&&n.formData!=null||"body"in n&&n.body!==void 0)}function Wd(n,r,i,o,d,f){let m,h;if(d){m=[];for(let g of r)if(m.push(g),g.route.id===d){h=g;break}}else m=r,h=r[r.length-1];let v=bf(o||".",xf(m),ja(n.pathname,i)||n.pathname,f==="path");if(o==null&&(v.search=n.search,v.hash=n.hash),(o==null||o===""||o===".")&&h){let g=Sf(v.search);if(h.route.index&&!g)v.search=v.search?v.search.replace(/^\?/,"?index&"):"?index";else if(!h.route.index&&g){let y=new URLSearchParams(v.search),S=y.getAll("index");y.delete("index"),S.filter(T=>T).forEach(T=>y.append("index",T));let w=y.toString();v.search=w?`?${w}`:""}}return i!=="/"&&(v.pathname=v.pathname==="/"?i:Ia([i,v.pathname])),cl(v)}function K0(n,r,i){if(!i||!D2(i))return{path:r};if(i.formMethod&&!V2(i.formMethod))return{path:r,error:wa(405,{method:i.formMethod})};let o=()=>({path:r,error:wa(400,{type:"invalid-body"})}),f=(i.formMethod||"get").toUpperCase(),m=gp(r);if(i.body!==void 0){if(i.formEncType==="text/plain"){if(!ua(f))return o();let S=typeof i.body=="string"?i.body:i.body instanceof FormData||i.body instanceof URLSearchParams?Array.from(i.body.entries()).reduce((w,[T,j])=>`${w}${T}=${j}
`,""):String(i.body);return{path:r,submission:{formMethod:f,formAction:m,formEncType:i.formEncType,formData:void 0,json:void 0,text:S}}}else if(i.formEncType==="application/json"){if(!ua(f))return o();try{let S=typeof i.body=="string"?JSON.parse(i.body):i.body;return{path:r,submission:{formMethod:f,formAction:m,formEncType:i.formEncType,formData:void 0,json:S,text:void 0}}}catch{return o()}}}ke(typeof FormData=="function","FormData is not available in this environment");let h,v;if(i.formData)h=af(i.formData),v=i.formData;else if(i.body instanceof FormData)h=af(i.body),v=i.body;else if(i.body instanceof URLSearchParams)h=i.body,v=I0(h);else if(i.body==null)h=new URLSearchParams,v=new FormData;else try{h=new URLSearchParams(i.body),v=I0(h)}catch{return o()}let g={formMethod:f,formAction:m,formEncType:i&&i.formEncType||"application/x-www-form-urlencoded",formData:v,json:void 0,text:void 0};if(ua(g.formMethod))return{path:r,submission:g};let y=dl(r);return n&&y.search&&Sf(y.search)&&h.append("index",""),y.search=`?${h}`,{path:cl(y),submission:g}}function P0(n,r,i,o,d,f,m,h,v,g,y,S,w,T,j,N,C,M,P,_){var pt;let V=_?oa(_[1])?_[1].error:_[1].data:void 0,q=d.createURL(f.location),O=d.createURL(v),te;if(y&&f.errors){let Me=Object.keys(f.errors)[0];te=m.findIndex(Ne=>Ne.route.id===Me)}else if(_&&oa(_[1])){let Me=_[0];te=m.findIndex(Ne=>Ne.route.id===Me)-1}let K=_?_[1].statusCode:void 0,W=K&&K>=400,ge={currentUrl:q,currentParams:((pt=f.matches[0])==null?void 0:pt.params)||{},nextUrl:O,nextParams:m[0].params,...h,actionResult:V,actionStatus:K},Le=m.map((Me,Ne)=>{let{route:L}=Me,ae=null;if(te!=null&&Ne>te?ae=!1:L.lazy?ae=!0:L.loader==null?ae=!1:y?ae=ef(L,f.loaderData,f.errors):O2(f.loaderData,f.matches[Ne],Me)&&(ae=!0),ae!==null)return tf(i,o,n,Me,g,r,ae);let J=W?!1:S||q.pathname+q.search===O.pathname+O.search||q.search!==O.search||z2(f.matches[Ne],Me),Ee={...ge,defaultShouldRevalidate:J},R=lu(Me,Ee);return tf(i,o,n,Me,g,r,R,Ee)}),qe=[];return j.forEach((Me,Ne)=>{if(y||!m.some(ee=>ee.route.id===Me.routeId)||T.has(Ne))return;let L=f.fetchers.get(Ne),ae=L&&L.state!=="idle"&&L.data===void 0,J=al(C,Me.path,M);if(!J){if(P&&ae)return;qe.push({key:Ne,routeId:Me.routeId,path:Me.path,matches:null,match:null,request:null,controller:null});return}if(N.has(Ne))return;let Ee=ls(J,Me.path),R=new AbortController,X=Lr(d,Me.path,R.signal),le=null;if(w.has(Ne))w.delete(Ne),le=Hr(i,o,X,J,Ee,g,r);else if(ae)S&&(le=Hr(i,o,X,J,Ee,g,r));else{let ee={...ge,defaultShouldRevalidate:W?!1:S};lu(Ee,ee)&&(le=Hr(i,o,X,J,Ee,g,r,ee))}le&&qe.push({key:Ne,routeId:Me.routeId,path:Me.path,matches:le,match:Ee,request:X,controller:R})}),{dsMatches:Le,revalidatingFetchers:qe}}function ef(n,r,i){if(n.lazy)return!0;if(!n.loader)return!1;let o=r!=null&&n.id in r,d=i!=null&&i[n.id]!==void 0;return!o&&d?!1:typeof n.loader=="function"&&n.loader.hydrate===!0?!0:!o&&!d}function O2(n,r,i){let o=!r||i.route.id!==r.route.id,d=!n.hasOwnProperty(i.route.id);return o||d}function z2(n,r){let i=n.route.path;return n.pathname!==r.pathname||i!=null&&i.endsWith("*")&&n.params["*"]!==r.params["*"]}function lu(n,r){if(n.route.shouldRevalidate){let i=n.route.shouldRevalidate(r);if(typeof i=="boolean")return i}return r.defaultShouldRevalidate}function X0(n,r,i,o,d){let f;if(n){let v=o[n];ke(v,`No route found to patch children into: routeId = ${n}`),v.children||(v.children=[]),f=v.children}else f=i;let m=r.filter(v=>!f.some(g=>dp(v,g))),h=tu(m,d,[n||"_","patch",String((f==null?void 0:f.length)||"0")],o);f.push(...h)}function dp(n,r){return"id"in n&&"id"in r&&n.id===r.id?!0:n.index===r.index&&n.path===r.path&&n.caseSensitive===r.caseSensitive?(!n.children||n.children.length===0)&&(!r.children||r.children.length===0)?!0:n.children.every((i,o)=>{var d;return(d=r.children)==null?void 0:d.some(f=>dp(i,f))}):!1}var F0=new WeakMap,fp=({key:n,route:r,manifest:i,mapRouteProperties:o})=>{let d=i[r.id];if(ke(d,"No route found in manifest"),!d.lazy||typeof d.lazy!="object")return;let f=d.lazy[n];if(!f)return;let m=F0.get(d);m||(m={},F0.set(d,m));let h=m[n];if(h)return h;let v=(async()=>{let g=t2(n),S=d[n]!==void 0&&n!=="hasErrorBoundary";if(g)bt(!g,"Route property "+n+" is not a supported lazy route property. This property will be ignored."),m[n]=Promise.resolve();else if(S)bt(!1,`Route "${d.id}" has a static property "${n}" defined. The lazy property will be ignored.`);else{let w=await f();w!=null&&(Object.assign(d,{[n]:w}),Object.assign(d,o(d)))}typeof d.lazy=="object"&&(d.lazy[n]=void 0,Object.values(d.lazy).every(w=>w===void 0)&&(d.lazy=void 0))})();return m[n]=v,v},Z0=new WeakMap;function _2(n,r,i,o,d){let f=i[n.id];if(ke(f,"No route found in manifest"),!n.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof n.lazy=="function"){let y=Z0.get(f);if(y)return{lazyRoutePromise:y,lazyHandlerPromise:y};let S=(async()=>{ke(typeof n.lazy=="function","No lazy route function found");let w=await n.lazy(),T={};for(let j in w){let N=w[j];if(N===void 0)continue;let C=n2(j),P=f[j]!==void 0&&j!=="hasErrorBoundary";C?bt(!C,"Route property "+j+" is not a supported property to be returned from a lazy route function. This property will be ignored."):P?bt(!P,`Route "${f.id}" has a static property "${j}" defined but its lazy function is also returning a value for this property. The lazy route property "${j}" will be ignored.`):T[j]=N}Object.assign(f,T),Object.assign(f,{...o(f),lazy:void 0})})();return Z0.set(f,S),S.catch(()=>{}),{lazyRoutePromise:S,lazyHandlerPromise:S}}let m=Object.keys(n.lazy),h=[],v;for(let y of m){if(d&&d.includes(y))continue;let S=fp({key:y,route:n,manifest:i,mapRouteProperties:o});S&&(h.push(S),y===r&&(v=S))}let g=h.length>0?Promise.all(h).then(()=>{}):void 0;return g==null||g.catch(()=>{}),v==null||v.catch(()=>{}),{lazyRoutePromise:g,lazyHandlerPromise:v}}async function $0(n){let r=n.matches.filter(d=>d.shouldLoad),i={};return(await Promise.all(r.map(d=>d.resolve()))).forEach((d,f)=>{i[r[f].route.id]=d}),i}async function k2(n){return n.matches.some(r=>r.route.unstable_middleware)?hp(n,!1,()=>$0(n),(r,i)=>({[i]:{type:"error",result:r}})):$0(n)}async function hp(n,r,i,o){let{matches:d,request:f,params:m,context:h}=n,v={handlerResult:void 0};try{let g=d.flatMap(S=>S.route.unstable_middleware?S.route.unstable_middleware.map(w=>[S.route.id,w]):[]),y=await mp({request:f,params:m,context:h},g,r,v,i);return r?y:v.handlerResult}catch(g){if(!v.middlewareError)throw g;let y=await o(v.middlewareError.error,v.middlewareError.routeId);return v.handlerResult?Object.assign(v.handlerResult,y):y}}async function mp(n,r,i,o,d,f=0){let{request:m}=n;if(m.signal.aborted)throw m.signal.reason?m.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${m.method} ${m.url}`);let h=r[f];if(!h)return o.handlerResult=await d(),o.handlerResult;let[v,g]=h,y=!1,S,w=async()=>{if(y)throw new Error("You may only call `next()` once per middleware");y=!0,await mp(n,r,i,o,d,f+1)};try{let T=await g({request:n.request,params:n.params,context:n.context},w);return y?T===void 0?S:T:w()}catch(T){throw o.middlewareError?o.middlewareError.error!==T&&(o.middlewareError={routeId:v,error:T}):o.middlewareError={routeId:v,error:T},T}}function vp(n,r,i,o,d){let f=fp({key:"unstable_middleware",route:o.route,manifest:r,mapRouteProperties:n}),m=_2(o.route,ua(i.method)?"action":"loader",r,n,d);return{middleware:f,route:m.lazyRoutePromise,handler:m.lazyHandlerPromise}}function tf(n,r,i,o,d,f,m,h=null){let v=!1,g=vp(n,r,i,o,d);return{...o,_lazyPromises:g,shouldLoad:m,unstable_shouldRevalidateArgs:h,unstable_shouldCallHandler(y){return v=!0,h?typeof y=="boolean"?lu(o,{...h,defaultShouldRevalidate:y}):lu(o,h):m},resolve(y){return v||m||y&&i.method==="GET"&&(o.route.lazy||o.route.loader)?U2({request:i,match:o,lazyHandlerPromise:g==null?void 0:g.handler,lazyRoutePromise:g==null?void 0:g.route,handlerOverride:y,scopedContext:f}):Promise.resolve({type:"data",result:void 0})}}}function Hr(n,r,i,o,d,f,m,h=null){return o.map(v=>v.route.id!==d.route.id?{...v,shouldLoad:!1,unstable_shouldRevalidateArgs:h,unstable_shouldCallHandler:()=>!1,_lazyPromises:vp(n,r,i,v,f),resolve:()=>Promise.resolve({type:"data",result:void 0})}:tf(n,r,i,v,f,m,!0,h))}async function L2(n,r,i,o,d,f){i.some(g=>{var y;return(y=g._lazyPromises)==null?void 0:y.middleware})&&await Promise.all(i.map(g=>{var y;return(y=g._lazyPromises)==null?void 0:y.middleware}));let m={request:r,params:i[0].params,context:d,matches:i},v=await n({...m,fetcherKey:o,unstable_runClientMiddleware:g=>{let y=m;return hp(y,!1,()=>g({...y,fetcherKey:o,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(S,w)=>({[w]:{type:"error",result:S}}))}});try{await Promise.all(i.flatMap(g=>{var y,S;return[(y=g._lazyPromises)==null?void 0:y.handler,(S=g._lazyPromises)==null?void 0:S.route]}))}catch{}return v}async function U2({request:n,match:r,lazyHandlerPromise:i,lazyRoutePromise:o,handlerOverride:d,scopedContext:f}){let m,h,v=ua(n.method),g=v?"action":"loader",y=S=>{let w,T=new Promise((C,M)=>w=M);h=()=>w(),n.signal.addEventListener("abort",h);let j=C=>typeof S!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${g}" [routeId: ${r.route.id}]`)):S({request:n,params:r.params,context:f},...C!==void 0?[C]:[]),N=(async()=>{try{return{type:"data",result:await(d?d(M=>j(M)):j())}}catch(C){return{type:"error",result:C}}})();return Promise.race([N,T])};try{let S=v?r.route.action:r.route.loader;if(i||o)if(S){let w,[T]=await Promise.all([y(S).catch(j=>{w=j}),i,o]);if(w!==void 0)throw w;m=T}else{await i;let w=v?r.route.action:r.route.loader;if(w)[m]=await Promise.all([y(w),o]);else if(g==="action"){let T=new URL(n.url),j=T.pathname+T.search;throw wa(405,{method:n.method,pathname:j,routeId:r.route.id})}else return{type:"data",result:void 0}}else if(S)m=await y(S);else{let w=new URL(n.url),T=w.pathname+w.search;throw wa(404,{pathname:T})}}catch(S){return{type:"error",result:S}}finally{h&&n.signal.removeEventListener("abort",h)}return m}async function H2(n){var o,d,f,m,h,v;let{result:r,type:i}=n;if(pp(r)){let g;try{let y=r.headers.get("Content-Type");y&&/\bapplication\/json\b/.test(y)?r.body==null?g=null:g=await r.json():g=await r.text()}catch(y){return{type:"error",error:y}}return i==="error"?{type:"error",error:new nu(r.status,r.statusText,g),statusCode:r.status,headers:r.headers}:{type:"data",data:g,statusCode:r.status,headers:r.headers}}return i==="error"?ng(r)?r.data instanceof Error?{type:"error",error:r.data,statusCode:(o=r.init)==null?void 0:o.status,headers:(d=r.init)!=null&&d.headers?new Headers(r.init.headers):void 0}:{type:"error",error:new nu(((f=r.init)==null?void 0:f.status)||500,void 0,r.data),statusCode:os(r)?r.status:void 0,headers:(m=r.init)!=null&&m.headers?new Headers(r.init.headers):void 0}:{type:"error",error:r,statusCode:os(r)?r.status:void 0}:ng(r)?{type:"data",data:r.data,statusCode:(h=r.init)==null?void 0:h.status,headers:(v=r.init)!=null&&v.headers?new Headers(r.init.headers):void 0}:{type:"data",data:r}}function B2(n,r,i,o,d){let f=n.headers.get("Location");if(ke(f,"Redirects returned/thrown from loaders/actions must have a Location header"),!wf.test(f)){let m=o.slice(0,o.findIndex(h=>h.route.id===i)+1);f=Wd(new URL(r.url),m,d,f),n.headers.set("Location",f)}return n}function J0(n,r,i){if(wf.test(n)){let o=n,d=o.startsWith("//")?new URL(r.protocol+o):new URL(o),f=ja(d.pathname,i)!=null;if(d.origin===r.origin&&f)return d.pathname+d.search+d.hash}return n}function Lr(n,r,i,o){let d=n.createURL(gp(r)).toString(),f={signal:i};if(o&&ua(o.formMethod)){let{formMethod:m,formEncType:h}=o;f.method=m.toUpperCase(),h==="application/json"?(f.headers=new Headers({"Content-Type":h}),f.body=JSON.stringify(o.json)):h==="text/plain"?f.body=o.text:h==="application/x-www-form-urlencoded"&&o.formData?f.body=af(o.formData):f.body=o.formData}return new Request(d,f)}function af(n){let r=new URLSearchParams;for(let[i,o]of n.entries())r.append(i,typeof o=="string"?o:o.name);return r}function I0(n){let r=new FormData;for(let[i,o]of n.entries())r.append(i,o);return r}function q2(n,r,i,o=!1,d=!1){let f={},m=null,h,v=!1,g={},y=i&&oa(i[1])?i[1].error:void 0;return n.forEach(S=>{if(!(S.route.id in r))return;let w=S.route.id,T=r[w];if(ke(!Ul(T),"Cannot handle redirect results in processLoaderData"),oa(T)){let j=T.error;if(y!==void 0&&(j=y,y=void 0),m=m||{},d)m[w]=j;else{let N=Ll(n,w);m[N.route.id]==null&&(m[N.route.id]=j)}o||(f[w]=cp),v||(v=!0,h=os(T.error)?T.error.status:500),T.headers&&(g[w]=T.headers)}else f[w]=T.data,T.statusCode&&T.statusCode!==200&&!v&&(h=T.statusCode),T.headers&&(g[w]=T.headers)}),y!==void 0&&i&&(m={[i[0]]:y},i[2]&&(f[i[2]]=void 0)),{loaderData:f,errors:m,statusCode:h||200,loaderHeaders:g}}function W0(n,r,i,o,d,f){let{loaderData:m,errors:h}=q2(r,i,o);return d.filter(v=>!v.matches||v.matches.some(g=>g.shouldLoad)).forEach(v=>{let{key:g,match:y,controller:S}=v,w=f[g];if(ke(w,"Did not find corresponding fetcher result"),!(S&&S.signal.aborted))if(oa(w)){let T=Ll(n.matches,y==null?void 0:y.route.id);h&&h[T.route.id]||(h={...h,[T.route.id]:w.error}),n.fetchers.delete(g)}else if(Ul(w))ke(!1,"Unhandled fetcher revalidation redirect");else{let T=el(w.data);n.fetchers.set(g,T)}}),{loaderData:m,errors:h}}function eg(n,r,i,o){let d=Object.entries(r).filter(([,f])=>f!==cp).reduce((f,[m,h])=>(f[m]=h,f),{});for(let f of i){let m=f.route.id;if(!r.hasOwnProperty(m)&&n.hasOwnProperty(m)&&f.route.loader&&(d[m]=n[m]),o&&o.hasOwnProperty(m))break}return d}function tg(n){return n?oa(n[1])?{actionData:{}}:{actionData:{[n[0]]:n[1].data}}:{}}function Ll(n,r){return(r?n.slice(0,n.findIndex(o=>o.route.id===r)+1):[...n]).reverse().find(o=>o.route.hasErrorBoundary===!0)||n[0]}function ag(n){let r=n.length===1?n[0]:n.find(i=>i.index||!i.path||i.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:r}],route:r}}function wa(n,{pathname:r,routeId:i,method:o,type:d,message:f}={}){let m="Unknown Server Error",h="Unknown @remix-run/router error";return n===400?(m="Bad Request",o&&r&&i?h=`You made a ${o} request to "${r}" but did not provide a \`loader\` for route "${i}", so there is no way to handle the request.`:d==="invalid-body"&&(h="Unable to encode submission body")):n===403?(m="Forbidden",h=`Route "${i}" does not match URL "${r}"`):n===404?(m="Not Found",h=`No route matches URL "${r}"`):n===405&&(m="Method Not Allowed",o&&r&&i?h=`You made a ${o.toUpperCase()} request to "${r}" but did not provide an \`action\` for route "${i}", so there is no way to handle the request.`:o&&(h=`Invalid request method "${o.toUpperCase()}"`)),new nu(n||500,m,new Error(h),!0)}function Lo(n){let r=Object.entries(n);for(let i=r.length-1;i>=0;i--){let[o,d]=r[i];if(Ul(d))return{key:o,result:d}}}function gp(n){let r=typeof n=="string"?dl(n):n;return cl({...r,hash:""})}function G2(n,r){return n.pathname!==r.pathname||n.search!==r.search?!1:n.hash===""?r.hash!=="":n.hash===r.hash?!0:r.hash!==""}function Y2(n){return pp(n.result)&&T2.has(n.result.status)}function oa(n){return n.type==="error"}function Ul(n){return(n&&n.type)==="redirect"}function ng(n){return typeof n=="object"&&n!=null&&"type"in n&&"data"in n&&"init"in n&&n.type==="DataWithResponseInit"}function pp(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.headers=="object"&&typeof n.body<"u"}function V2(n){return j2.has(n.toUpperCase())}function ua(n){return N2.has(n.toUpperCase())}function Sf(n){return new URLSearchParams(n).getAll("index").some(r=>r==="")}function ls(n,r){let i=typeof r=="string"?dl(r).search:r.search;if(n[n.length-1].route.index&&Sf(i||""))return n[n.length-1];let o=sp(n);return o[o.length-1]}function lg(n){let{formMethod:r,formAction:i,formEncType:o,text:d,formData:f,json:m}=n;if(!(!r||!i||!o)){if(d!=null)return{formMethod:r,formAction:i,formEncType:o,formData:void 0,json:void 0,text:d};if(f!=null)return{formMethod:r,formAction:i,formEncType:o,formData:f,json:void 0,text:void 0};if(m!==void 0)return{formMethod:r,formAction:i,formEncType:o,formData:void 0,json:m,text:void 0}}}function Ud(n,r){return r?{state:"loading",location:n,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}:{state:"loading",location:n,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Q2(n,r){return{state:"submitting",location:n,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}}function es(n,r){return n?{state:"loading",formMethod:n.formMethod,formAction:n.formAction,formEncType:n.formEncType,formData:n.formData,json:n.json,text:n.text,data:r}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:r}}function K2(n,r){return{state:"submitting",formMethod:n.formMethod,formAction:n.formAction,formEncType:n.formEncType,formData:n.formData,json:n.json,text:n.text,data:r?r.data:void 0}}function el(n){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:n}}function P2(n,r){try{let i=n.sessionStorage.getItem(up);if(i){let o=JSON.parse(i);for(let[d,f]of Object.entries(o||{}))f&&Array.isArray(f)&&r.set(d,new Set(f||[]))}}catch{}}function X2(n,r){if(r.size>0){let i={};for(let[o,d]of r)i[o]=[...d];try{n.sessionStorage.setItem(up,JSON.stringify(i))}catch(o){bt(!1,`Failed to save applied view transitions in sessionStorage (${o}).`)}}}function F2(){let n,r,i=new Promise((o,d)=>{n=async f=>{o(f);try{await i}catch{}},r=async f=>{d(f);try{await i}catch{}}});return{promise:i,resolve:n,reject:r}}var Xl=x.createContext(null);Xl.displayName="DataRouter";var fs=x.createContext(null);fs.displayName="DataRouterState";var Nf=x.createContext({isTransitioning:!1});Nf.displayName="ViewTransition";var yp=x.createContext(new Map);yp.displayName="Fetchers";var Z2=x.createContext(null);Z2.displayName="Await";var Wa=x.createContext(null);Wa.displayName="Navigation";var mu=x.createContext(null);mu.displayName="Location";var en=x.createContext({outlet:null,matches:[],isDataRoute:!1});en.displayName="Route";var Ef=x.createContext(null);Ef.displayName="RouteError";function $2(n,{relative:r}={}){ke(hs(),"useHref() may be used only in the context of a <Router> component.");let{basename:i,navigator:o}=x.useContext(Wa),{hash:d,pathname:f,search:m}=ms(n,{relative:r}),h=f;return i!=="/"&&(h=f==="/"?i:Ia([i,f])),o.createHref({pathname:h,search:m,hash:d})}function hs(){return x.useContext(mu)!=null}function Fl(){return ke(hs(),"useLocation() may be used only in the context of a <Router> component."),x.useContext(mu).location}var xp="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function bp(n){x.useContext(Wa).static||x.useLayoutEffect(n)}function J2(){let{isDataRoute:n}=x.useContext(en);return n?fw():I2()}function I2(){ke(hs(),"useNavigate() may be used only in the context of a <Router> component.");let n=x.useContext(Xl),{basename:r,navigator:i}=x.useContext(Wa),{matches:o}=x.useContext(en),{pathname:d}=Fl(),f=JSON.stringify(xf(o)),m=x.useRef(!1);return bp(()=>{m.current=!0}),x.useCallback((v,g={})=>{if(bt(m.current,xp),!m.current)return;if(typeof v=="number"){i.go(v);return}let y=bf(v,JSON.parse(f),d,g.relative==="path");n==null&&r!=="/"&&(y.pathname=y.pathname==="/"?r:Ia([r,y.pathname])),(g.replace?i.replace:i.push)(y,g.state,g)},[r,i,f,d,n])}var W2=x.createContext(null);function ew(n){let r=x.useContext(en).outlet;return r&&x.createElement(W2.Provider,{value:n},r)}function ms(n,{relative:r}={}){let{matches:i}=x.useContext(en),{pathname:o}=Fl(),d=JSON.stringify(xf(i));return x.useMemo(()=>bf(n,JSON.parse(d),o,r==="path"),[n,d,o,r])}function tw(n,r,i,o){ke(hs(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:d}=x.useContext(Wa),{matches:f}=x.useContext(en),m=f[f.length-1],h=m?m.params:{},v=m?m.pathname:"/",g=m?m.pathnameBase:"/",y=m&&m.route;{let M=y&&y.path||"";wp(v,!y||M.endsWith("*")||M.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${v}" (under <Route path="${M}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${M}"> to <Route path="${M==="/"?"*":`${M}/*`}">.`)}let S=Fl(),w;w=S;let T=w.pathname||"/",j=T;if(g!=="/"){let M=g.replace(/^\//,"").split("/");j="/"+T.replace(/^\//,"").split("/").slice(M.length).join("/")}let N=al(n,{pathname:j});return bt(y||N!=null,`No routes matched location "${w.pathname}${w.search}${w.hash}" `),bt(N==null||N[N.length-1].route.element!==void 0||N[N.length-1].route.Component!==void 0||N[N.length-1].route.lazy!==void 0,`Matched leaf route at location "${w.pathname}${w.search}${w.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),iw(N&&N.map(M=>Object.assign({},M,{params:Object.assign({},h,M.params),pathname:Ia([g,d.encodeLocation?d.encodeLocation(M.pathname).pathname:M.pathname]),pathnameBase:M.pathnameBase==="/"?g:Ia([g,d.encodeLocation?d.encodeLocation(M.pathnameBase).pathname:M.pathnameBase])})),f,i,o)}function aw(){let n=dw(),r=os(n)?`${n.status} ${n.statusText}`:n instanceof Error?n.message:JSON.stringify(n),i=n instanceof Error?n.stack:null,o="rgba(200,200,200, 0.5)",d={padding:"0.5rem",backgroundColor:o},f={padding:"2px 4px",backgroundColor:o},m=null;return console.error("Error handled by React Router default ErrorBoundary:",n),m=x.createElement(x.Fragment,null,x.createElement("p",null,"💿 Hey developer 👋"),x.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",x.createElement("code",{style:f},"ErrorBoundary")," or"," ",x.createElement("code",{style:f},"errorElement")," prop on your route.")),x.createElement(x.Fragment,null,x.createElement("h2",null,"Unexpected Application Error!"),x.createElement("h3",{style:{fontStyle:"italic"}},r),i?x.createElement("pre",{style:d},i):null,m)}var nw=x.createElement(aw,null),lw=class extends x.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,r){return r.location!==n.location||r.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:r.error,location:r.location,revalidation:n.revalidation||r.revalidation}}componentDidCatch(n,r){console.error("React Router caught the following error during render",n,r)}render(){return this.state.error!==void 0?x.createElement(en.Provider,{value:this.props.routeContext},x.createElement(Ef.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function rw({routeContext:n,match:r,children:i}){let o=x.useContext(Xl);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),x.createElement(en.Provider,{value:n},i)}function iw(n,r=[],i=null,o=null){if(n==null){if(!i)return null;if(i.errors)n=i.matches;else if(r.length===0&&!i.initialized&&i.matches.length>0)n=i.matches;else return null}let d=n,f=i==null?void 0:i.errors;if(f!=null){let v=d.findIndex(g=>g.route.id&&(f==null?void 0:f[g.route.id])!==void 0);ke(v>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),d=d.slice(0,Math.min(d.length,v+1))}let m=!1,h=-1;if(i)for(let v=0;v<d.length;v++){let g=d[v];if((g.route.HydrateFallback||g.route.hydrateFallbackElement)&&(h=v),g.route.id){let{loaderData:y,errors:S}=i,w=g.route.loader&&!y.hasOwnProperty(g.route.id)&&(!S||S[g.route.id]===void 0);if(g.route.lazy||w){m=!0,h>=0?d=d.slice(0,h+1):d=[d[0]];break}}}return d.reduceRight((v,g,y)=>{let S,w=!1,T=null,j=null;i&&(S=f&&g.route.id?f[g.route.id]:void 0,T=g.route.errorElement||nw,m&&(h<0&&y===0?(wp("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),w=!0,j=null):h===y&&(w=!0,j=g.route.hydrateFallbackElement||null)));let N=r.concat(d.slice(0,y+1)),C=()=>{let M;return S?M=T:w?M=j:g.route.Component?M=x.createElement(g.route.Component,null):g.route.element?M=g.route.element:M=v,x.createElement(rw,{match:g,routeContext:{outlet:v,matches:N,isDataRoute:i!=null},children:M})};return i&&(g.route.ErrorBoundary||g.route.errorElement||y===0)?x.createElement(lw,{location:i.location,revalidation:i.revalidation,component:T,error:S,children:C(),routeContext:{outlet:null,matches:N,isDataRoute:!0}}):C()},null)}function jf(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function sw(n){let r=x.useContext(Xl);return ke(r,jf(n)),r}function ow(n){let r=x.useContext(fs);return ke(r,jf(n)),r}function uw(n){let r=x.useContext(en);return ke(r,jf(n)),r}function Tf(n){let r=uw(n),i=r.matches[r.matches.length-1];return ke(i.route.id,`${n} can only be used on routes that contain a unique "id"`),i.route.id}function cw(){return Tf("useRouteId")}function dw(){var o;let n=x.useContext(Ef),r=ow("useRouteError"),i=Tf("useRouteError");return n!==void 0?n:(o=r.errors)==null?void 0:o[i]}function fw(){let{router:n}=sw("useNavigate"),r=Tf("useNavigate"),i=x.useRef(!1);return bp(()=>{i.current=!0}),x.useCallback(async(d,f={})=>{bt(i.current,xp),i.current&&(typeof d=="number"?n.navigate(d):await n.navigate(d,{fromRouteId:r,...f}))},[n,r])}var rg={};function wp(n,r,i){!r&&!rg[n]&&(rg[n]=!0,bt(!1,i))}var ig={};function sg(n,r){!n&&!ig[r]&&(ig[r]=!0,console.warn(r))}function hw(n){let r={hasErrorBoundary:n.hasErrorBoundary||n.ErrorBoundary!=null||n.errorElement!=null};return n.Component&&(n.element&&bt(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(r,{element:x.createElement(n.Component),Component:void 0})),n.HydrateFallback&&(n.hydrateFallbackElement&&bt(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(r,{hydrateFallbackElement:x.createElement(n.HydrateFallback),HydrateFallback:void 0})),n.ErrorBoundary&&(n.errorElement&&bt(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(r,{errorElement:x.createElement(n.ErrorBoundary),ErrorBoundary:void 0})),r}var mw=["HydrateFallback","hydrateFallbackElement"],vw=class{constructor(){this.status="pending",this.promise=new Promise((n,r)=>{this.resolve=i=>{this.status==="pending"&&(this.status="resolved",n(i))},this.reject=i=>{this.status==="pending"&&(this.status="rejected",r(i))}})}};function gw({router:n,flushSync:r}){let[i,o]=x.useState(n.state),[d,f]=x.useState(),[m,h]=x.useState({isTransitioning:!1}),[v,g]=x.useState(),[y,S]=x.useState(),[w,T]=x.useState(),j=x.useRef(new Map),N=x.useCallback((_,{deletedFetchers:V,flushSync:q,viewTransitionOpts:O})=>{_.fetchers.forEach((K,W)=>{K.data!==void 0&&j.current.set(W,K.data)}),V.forEach(K=>j.current.delete(K)),sg(q===!1||r!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let te=n.window!=null&&n.window.document!=null&&typeof n.window.document.startViewTransition=="function";if(sg(O==null||te,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!O||!te){r&&q?r(()=>o(_)):x.startTransition(()=>o(_));return}if(r&&q){r(()=>{y&&(v&&v.resolve(),y.skipTransition()),h({isTransitioning:!0,flushSync:!0,currentLocation:O.currentLocation,nextLocation:O.nextLocation})});let K=n.window.document.startViewTransition(()=>{r(()=>o(_))});K.finished.finally(()=>{r(()=>{g(void 0),S(void 0),f(void 0),h({isTransitioning:!1})})}),r(()=>S(K));return}y?(v&&v.resolve(),y.skipTransition(),T({state:_,currentLocation:O.currentLocation,nextLocation:O.nextLocation})):(f(_),h({isTransitioning:!0,flushSync:!1,currentLocation:O.currentLocation,nextLocation:O.nextLocation}))},[n.window,r,y,v]);x.useLayoutEffect(()=>n.subscribe(N),[n,N]),x.useEffect(()=>{m.isTransitioning&&!m.flushSync&&g(new vw)},[m]),x.useEffect(()=>{if(v&&d&&n.window){let _=d,V=v.promise,q=n.window.document.startViewTransition(async()=>{x.startTransition(()=>o(_)),await V});q.finished.finally(()=>{g(void 0),S(void 0),f(void 0),h({isTransitioning:!1})}),S(q)}},[d,v,n.window]),x.useEffect(()=>{v&&d&&i.location.key===d.location.key&&v.resolve()},[v,y,i.location,d]),x.useEffect(()=>{!m.isTransitioning&&w&&(f(w.state),h({isTransitioning:!0,flushSync:!1,currentLocation:w.currentLocation,nextLocation:w.nextLocation}),T(void 0))},[m.isTransitioning,w]);let C=x.useMemo(()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:_=>n.navigate(_),push:(_,V,q)=>n.navigate(_,{state:V,preventScrollReset:q==null?void 0:q.preventScrollReset}),replace:(_,V,q)=>n.navigate(_,{replace:!0,state:V,preventScrollReset:q==null?void 0:q.preventScrollReset})}),[n]),M=n.basename||"/",P=x.useMemo(()=>({router:n,navigator:C,static:!1,basename:M}),[n,C,M]);return x.createElement(x.Fragment,null,x.createElement(Xl.Provider,{value:P},x.createElement(fs.Provider,{value:i},x.createElement(yp.Provider,{value:j.current},x.createElement(Nf.Provider,{value:m},x.createElement(bw,{basename:M,location:i.location,navigationType:i.historyAction,navigator:C},x.createElement(pw,{routes:n.routes,future:n.future,state:i})))))),null)}var pw=x.memo(yw);function yw({routes:n,future:r,state:i}){return tw(n,void 0,i,r)}function xw(n){return ew(n.context)}function bw({basename:n="/",children:r=null,location:i,navigationType:o="POP",navigator:d,static:f=!1}){ke(!hs(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let m=n.replace(/^\/*/,"/"),h=x.useMemo(()=>({basename:m,navigator:d,static:f,future:{}}),[m,d,f]);typeof i=="string"&&(i=dl(i));let{pathname:v="/",search:g="",hash:y="",state:S=null,key:w="default"}=i,T=x.useMemo(()=>{let j=ja(v,m);return j==null?null:{location:{pathname:j,search:g,hash:y,state:S,key:w},navigationType:o}},[m,v,g,y,S,w,o]);return bt(T!=null,`<Router basename="${m}"> is not able to match the URL "${v}${g}${y}" because it does not start with the basename, so the <Router> won't render anything.`),T==null?null:x.createElement(Wa.Provider,{value:h},x.createElement(mu.Provider,{children:r,value:T}))}var Fo="get",Zo="application/x-www-form-urlencoded";function vu(n){return n!=null&&typeof n.tagName=="string"}function ww(n){return vu(n)&&n.tagName.toLowerCase()==="button"}function Sw(n){return vu(n)&&n.tagName.toLowerCase()==="form"}function Nw(n){return vu(n)&&n.tagName.toLowerCase()==="input"}function Ew(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function jw(n,r){return n.button===0&&(!r||r==="_self")&&!Ew(n)}var Uo=null;function Tw(){if(Uo===null)try{new FormData(document.createElement("form"),0),Uo=!1}catch{Uo=!0}return Uo}var Rw=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Hd(n){return n!=null&&!Rw.has(n)?(bt(!1,`"${n}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Zo}"`),null):n}function Mw(n,r){let i,o,d,f,m;if(Sw(n)){let h=n.getAttribute("action");o=h?ja(h,r):null,i=n.getAttribute("method")||Fo,d=Hd(n.getAttribute("enctype"))||Zo,f=new FormData(n)}else if(ww(n)||Nw(n)&&(n.type==="submit"||n.type==="image")){let h=n.form;if(h==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let v=n.getAttribute("formaction")||h.getAttribute("action");if(o=v?ja(v,r):null,i=n.getAttribute("formmethod")||h.getAttribute("method")||Fo,d=Hd(n.getAttribute("formenctype"))||Hd(h.getAttribute("enctype"))||Zo,f=new FormData(h,n),!Tw()){let{name:g,type:y,value:S}=n;if(y==="image"){let w=g?`${g}.`:"";f.append(`${w}x`,"0"),f.append(`${w}y`,"0")}else g&&f.append(g,S)}}else{if(vu(n))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');i=Fo,o=null,d=Zo,m=n}return f&&d==="text/plain"&&(m=f,f=void 0),{action:o,method:i.toLowerCase(),encType:d,formData:f,body:m}}function Rf(n,r){if(n===!1||n===null||typeof n>"u")throw new Error(r)}async function Aw(n,r){if(n.id in r)return r[n.id];try{let i=await import(n.module);return r[n.id]=i,i}catch(i){return console.error(`Error loading route module \`${n.module}\`, reloading page...`),console.error(i),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Cw(n){return n==null?!1:n.href==null?n.rel==="preload"&&typeof n.imageSrcSet=="string"&&typeof n.imageSizes=="string":typeof n.rel=="string"&&typeof n.href=="string"}async function Dw(n,r,i){let o=await Promise.all(n.map(async d=>{let f=r.routes[d.route.id];if(f){let m=await Aw(f,i);return m.links?m.links():[]}return[]}));return kw(o.flat(1).filter(Cw).filter(d=>d.rel==="stylesheet"||d.rel==="preload").map(d=>d.rel==="stylesheet"?{...d,rel:"prefetch",as:"style"}:{...d,rel:"prefetch"}))}function og(n,r,i,o,d,f){let m=(v,g)=>i[g]?v.route.id!==i[g].route.id:!0,h=(v,g)=>{var y;return i[g].pathname!==v.pathname||((y=i[g].route.path)==null?void 0:y.endsWith("*"))&&i[g].params["*"]!==v.params["*"]};return f==="assets"?r.filter((v,g)=>m(v,g)||h(v,g)):f==="data"?r.filter((v,g)=>{var S;let y=o.routes[v.route.id];if(!y||!y.hasLoader)return!1;if(m(v,g)||h(v,g))return!0;if(v.route.shouldRevalidate){let w=v.route.shouldRevalidate({currentUrl:new URL(d.pathname+d.search+d.hash,window.origin),currentParams:((S=i[0])==null?void 0:S.params)||{},nextUrl:new URL(n,window.origin),nextParams:v.params,defaultShouldRevalidate:!0});if(typeof w=="boolean")return w}return!0}):[]}function Ow(n,r,{includeHydrateFallback:i}={}){return zw(n.map(o=>{let d=r.routes[o.route.id];if(!d)return[];let f=[d.module];return d.clientActionModule&&(f=f.concat(d.clientActionModule)),d.clientLoaderModule&&(f=f.concat(d.clientLoaderModule)),i&&d.hydrateFallbackModule&&(f=f.concat(d.hydrateFallbackModule)),d.imports&&(f=f.concat(d.imports)),f}).flat(1))}function zw(n){return[...new Set(n)]}function _w(n){let r={},i=Object.keys(n).sort();for(let o of i)r[o]=n[o];return r}function kw(n,r){let i=new Set;return new Set(r),n.reduce((o,d)=>{let f=JSON.stringify(_w(d));return i.has(f)||(i.add(f),o.push({key:f,link:d})),o},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Lw=new Set([100,101,204,205]);function Uw(n,r){let i=typeof n=="string"?new URL(n,typeof window>"u"?"server://singlefetch/":window.location.origin):n;return i.pathname==="/"?i.pathname="_root.data":r&&ja(i.pathname,r)==="/"?i.pathname=`${r.replace(/\/$/,"")}/_root.data`:i.pathname=`${i.pathname.replace(/\/$/,"")}.data`,i}function Sp(){let n=x.useContext(Xl);return Rf(n,"You must render this element inside a <DataRouterContext.Provider> element"),n}function Hw(){let n=x.useContext(fs);return Rf(n,"You must render this element inside a <DataRouterStateContext.Provider> element"),n}var Mf=x.createContext(void 0);Mf.displayName="FrameworkContext";function Np(){let n=x.useContext(Mf);return Rf(n,"You must render this element inside a <HydratedRouter> element"),n}function Bw(n,r){let i=x.useContext(Mf),[o,d]=x.useState(!1),[f,m]=x.useState(!1),{onFocus:h,onBlur:v,onMouseEnter:g,onMouseLeave:y,onTouchStart:S}=r,w=x.useRef(null);x.useEffect(()=>{if(n==="render"&&m(!0),n==="viewport"){let N=M=>{M.forEach(P=>{m(P.isIntersecting)})},C=new IntersectionObserver(N,{threshold:.5});return w.current&&C.observe(w.current),()=>{C.disconnect()}}},[n]),x.useEffect(()=>{if(o){let N=setTimeout(()=>{m(!0)},100);return()=>{clearTimeout(N)}}},[o]);let T=()=>{d(!0)},j=()=>{d(!1),m(!1)};return i?n!=="intent"?[f,w,{}]:[f,w,{onFocus:ts(h,T),onBlur:ts(v,j),onMouseEnter:ts(g,T),onMouseLeave:ts(y,j),onTouchStart:ts(S,T)}]:[!1,w,{}]}function ts(n,r){return i=>{n&&n(i),i.defaultPrevented||r(i)}}function qw({page:n,...r}){let{router:i}=Sp(),o=x.useMemo(()=>al(i.routes,n,i.basename),[i.routes,n,i.basename]);return o?x.createElement(Yw,{page:n,matches:o,...r}):null}function Gw(n){let{manifest:r,routeModules:i}=Np(),[o,d]=x.useState([]);return x.useEffect(()=>{let f=!1;return Dw(n,r,i).then(m=>{f||d(m)}),()=>{f=!0}},[n,r,i]),o}function Yw({page:n,matches:r,...i}){let o=Fl(),{manifest:d,routeModules:f}=Np(),{basename:m}=Sp(),{loaderData:h,matches:v}=Hw(),g=x.useMemo(()=>og(n,r,v,d,o,"data"),[n,r,v,d,o]),y=x.useMemo(()=>og(n,r,v,d,o,"assets"),[n,r,v,d,o]),S=x.useMemo(()=>{if(n===o.pathname+o.search+o.hash)return[];let j=new Set,N=!1;if(r.forEach(M=>{var _;let P=d.routes[M.route.id];!P||!P.hasLoader||(!g.some(V=>V.route.id===M.route.id)&&M.route.id in h&&((_=f[M.route.id])!=null&&_.shouldRevalidate)||P.hasClientLoader?N=!0:j.add(M.route.id))}),j.size===0)return[];let C=Uw(n,m);return N&&j.size>0&&C.searchParams.set("_routes",r.filter(M=>j.has(M.route.id)).map(M=>M.route.id).join(",")),[C.pathname+C.search]},[m,h,o,d,g,r,n,f]),w=x.useMemo(()=>Ow(y,d),[y,d]),T=Gw(y);return x.createElement(x.Fragment,null,S.map(j=>x.createElement("link",{key:j,rel:"prefetch",as:"fetch",href:j,...i})),w.map(j=>x.createElement("link",{key:j,rel:"modulepreload",href:j,...i})),T.map(({key:j,link:N})=>x.createElement("link",{key:j,...N})))}function Vw(...n){return r=>{n.forEach(i=>{typeof i=="function"?i(r):i!=null&&(i.current=r)})}}var Ep=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Ep&&(window.__reactRouterVersion="7.6.3")}catch{}function Qw(n,r){return C2({basename:r==null?void 0:r.basename,unstable_getContext:r==null?void 0:r.unstable_getContext,future:r==null?void 0:r.future,history:J1({window:r==null?void 0:r.window}),hydrationData:Kw(),routes:n,mapRouteProperties:hw,hydrationRouteProperties:mw,dataStrategy:r==null?void 0:r.dataStrategy,patchRoutesOnNavigation:r==null?void 0:r.patchRoutesOnNavigation,window:r==null?void 0:r.window}).initialize()}function Kw(){let n=window==null?void 0:window.__staticRouterHydrationData;return n&&n.errors&&(n={...n,errors:Pw(n.errors)}),n}function Pw(n){if(!n)return null;let r=Object.entries(n),i={};for(let[o,d]of r)if(d&&d.__type==="RouteErrorResponse")i[o]=new nu(d.status,d.statusText,d.data,d.internal===!0);else if(d&&d.__type==="Error"){if(d.__subType){let f=window[d.__subType];if(typeof f=="function")try{let m=new f(d.message);m.stack="",i[o]=m}catch{}}if(i[o]==null){let f=new Error(d.message);f.stack="",i[o]=f}}else i[o]=d;return i}var jp=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Tp=x.forwardRef(function({onClick:r,discover:i="render",prefetch:o="none",relative:d,reloadDocument:f,replace:m,state:h,target:v,to:g,preventScrollReset:y,viewTransition:S,...w},T){let{basename:j}=x.useContext(Wa),N=typeof g=="string"&&jp.test(g),C,M=!1;if(typeof g=="string"&&N&&(C=g,Ep))try{let W=new URL(window.location.href),ge=g.startsWith("//")?new URL(W.protocol+g):new URL(g),Le=ja(ge.pathname,j);ge.origin===W.origin&&Le!=null?g=Le+ge.search+ge.hash:M=!0}catch{bt(!1,`<Link to="${g}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let P=$2(g,{relative:d}),[_,V,q]=Bw(o,w),O=Zw(g,{replace:m,state:h,target:v,preventScrollReset:y,relative:d,viewTransition:S});function te(W){r&&r(W),W.defaultPrevented||O(W)}let K=x.createElement("a",{...w,...q,href:C||P,onClick:M||f?r:te,ref:Vw(T,V),target:v,"data-discover":!N&&i==="render"?"true":void 0});return _&&!N?x.createElement(x.Fragment,null,K,x.createElement(qw,{page:P})):K});Tp.displayName="Link";var rt=x.forwardRef(function({"aria-current":r="page",caseSensitive:i=!1,className:o="",end:d=!1,style:f,to:m,viewTransition:h,children:v,...g},y){let S=ms(m,{relative:g.relative}),w=Fl(),T=x.useContext(fs),{navigator:j,basename:N}=x.useContext(Wa),C=T!=null&&eS(S)&&h===!0,M=j.encodeLocation?j.encodeLocation(S).pathname:S.pathname,P=w.pathname,_=T&&T.navigation&&T.navigation.location?T.navigation.location.pathname:null;i||(P=P.toLowerCase(),_=_?_.toLowerCase():null,M=M.toLowerCase()),_&&N&&(_=ja(_,N)||_);const V=M!=="/"&&M.endsWith("/")?M.length-1:M.length;let q=P===M||!d&&P.startsWith(M)&&P.charAt(V)==="/",O=_!=null&&(_===M||!d&&_.startsWith(M)&&_.charAt(M.length)==="/"),te={isActive:q,isPending:O,isTransitioning:C},K=q?r:void 0,W;typeof o=="function"?W=o(te):W=[o,q?"active":null,O?"pending":null,C?"transitioning":null].filter(Boolean).join(" ");let ge=typeof f=="function"?f(te):f;return x.createElement(Tp,{...g,"aria-current":K,className:W,ref:y,style:ge,to:m,viewTransition:h},typeof v=="function"?v(te):v)});rt.displayName="NavLink";var Xw=x.forwardRef(({discover:n="render",fetcherKey:r,navigate:i,reloadDocument:o,replace:d,state:f,method:m=Fo,action:h,onSubmit:v,relative:g,preventScrollReset:y,viewTransition:S,...w},T)=>{let j=Iw(),N=Ww(h,{relative:g}),C=m.toLowerCase()==="get"?"get":"post",M=typeof h=="string"&&jp.test(h),P=_=>{if(v&&v(_),_.defaultPrevented)return;_.preventDefault();let V=_.nativeEvent.submitter,q=(V==null?void 0:V.getAttribute("formmethod"))||m;j(V||_.currentTarget,{fetcherKey:r,method:q,navigate:i,replace:d,state:f,relative:g,preventScrollReset:y,viewTransition:S})};return x.createElement("form",{ref:T,method:C,action:N,onSubmit:o?v:P,...w,"data-discover":!M&&n==="render"?"true":void 0})});Xw.displayName="Form";function Fw(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Rp(n){let r=x.useContext(Xl);return ke(r,Fw(n)),r}function Zw(n,{target:r,replace:i,state:o,preventScrollReset:d,relative:f,viewTransition:m}={}){let h=J2(),v=Fl(),g=ms(n,{relative:f});return x.useCallback(y=>{if(jw(y,r)){y.preventDefault();let S=i!==void 0?i:cl(v)===cl(g);h(n,{replace:S,state:o,preventScrollReset:d,relative:f,viewTransition:m})}},[v,h,g,i,o,r,n,d,f,m])}var $w=0,Jw=()=>`__${String(++$w)}__`;function Iw(){let{router:n}=Rp("useSubmit"),{basename:r}=x.useContext(Wa),i=cw();return x.useCallback(async(o,d={})=>{let{action:f,method:m,encType:h,formData:v,body:g}=Mw(o,r);if(d.navigate===!1){let y=d.fetcherKey||Jw();await n.fetch(y,i,d.action||f,{preventScrollReset:d.preventScrollReset,formData:v,body:g,formMethod:d.method||m,formEncType:d.encType||h,flushSync:d.flushSync})}else await n.navigate(d.action||f,{preventScrollReset:d.preventScrollReset,formData:v,body:g,formMethod:d.method||m,formEncType:d.encType||h,replace:d.replace,state:d.state,fromRouteId:i,flushSync:d.flushSync,viewTransition:d.viewTransition})},[n,r,i])}function Ww(n,{relative:r}={}){let{basename:i}=x.useContext(Wa),o=x.useContext(en);ke(o,"useFormAction must be used inside a RouteContext");let[d]=o.matches.slice(-1),f={...ms(n||".",{relative:r})},m=Fl();if(n==null){f.search=m.search;let h=new URLSearchParams(f.search),v=h.getAll("index");if(v.some(y=>y==="")){h.delete("index"),v.filter(S=>S).forEach(S=>h.append("index",S));let y=h.toString();f.search=y?`?${y}`:""}}return(!n||n===".")&&d.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),i!=="/"&&(f.pathname=f.pathname==="/"?i:Ia([i,f.pathname])),cl(f)}function eS(n,r={}){let i=x.useContext(Nf);ke(i!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=Rp("useViewTransitionState"),d=ms(n,{relative:r.relative});if(!i.isTransitioning)return!1;let f=ja(i.currentLocation.pathname,o)||i.currentLocation.pathname,m=ja(i.nextLocation.pathname,o)||i.nextLocation.pathname;return au(d.pathname,m)!=null||au(d.pathname,f)!=null}[...Lw];const tS=()=>u.jsx("div",{children:u.jsx("footer",{id:"contact",className:"bg-gray-50 pt-20 pb-10",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs("div",{className:"grid md:grid-cols-4 gap-8",children:[u.jsxs("div",{children:[u.jsx("div",{className:"text-2xl font-bold text-red-600 mb-4",children:"TürkTest"}),u.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"Türkçe dil yeterlilik seviyenizi belirlemek ve geliştirmek için profesyonel platform."})]}),u.jsxs("div",{children:[u.jsx("h3",{className:"font-semibold text-gray-900 mb-4",children:"Hızlı Bağlantılar"}),u.jsxs("ul",{className:"space-y-3 text-gray-600",children:[u.jsx("li",{children:u.jsx(rt,{to:"#",className:"hover:text-red-600 transition-colors",children:"Ana Sayfa"})}),u.jsx("li",{children:u.jsx(rt,{to:"#about",className:"hover:text-red-600 transition-colors",children:"Hakkımızda"})}),u.jsx("li",{children:u.jsx(rt,{to:"#features",className:"hover:text-red-600 transition-colors",children:"Özellikler"})}),u.jsx("li",{children:u.jsx(rt,{to:"#pricing",className:"hover:text-red-600 transition-colors",children:"Fiyatlar"})})]})]}),u.jsxs("div",{children:[u.jsx("h3",{className:"font-semibold text-gray-900 mb-4",children:"Test Türleri"}),u.jsxs("ul",{className:"space-y-3 text-gray-600",children:[u.jsx("li",{children:u.jsx(rt,{to:"#",className:"hover:text-red-600 transition-colors",children:"Dinleme Testi"})}),u.jsx("li",{children:u.jsx(rt,{to:"#",className:"hover:text-red-600 transition-colors",children:"Okuma Testi"})}),u.jsx("li",{children:u.jsx(rt,{to:"#",className:"hover:text-red-600 transition-colors",children:"Yazma Testi"})}),u.jsx("li",{children:u.jsx(rt,{to:"#",className:"hover:text-red-600 transition-colors",children:"Konuşma Testi"})})]})]}),u.jsxs("div",{children:[u.jsx("h3",{className:"font-semibold text-gray-900 mb-4",children:"İletişim"}),u.jsxs("div",{className:"text-gray-600 space-y-3",children:[u.jsx("p",{children:"<EMAIL>"}),u.jsx("p",{children:"+90 (212) 123-4567"}),u.jsxs("p",{children:["Türkiye Caddesi No:123",u.jsx("br",{}),"İstanbul, Türkiye 34000"]})]})]})]}),u.jsx("div",{className:"border-t border-gray-200 mt-12 pt-8 text-center text-gray-500",children:u.jsxs("p",{children:["© ",new Date().getFullYear()," TürkTest. Tüm hakları saklıdır."]})})]})})});function ug(n,r){if(typeof n=="function")return n(r);n!=null&&(n.current=r)}function Af(...n){return r=>{let i=!1;const o=n.map(d=>{const f=ug(d,r);return!i&&typeof f=="function"&&(i=!0),f});if(i)return()=>{for(let d=0;d<o.length;d++){const f=o[d];typeof f=="function"?f():ug(n[d],null)}}}}function Kt(...n){return x.useCallback(Af(...n),n)}function us(n){const r=aS(n),i=x.forwardRef((o,d)=>{const{children:f,...m}=o,h=x.Children.toArray(f),v=h.find(lS);if(v){const g=v.props.children,y=h.map(S=>S===v?x.Children.count(g)>1?x.Children.only(null):x.isValidElement(g)?g.props.children:null:S);return u.jsx(r,{...m,ref:d,children:x.isValidElement(g)?x.cloneElement(g,void 0,y):null})}return u.jsx(r,{...m,ref:d,children:f})});return i.displayName=`${n}.Slot`,i}var Mp=us("Slot");function aS(n){const r=x.forwardRef((i,o)=>{const{children:d,...f}=i;if(x.isValidElement(d)){const m=iS(d),h=rS(f,d.props);return d.type!==x.Fragment&&(h.ref=o?Af(o,m):m),x.cloneElement(d,h)}return x.Children.count(d)>1?x.Children.only(null):null});return r.displayName=`${n}.SlotClone`,r}var nS=Symbol("radix.slottable");function lS(n){return x.isValidElement(n)&&typeof n.type=="function"&&"__radixId"in n.type&&n.type.__radixId===nS}function rS(n,r){const i={...r};for(const o in r){const d=n[o],f=r[o];/^on[A-Z]/.test(o)?d&&f?i[o]=(...h)=>{const v=f(...h);return d(...h),v}:d&&(i[o]=d):o==="style"?i[o]={...d,...f}:o==="className"&&(i[o]=[d,f].filter(Boolean).join(" "))}return{...n,...i}}function iS(n){var o,d;let r=(o=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:o.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?n.ref:(r=(d=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:d.get,i=r&&"isReactWarning"in r&&r.isReactWarning,i?n.props.ref:n.props.ref||n.ref)}function Ap(n){var r,i,o="";if(typeof n=="string"||typeof n=="number")o+=n;else if(typeof n=="object")if(Array.isArray(n)){var d=n.length;for(r=0;r<d;r++)n[r]&&(i=Ap(n[r]))&&(o&&(o+=" "),o+=i)}else for(i in n)n[i]&&(o&&(o+=" "),o+=i);return o}function Cp(){for(var n,r,i=0,o="",d=arguments.length;i<d;i++)(n=arguments[i])&&(r=Ap(n))&&(o&&(o+=" "),o+=r);return o}const cg=n=>typeof n=="boolean"?`${n}`:n===0?"0":n,dg=Cp,Cf=(n,r)=>i=>{var o;if((r==null?void 0:r.variants)==null)return dg(n,i==null?void 0:i.class,i==null?void 0:i.className);const{variants:d,defaultVariants:f}=r,m=Object.keys(d).map(g=>{const y=i==null?void 0:i[g],S=f==null?void 0:f[g];if(y===null)return null;const w=cg(y)||cg(S);return d[g][w]}),h=i&&Object.entries(i).reduce((g,y)=>{let[S,w]=y;return w===void 0||(g[S]=w),g},{}),v=r==null||(o=r.compoundVariants)===null||o===void 0?void 0:o.reduce((g,y)=>{let{class:S,className:w,...T}=y;return Object.entries(T).every(j=>{let[N,C]=j;return Array.isArray(C)?C.includes({...f,...h}[N]):{...f,...h}[N]===C})?[...g,S,w]:g},[]);return dg(n,m,v,i==null?void 0:i.class,i==null?void 0:i.className)},Df="-",sS=n=>{const r=uS(n),{conflictingClassGroups:i,conflictingClassGroupModifiers:o}=n;return{getClassGroupId:m=>{const h=m.split(Df);return h[0]===""&&h.length!==1&&h.shift(),Dp(h,r)||oS(m)},getConflictingClassGroupIds:(m,h)=>{const v=i[m]||[];return h&&o[m]?[...v,...o[m]]:v}}},Dp=(n,r)=>{var m;if(n.length===0)return r.classGroupId;const i=n[0],o=r.nextPart.get(i),d=o?Dp(n.slice(1),o):void 0;if(d)return d;if(r.validators.length===0)return;const f=n.join(Df);return(m=r.validators.find(({validator:h})=>h(f)))==null?void 0:m.classGroupId},fg=/^\[(.+)\]$/,oS=n=>{if(fg.test(n)){const r=fg.exec(n)[1],i=r==null?void 0:r.substring(0,r.indexOf(":"));if(i)return"arbitrary.."+i}},uS=n=>{const{theme:r,classGroups:i}=n,o={nextPart:new Map,validators:[]};for(const d in i)nf(i[d],o,d,r);return o},nf=(n,r,i,o)=>{n.forEach(d=>{if(typeof d=="string"){const f=d===""?r:hg(r,d);f.classGroupId=i;return}if(typeof d=="function"){if(cS(d)){nf(d(o),r,i,o);return}r.validators.push({validator:d,classGroupId:i});return}Object.entries(d).forEach(([f,m])=>{nf(m,hg(r,f),i,o)})})},hg=(n,r)=>{let i=n;return r.split(Df).forEach(o=>{i.nextPart.has(o)||i.nextPart.set(o,{nextPart:new Map,validators:[]}),i=i.nextPart.get(o)}),i},cS=n=>n.isThemeGetter,dS=n=>{if(n<1)return{get:()=>{},set:()=>{}};let r=0,i=new Map,o=new Map;const d=(f,m)=>{i.set(f,m),r++,r>n&&(r=0,o=i,i=new Map)};return{get(f){let m=i.get(f);if(m!==void 0)return m;if((m=o.get(f))!==void 0)return d(f,m),m},set(f,m){i.has(f)?i.set(f,m):d(f,m)}}},lf="!",rf=":",fS=rf.length,hS=n=>{const{prefix:r,experimentalParseClassName:i}=n;let o=d=>{const f=[];let m=0,h=0,v=0,g;for(let j=0;j<d.length;j++){let N=d[j];if(m===0&&h===0){if(N===rf){f.push(d.slice(v,j)),v=j+fS;continue}if(N==="/"){g=j;continue}}N==="["?m++:N==="]"?m--:N==="("?h++:N===")"&&h--}const y=f.length===0?d:d.substring(v),S=mS(y),w=S!==y,T=g&&g>v?g-v:void 0;return{modifiers:f,hasImportantModifier:w,baseClassName:S,maybePostfixModifierPosition:T}};if(r){const d=r+rf,f=o;o=m=>m.startsWith(d)?f(m.substring(d.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:m,maybePostfixModifierPosition:void 0}}if(i){const d=o;o=f=>i({className:f,parseClassName:d})}return o},mS=n=>n.endsWith(lf)?n.substring(0,n.length-1):n.startsWith(lf)?n.substring(1):n,vS=n=>{const r=Object.fromEntries(n.orderSensitiveModifiers.map(o=>[o,!0]));return o=>{if(o.length<=1)return o;const d=[];let f=[];return o.forEach(m=>{m[0]==="["||r[m]?(d.push(...f.sort(),m),f=[]):f.push(m)}),d.push(...f.sort()),d}},gS=n=>({cache:dS(n.cacheSize),parseClassName:hS(n),sortModifiers:vS(n),...sS(n)}),pS=/\s+/,yS=(n,r)=>{const{parseClassName:i,getClassGroupId:o,getConflictingClassGroupIds:d,sortModifiers:f}=r,m=[],h=n.trim().split(pS);let v="";for(let g=h.length-1;g>=0;g-=1){const y=h[g],{isExternal:S,modifiers:w,hasImportantModifier:T,baseClassName:j,maybePostfixModifierPosition:N}=i(y);if(S){v=y+(v.length>0?" "+v:v);continue}let C=!!N,M=o(C?j.substring(0,N):j);if(!M){if(!C){v=y+(v.length>0?" "+v:v);continue}if(M=o(j),!M){v=y+(v.length>0?" "+v:v);continue}C=!1}const P=f(w).join(":"),_=T?P+lf:P,V=_+M;if(m.includes(V))continue;m.push(V);const q=d(M,C);for(let O=0;O<q.length;++O){const te=q[O];m.push(_+te)}v=y+(v.length>0?" "+v:v)}return v};function xS(){let n=0,r,i,o="";for(;n<arguments.length;)(r=arguments[n++])&&(i=Op(r))&&(o&&(o+=" "),o+=i);return o}const Op=n=>{if(typeof n=="string")return n;let r,i="";for(let o=0;o<n.length;o++)n[o]&&(r=Op(n[o]))&&(i&&(i+=" "),i+=r);return i};function bS(n,...r){let i,o,d,f=m;function m(v){const g=r.reduce((y,S)=>S(y),n());return i=gS(g),o=i.cache.get,d=i.cache.set,f=h,h(v)}function h(v){const g=o(v);if(g)return g;const y=yS(v,i);return d(v,y),y}return function(){return f(xS.apply(null,arguments))}}const Nt=n=>{const r=i=>i[n]||[];return r.isThemeGetter=!0,r},zp=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,_p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,wS=/^\d+\/\d+$/,SS=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,NS=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,ES=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,jS=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,TS=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Or=n=>wS.test(n),Ce=n=>!!n&&!Number.isNaN(Number(n)),Jn=n=>!!n&&Number.isInteger(Number(n)),Bd=n=>n.endsWith("%")&&Ce(n.slice(0,-1)),xn=n=>SS.test(n),RS=()=>!0,MS=n=>NS.test(n)&&!ES.test(n),kp=()=>!1,AS=n=>jS.test(n),CS=n=>TS.test(n),DS=n=>!oe(n)&&!ue(n),OS=n=>Zr(n,Hp,kp),oe=n=>zp.test(n),kl=n=>Zr(n,Bp,MS),qd=n=>Zr(n,US,Ce),mg=n=>Zr(n,Lp,kp),zS=n=>Zr(n,Up,CS),Ho=n=>Zr(n,qp,AS),ue=n=>_p.test(n),as=n=>$r(n,Bp),_S=n=>$r(n,HS),vg=n=>$r(n,Lp),kS=n=>$r(n,Hp),LS=n=>$r(n,Up),Bo=n=>$r(n,qp,!0),Zr=(n,r,i)=>{const o=zp.exec(n);return o?o[1]?r(o[1]):i(o[2]):!1},$r=(n,r,i=!1)=>{const o=_p.exec(n);return o?o[1]?r(o[1]):i:!1},Lp=n=>n==="position"||n==="percentage",Up=n=>n==="image"||n==="url",Hp=n=>n==="length"||n==="size"||n==="bg-size",Bp=n=>n==="length",US=n=>n==="number",HS=n=>n==="family-name",qp=n=>n==="shadow",BS=()=>{const n=Nt("color"),r=Nt("font"),i=Nt("text"),o=Nt("font-weight"),d=Nt("tracking"),f=Nt("leading"),m=Nt("breakpoint"),h=Nt("container"),v=Nt("spacing"),g=Nt("radius"),y=Nt("shadow"),S=Nt("inset-shadow"),w=Nt("text-shadow"),T=Nt("drop-shadow"),j=Nt("blur"),N=Nt("perspective"),C=Nt("aspect"),M=Nt("ease"),P=Nt("animate"),_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],V=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],q=()=>[...V(),ue,oe],O=()=>["auto","hidden","clip","visible","scroll"],te=()=>["auto","contain","none"],K=()=>[ue,oe,v],W=()=>[Or,"full","auto",...K()],ge=()=>[Jn,"none","subgrid",ue,oe],Le=()=>["auto",{span:["full",Jn,ue,oe]},Jn,ue,oe],qe=()=>[Jn,"auto",ue,oe],pt=()=>["auto","min","max","fr",ue,oe],Me=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Ne=()=>["start","end","center","stretch","center-safe","end-safe"],L=()=>["auto",...K()],ae=()=>[Or,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...K()],J=()=>[n,ue,oe],Ee=()=>[...V(),vg,mg,{position:[ue,oe]}],R=()=>["no-repeat",{repeat:["","x","y","space","round"]}],X=()=>["auto","cover","contain",kS,OS,{size:[ue,oe]}],le=()=>[Bd,as,kl],ee=()=>["","none","full",g,ue,oe],re=()=>["",Ce,as,kl],Te=()=>["solid","dashed","dotted","double"],he=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],He=()=>[Ce,Bd,vg,mg],Ze=()=>["","none",j,ue,oe],Ht=()=>["none",Ce,ue,oe],it=()=>["none",Ce,ue,oe],Bt=()=>[Ce,ue,oe],Ra=()=>[Or,"full",...K()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[xn],breakpoint:[xn],color:[RS],container:[xn],"drop-shadow":[xn],ease:["in","out","in-out"],font:[DS],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[xn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[xn],shadow:[xn],spacing:["px",Ce],text:[xn],"text-shadow":[xn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Or,oe,ue,C]}],container:["container"],columns:[{columns:[Ce,oe,ue,h]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:q()}],overflow:[{overflow:O()}],"overflow-x":[{"overflow-x":O()}],"overflow-y":[{"overflow-y":O()}],overscroll:[{overscroll:te()}],"overscroll-x":[{"overscroll-x":te()}],"overscroll-y":[{"overscroll-y":te()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:W()}],"inset-x":[{"inset-x":W()}],"inset-y":[{"inset-y":W()}],start:[{start:W()}],end:[{end:W()}],top:[{top:W()}],right:[{right:W()}],bottom:[{bottom:W()}],left:[{left:W()}],visibility:["visible","invisible","collapse"],z:[{z:[Jn,"auto",ue,oe]}],basis:[{basis:[Or,"full","auto",h,...K()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Ce,Or,"auto","initial","none",oe]}],grow:[{grow:["",Ce,ue,oe]}],shrink:[{shrink:["",Ce,ue,oe]}],order:[{order:[Jn,"first","last","none",ue,oe]}],"grid-cols":[{"grid-cols":ge()}],"col-start-end":[{col:Le()}],"col-start":[{"col-start":qe()}],"col-end":[{"col-end":qe()}],"grid-rows":[{"grid-rows":ge()}],"row-start-end":[{row:Le()}],"row-start":[{"row-start":qe()}],"row-end":[{"row-end":qe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":pt()}],"auto-rows":[{"auto-rows":pt()}],gap:[{gap:K()}],"gap-x":[{"gap-x":K()}],"gap-y":[{"gap-y":K()}],"justify-content":[{justify:[...Me(),"normal"]}],"justify-items":[{"justify-items":[...Ne(),"normal"]}],"justify-self":[{"justify-self":["auto",...Ne()]}],"align-content":[{content:["normal",...Me()]}],"align-items":[{items:[...Ne(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Ne(),{baseline:["","last"]}]}],"place-content":[{"place-content":Me()}],"place-items":[{"place-items":[...Ne(),"baseline"]}],"place-self":[{"place-self":["auto",...Ne()]}],p:[{p:K()}],px:[{px:K()}],py:[{py:K()}],ps:[{ps:K()}],pe:[{pe:K()}],pt:[{pt:K()}],pr:[{pr:K()}],pb:[{pb:K()}],pl:[{pl:K()}],m:[{m:L()}],mx:[{mx:L()}],my:[{my:L()}],ms:[{ms:L()}],me:[{me:L()}],mt:[{mt:L()}],mr:[{mr:L()}],mb:[{mb:L()}],ml:[{ml:L()}],"space-x":[{"space-x":K()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":K()}],"space-y-reverse":["space-y-reverse"],size:[{size:ae()}],w:[{w:[h,"screen",...ae()]}],"min-w":[{"min-w":[h,"screen","none",...ae()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[m]},...ae()]}],h:[{h:["screen","lh",...ae()]}],"min-h":[{"min-h":["screen","lh","none",...ae()]}],"max-h":[{"max-h":["screen","lh",...ae()]}],"font-size":[{text:["base",i,as,kl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,ue,qd]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Bd,oe]}],"font-family":[{font:[_S,oe,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[d,ue,oe]}],"line-clamp":[{"line-clamp":[Ce,"none",ue,qd]}],leading:[{leading:[f,...K()]}],"list-image":[{"list-image":["none",ue,oe]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ue,oe]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:J()}],"text-color":[{text:J()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Te(),"wavy"]}],"text-decoration-thickness":[{decoration:[Ce,"from-font","auto",ue,kl]}],"text-decoration-color":[{decoration:J()}],"underline-offset":[{"underline-offset":[Ce,"auto",ue,oe]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:K()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ue,oe]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ue,oe]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Ee()}],"bg-repeat":[{bg:R()}],"bg-size":[{bg:X()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Jn,ue,oe],radial:["",ue,oe],conic:[Jn,ue,oe]},LS,zS]}],"bg-color":[{bg:J()}],"gradient-from-pos":[{from:le()}],"gradient-via-pos":[{via:le()}],"gradient-to-pos":[{to:le()}],"gradient-from":[{from:J()}],"gradient-via":[{via:J()}],"gradient-to":[{to:J()}],rounded:[{rounded:ee()}],"rounded-s":[{"rounded-s":ee()}],"rounded-e":[{"rounded-e":ee()}],"rounded-t":[{"rounded-t":ee()}],"rounded-r":[{"rounded-r":ee()}],"rounded-b":[{"rounded-b":ee()}],"rounded-l":[{"rounded-l":ee()}],"rounded-ss":[{"rounded-ss":ee()}],"rounded-se":[{"rounded-se":ee()}],"rounded-ee":[{"rounded-ee":ee()}],"rounded-es":[{"rounded-es":ee()}],"rounded-tl":[{"rounded-tl":ee()}],"rounded-tr":[{"rounded-tr":ee()}],"rounded-br":[{"rounded-br":ee()}],"rounded-bl":[{"rounded-bl":ee()}],"border-w":[{border:re()}],"border-w-x":[{"border-x":re()}],"border-w-y":[{"border-y":re()}],"border-w-s":[{"border-s":re()}],"border-w-e":[{"border-e":re()}],"border-w-t":[{"border-t":re()}],"border-w-r":[{"border-r":re()}],"border-w-b":[{"border-b":re()}],"border-w-l":[{"border-l":re()}],"divide-x":[{"divide-x":re()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":re()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Te(),"hidden","none"]}],"divide-style":[{divide:[...Te(),"hidden","none"]}],"border-color":[{border:J()}],"border-color-x":[{"border-x":J()}],"border-color-y":[{"border-y":J()}],"border-color-s":[{"border-s":J()}],"border-color-e":[{"border-e":J()}],"border-color-t":[{"border-t":J()}],"border-color-r":[{"border-r":J()}],"border-color-b":[{"border-b":J()}],"border-color-l":[{"border-l":J()}],"divide-color":[{divide:J()}],"outline-style":[{outline:[...Te(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Ce,ue,oe]}],"outline-w":[{outline:["",Ce,as,kl]}],"outline-color":[{outline:J()}],shadow:[{shadow:["","none",y,Bo,Ho]}],"shadow-color":[{shadow:J()}],"inset-shadow":[{"inset-shadow":["none",S,Bo,Ho]}],"inset-shadow-color":[{"inset-shadow":J()}],"ring-w":[{ring:re()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:J()}],"ring-offset-w":[{"ring-offset":[Ce,kl]}],"ring-offset-color":[{"ring-offset":J()}],"inset-ring-w":[{"inset-ring":re()}],"inset-ring-color":[{"inset-ring":J()}],"text-shadow":[{"text-shadow":["none",w,Bo,Ho]}],"text-shadow-color":[{"text-shadow":J()}],opacity:[{opacity:[Ce,ue,oe]}],"mix-blend":[{"mix-blend":[...he(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":he()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[Ce]}],"mask-image-linear-from-pos":[{"mask-linear-from":He()}],"mask-image-linear-to-pos":[{"mask-linear-to":He()}],"mask-image-linear-from-color":[{"mask-linear-from":J()}],"mask-image-linear-to-color":[{"mask-linear-to":J()}],"mask-image-t-from-pos":[{"mask-t-from":He()}],"mask-image-t-to-pos":[{"mask-t-to":He()}],"mask-image-t-from-color":[{"mask-t-from":J()}],"mask-image-t-to-color":[{"mask-t-to":J()}],"mask-image-r-from-pos":[{"mask-r-from":He()}],"mask-image-r-to-pos":[{"mask-r-to":He()}],"mask-image-r-from-color":[{"mask-r-from":J()}],"mask-image-r-to-color":[{"mask-r-to":J()}],"mask-image-b-from-pos":[{"mask-b-from":He()}],"mask-image-b-to-pos":[{"mask-b-to":He()}],"mask-image-b-from-color":[{"mask-b-from":J()}],"mask-image-b-to-color":[{"mask-b-to":J()}],"mask-image-l-from-pos":[{"mask-l-from":He()}],"mask-image-l-to-pos":[{"mask-l-to":He()}],"mask-image-l-from-color":[{"mask-l-from":J()}],"mask-image-l-to-color":[{"mask-l-to":J()}],"mask-image-x-from-pos":[{"mask-x-from":He()}],"mask-image-x-to-pos":[{"mask-x-to":He()}],"mask-image-x-from-color":[{"mask-x-from":J()}],"mask-image-x-to-color":[{"mask-x-to":J()}],"mask-image-y-from-pos":[{"mask-y-from":He()}],"mask-image-y-to-pos":[{"mask-y-to":He()}],"mask-image-y-from-color":[{"mask-y-from":J()}],"mask-image-y-to-color":[{"mask-y-to":J()}],"mask-image-radial":[{"mask-radial":[ue,oe]}],"mask-image-radial-from-pos":[{"mask-radial-from":He()}],"mask-image-radial-to-pos":[{"mask-radial-to":He()}],"mask-image-radial-from-color":[{"mask-radial-from":J()}],"mask-image-radial-to-color":[{"mask-radial-to":J()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":V()}],"mask-image-conic-pos":[{"mask-conic":[Ce]}],"mask-image-conic-from-pos":[{"mask-conic-from":He()}],"mask-image-conic-to-pos":[{"mask-conic-to":He()}],"mask-image-conic-from-color":[{"mask-conic-from":J()}],"mask-image-conic-to-color":[{"mask-conic-to":J()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Ee()}],"mask-repeat":[{mask:R()}],"mask-size":[{mask:X()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ue,oe]}],filter:[{filter:["","none",ue,oe]}],blur:[{blur:Ze()}],brightness:[{brightness:[Ce,ue,oe]}],contrast:[{contrast:[Ce,ue,oe]}],"drop-shadow":[{"drop-shadow":["","none",T,Bo,Ho]}],"drop-shadow-color":[{"drop-shadow":J()}],grayscale:[{grayscale:["",Ce,ue,oe]}],"hue-rotate":[{"hue-rotate":[Ce,ue,oe]}],invert:[{invert:["",Ce,ue,oe]}],saturate:[{saturate:[Ce,ue,oe]}],sepia:[{sepia:["",Ce,ue,oe]}],"backdrop-filter":[{"backdrop-filter":["","none",ue,oe]}],"backdrop-blur":[{"backdrop-blur":Ze()}],"backdrop-brightness":[{"backdrop-brightness":[Ce,ue,oe]}],"backdrop-contrast":[{"backdrop-contrast":[Ce,ue,oe]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Ce,ue,oe]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Ce,ue,oe]}],"backdrop-invert":[{"backdrop-invert":["",Ce,ue,oe]}],"backdrop-opacity":[{"backdrop-opacity":[Ce,ue,oe]}],"backdrop-saturate":[{"backdrop-saturate":[Ce,ue,oe]}],"backdrop-sepia":[{"backdrop-sepia":["",Ce,ue,oe]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":K()}],"border-spacing-x":[{"border-spacing-x":K()}],"border-spacing-y":[{"border-spacing-y":K()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ue,oe]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Ce,"initial",ue,oe]}],ease:[{ease:["linear","initial",M,ue,oe]}],delay:[{delay:[Ce,ue,oe]}],animate:[{animate:["none",P,ue,oe]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[N,ue,oe]}],"perspective-origin":[{"perspective-origin":q()}],rotate:[{rotate:Ht()}],"rotate-x":[{"rotate-x":Ht()}],"rotate-y":[{"rotate-y":Ht()}],"rotate-z":[{"rotate-z":Ht()}],scale:[{scale:it()}],"scale-x":[{"scale-x":it()}],"scale-y":[{"scale-y":it()}],"scale-z":[{"scale-z":it()}],"scale-3d":["scale-3d"],skew:[{skew:Bt()}],"skew-x":[{"skew-x":Bt()}],"skew-y":[{"skew-y":Bt()}],transform:[{transform:[ue,oe,"","none","gpu","cpu"]}],"transform-origin":[{origin:q()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ra()}],"translate-x":[{"translate-x":Ra()}],"translate-y":[{"translate-y":Ra()}],"translate-z":[{"translate-z":Ra()}],"translate-none":["translate-none"],accent:[{accent:J()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:J()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ue,oe]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":K()}],"scroll-mx":[{"scroll-mx":K()}],"scroll-my":[{"scroll-my":K()}],"scroll-ms":[{"scroll-ms":K()}],"scroll-me":[{"scroll-me":K()}],"scroll-mt":[{"scroll-mt":K()}],"scroll-mr":[{"scroll-mr":K()}],"scroll-mb":[{"scroll-mb":K()}],"scroll-ml":[{"scroll-ml":K()}],"scroll-p":[{"scroll-p":K()}],"scroll-px":[{"scroll-px":K()}],"scroll-py":[{"scroll-py":K()}],"scroll-ps":[{"scroll-ps":K()}],"scroll-pe":[{"scroll-pe":K()}],"scroll-pt":[{"scroll-pt":K()}],"scroll-pr":[{"scroll-pr":K()}],"scroll-pb":[{"scroll-pb":K()}],"scroll-pl":[{"scroll-pl":K()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ue,oe]}],fill:[{fill:["none",...J()]}],"stroke-w":[{stroke:[Ce,as,kl,qd]}],stroke:[{stroke:["none",...J()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},qS=bS(BS);function nt(...n){return qS(Cp(n))}const GS=Cf("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function Et({className:n,variant:r,size:i,asChild:o=!1,...d}){const f=o?Mp:"button";return u.jsx(f,{"data-slot":"button",className:nt(GS({variant:r,size:i,className:n})),...d})}function Ke(n,r,{checkForDefaultPrevented:i=!0}={}){return function(d){if(n==null||n(d),i===!1||!d.defaultPrevented)return r==null?void 0:r(d)}}function YS(n,r){const i=x.createContext(r),o=f=>{const{children:m,...h}=f,v=x.useMemo(()=>h,Object.values(h));return u.jsx(i.Provider,{value:v,children:m})};o.displayName=n+"Provider";function d(f){const m=x.useContext(i);if(m)return m;if(r!==void 0)return r;throw new Error(`\`${f}\` must be used within \`${n}\``)}return[o,d]}function Jr(n,r=[]){let i=[];function o(f,m){const h=x.createContext(m),v=i.length;i=[...i,m];const g=S=>{var M;const{scope:w,children:T,...j}=S,N=((M=w==null?void 0:w[n])==null?void 0:M[v])||h,C=x.useMemo(()=>j,Object.values(j));return u.jsx(N.Provider,{value:C,children:T})};g.displayName=f+"Provider";function y(S,w){var N;const T=((N=w==null?void 0:w[n])==null?void 0:N[v])||h,j=x.useContext(T);if(j)return j;if(m!==void 0)return m;throw new Error(`\`${S}\` must be used within \`${f}\``)}return[g,y]}const d=()=>{const f=i.map(m=>x.createContext(m));return function(h){const v=(h==null?void 0:h[n])||f;return x.useMemo(()=>({[`__scope${n}`]:{...h,[n]:v}}),[h,v])}};return d.scopeName=n,[o,VS(d,...r)]}function VS(...n){const r=n[0];if(n.length===1)return r;const i=()=>{const o=n.map(d=>({useScope:d(),scopeName:d.scopeName}));return function(f){const m=o.reduce((h,{useScope:v,scopeName:g})=>{const S=v(f)[`__scope${g}`];return{...h,...S}},{});return x.useMemo(()=>({[`__scope${r.scopeName}`]:m}),[m])}};return i.scopeName=r.scopeName,i}var Ha=globalThis!=null&&globalThis.document?x.useLayoutEffect:()=>{},QS=ap[" useId ".trim().toString()]||(()=>{}),KS=0;function Kl(n){const[r,i]=x.useState(QS());return Ha(()=>{i(o=>o??String(KS++))},[n]),n||(r?`radix-${r}`:"")}var PS=ap[" useInsertionEffect ".trim().toString()]||Ha;function vs({prop:n,defaultProp:r,onChange:i=()=>{},caller:o}){const[d,f,m]=XS({defaultProp:r,onChange:i}),h=n!==void 0,v=h?n:d;{const y=x.useRef(n!==void 0);x.useEffect(()=>{const S=y.current;S!==h&&console.warn(`${o} is changing from ${S?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),y.current=h},[h,o])}const g=x.useCallback(y=>{var S;if(h){const w=FS(y)?y(n):y;w!==n&&((S=m.current)==null||S.call(m,w))}else f(y)},[h,n,f,m]);return[v,g]}function XS({defaultProp:n,onChange:r}){const[i,o]=x.useState(n),d=x.useRef(i),f=x.useRef(r);return PS(()=>{f.current=r},[r]),x.useEffect(()=>{var m;d.current!==i&&((m=f.current)==null||m.call(f,i),d.current=i)},[i,d]),[i,o,f]}function FS(n){return typeof n=="function"}var Gp=Fg();const Yp=Xg(Gp);var ZS=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Xe=ZS.reduce((n,r)=>{const i=us(`Primitive.${r}`),o=x.forwardRef((d,f)=>{const{asChild:m,...h}=d,v=m?i:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(v,{...h,ref:f})});return o.displayName=`Primitive.${r}`,{...n,[r]:o}},{});function sf(n,r){n&&Gp.flushSync(()=>n.dispatchEvent(r))}function Wt(n){const r=x.useRef(n);return x.useEffect(()=>{r.current=n}),x.useMemo(()=>(...i)=>{var o;return(o=r.current)==null?void 0:o.call(r,...i)},[])}function $S(n,r=globalThis==null?void 0:globalThis.document){const i=Wt(n);x.useEffect(()=>{const o=d=>{d.key==="Escape"&&i(d)};return r.addEventListener("keydown",o,{capture:!0}),()=>r.removeEventListener("keydown",o,{capture:!0})},[i,r])}var JS="DismissableLayer",of="dismissableLayer.update",IS="dismissableLayer.pointerDownOutside",WS="dismissableLayer.focusOutside",gg,Vp=x.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Of=x.forwardRef((n,r)=>{const{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:o,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:m,onDismiss:h,...v}=n,g=x.useContext(Vp),[y,S]=x.useState(null),w=(y==null?void 0:y.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,T]=x.useState({}),j=Kt(r,te=>S(te)),N=Array.from(g.layers),[C]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),M=N.indexOf(C),P=y?N.indexOf(y):-1,_=g.layersWithOutsidePointerEventsDisabled.size>0,V=P>=M,q=aN(te=>{const K=te.target,W=[...g.branches].some(ge=>ge.contains(K));!V||W||(d==null||d(te),m==null||m(te),te.defaultPrevented||h==null||h())},w),O=nN(te=>{const K=te.target;[...g.branches].some(ge=>ge.contains(K))||(f==null||f(te),m==null||m(te),te.defaultPrevented||h==null||h())},w);return $S(te=>{P===g.layers.size-1&&(o==null||o(te),!te.defaultPrevented&&h&&(te.preventDefault(),h()))},w),x.useEffect(()=>{if(y)return i&&(g.layersWithOutsidePointerEventsDisabled.size===0&&(gg=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(y)),g.layers.add(y),pg(),()=>{i&&g.layersWithOutsidePointerEventsDisabled.size===1&&(w.body.style.pointerEvents=gg)}},[y,w,i,g]),x.useEffect(()=>()=>{y&&(g.layers.delete(y),g.layersWithOutsidePointerEventsDisabled.delete(y),pg())},[y,g]),x.useEffect(()=>{const te=()=>T({});return document.addEventListener(of,te),()=>document.removeEventListener(of,te)},[]),u.jsx(Xe.div,{...v,ref:j,style:{pointerEvents:_?V?"auto":"none":void 0,...n.style},onFocusCapture:Ke(n.onFocusCapture,O.onFocusCapture),onBlurCapture:Ke(n.onBlurCapture,O.onBlurCapture),onPointerDownCapture:Ke(n.onPointerDownCapture,q.onPointerDownCapture)})});Of.displayName=JS;var eN="DismissableLayerBranch",tN=x.forwardRef((n,r)=>{const i=x.useContext(Vp),o=x.useRef(null),d=Kt(r,o);return x.useEffect(()=>{const f=o.current;if(f)return i.branches.add(f),()=>{i.branches.delete(f)}},[i.branches]),u.jsx(Xe.div,{...n,ref:d})});tN.displayName=eN;function aN(n,r=globalThis==null?void 0:globalThis.document){const i=Wt(n),o=x.useRef(!1),d=x.useRef(()=>{});return x.useEffect(()=>{const f=h=>{if(h.target&&!o.current){let v=function(){Qp(IS,i,g,{discrete:!0})};const g={originalEvent:h};h.pointerType==="touch"?(r.removeEventListener("click",d.current),d.current=v,r.addEventListener("click",d.current,{once:!0})):v()}else r.removeEventListener("click",d.current);o.current=!1},m=window.setTimeout(()=>{r.addEventListener("pointerdown",f)},0);return()=>{window.clearTimeout(m),r.removeEventListener("pointerdown",f),r.removeEventListener("click",d.current)}},[r,i]),{onPointerDownCapture:()=>o.current=!0}}function nN(n,r=globalThis==null?void 0:globalThis.document){const i=Wt(n),o=x.useRef(!1);return x.useEffect(()=>{const d=f=>{f.target&&!o.current&&Qp(WS,i,{originalEvent:f},{discrete:!1})};return r.addEventListener("focusin",d),()=>r.removeEventListener("focusin",d)},[r,i]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function pg(){const n=new CustomEvent(of);document.dispatchEvent(n)}function Qp(n,r,i,{discrete:o}){const d=i.originalEvent.target,f=new CustomEvent(n,{bubbles:!1,cancelable:!0,detail:i});r&&d.addEventListener(n,r,{once:!0}),o?sf(d,f):d.dispatchEvent(f)}var Gd="focusScope.autoFocusOnMount",Yd="focusScope.autoFocusOnUnmount",yg={bubbles:!1,cancelable:!0},lN="FocusScope",Kp=x.forwardRef((n,r)=>{const{loop:i=!1,trapped:o=!1,onMountAutoFocus:d,onUnmountAutoFocus:f,...m}=n,[h,v]=x.useState(null),g=Wt(d),y=Wt(f),S=x.useRef(null),w=Kt(r,N=>v(N)),T=x.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;x.useEffect(()=>{if(o){let N=function(_){if(T.paused||!h)return;const V=_.target;h.contains(V)?S.current=V:tl(S.current,{select:!0})},C=function(_){if(T.paused||!h)return;const V=_.relatedTarget;V!==null&&(h.contains(V)||tl(S.current,{select:!0}))},M=function(_){if(document.activeElement===document.body)for(const q of _)q.removedNodes.length>0&&tl(h)};document.addEventListener("focusin",N),document.addEventListener("focusout",C);const P=new MutationObserver(M);return h&&P.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",N),document.removeEventListener("focusout",C),P.disconnect()}}},[o,h,T.paused]),x.useEffect(()=>{if(h){bg.add(T);const N=document.activeElement;if(!h.contains(N)){const M=new CustomEvent(Gd,yg);h.addEventListener(Gd,g),h.dispatchEvent(M),M.defaultPrevented||(rN(cN(Pp(h)),{select:!0}),document.activeElement===N&&tl(h))}return()=>{h.removeEventListener(Gd,g),setTimeout(()=>{const M=new CustomEvent(Yd,yg);h.addEventListener(Yd,y),h.dispatchEvent(M),M.defaultPrevented||tl(N??document.body,{select:!0}),h.removeEventListener(Yd,y),bg.remove(T)},0)}}},[h,g,y,T]);const j=x.useCallback(N=>{if(!i&&!o||T.paused)return;const C=N.key==="Tab"&&!N.altKey&&!N.ctrlKey&&!N.metaKey,M=document.activeElement;if(C&&M){const P=N.currentTarget,[_,V]=iN(P);_&&V?!N.shiftKey&&M===V?(N.preventDefault(),i&&tl(_,{select:!0})):N.shiftKey&&M===_&&(N.preventDefault(),i&&tl(V,{select:!0})):M===P&&N.preventDefault()}},[i,o,T.paused]);return u.jsx(Xe.div,{tabIndex:-1,...m,ref:w,onKeyDown:j})});Kp.displayName=lN;function rN(n,{select:r=!1}={}){const i=document.activeElement;for(const o of n)if(tl(o,{select:r}),document.activeElement!==i)return}function iN(n){const r=Pp(n),i=xg(r,n),o=xg(r.reverse(),n);return[i,o]}function Pp(n){const r=[],i=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const d=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||d?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;i.nextNode();)r.push(i.currentNode);return r}function xg(n,r){for(const i of n)if(!sN(i,{upTo:r}))return i}function sN(n,{upTo:r}){if(getComputedStyle(n).visibility==="hidden")return!0;for(;n;){if(r!==void 0&&n===r)return!1;if(getComputedStyle(n).display==="none")return!0;n=n.parentElement}return!1}function oN(n){return n instanceof HTMLInputElement&&"select"in n}function tl(n,{select:r=!1}={}){if(n&&n.focus){const i=document.activeElement;n.focus({preventScroll:!0}),n!==i&&oN(n)&&r&&n.select()}}var bg=uN();function uN(){let n=[];return{add(r){const i=n[0];r!==i&&(i==null||i.pause()),n=wg(n,r),n.unshift(r)},remove(r){var i;n=wg(n,r),(i=n[0])==null||i.resume()}}}function wg(n,r){const i=[...n],o=i.indexOf(r);return o!==-1&&i.splice(o,1),i}function cN(n){return n.filter(r=>r.tagName!=="A")}var dN="Portal",Xp=x.forwardRef((n,r)=>{var h;const{container:i,...o}=n,[d,f]=x.useState(!1);Ha(()=>f(!0),[]);const m=i||d&&((h=globalThis==null?void 0:globalThis.document)==null?void 0:h.body);return m?Yp.createPortal(u.jsx(Xe.div,{...o,ref:r}),m):null});Xp.displayName=dN;function fN(n,r){return x.useReducer((i,o)=>r[i][o]??i,n)}var Nn=n=>{const{present:r,children:i}=n,o=hN(r),d=typeof i=="function"?i({present:o.isPresent}):x.Children.only(i),f=Kt(o.ref,mN(d));return typeof i=="function"||o.isPresent?x.cloneElement(d,{ref:f}):null};Nn.displayName="Presence";function hN(n){const[r,i]=x.useState(),o=x.useRef(null),d=x.useRef(n),f=x.useRef("none"),m=n?"mounted":"unmounted",[h,v]=fN(m,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return x.useEffect(()=>{const g=qo(o.current);f.current=h==="mounted"?g:"none"},[h]),Ha(()=>{const g=o.current,y=d.current;if(y!==n){const w=f.current,T=qo(g);n?v("MOUNT"):T==="none"||(g==null?void 0:g.display)==="none"?v("UNMOUNT"):v(y&&w!==T?"ANIMATION_OUT":"UNMOUNT"),d.current=n}},[n,v]),Ha(()=>{if(r){let g;const y=r.ownerDocument.defaultView??window,S=T=>{const N=qo(o.current).includes(T.animationName);if(T.target===r&&N&&(v("ANIMATION_END"),!d.current)){const C=r.style.animationFillMode;r.style.animationFillMode="forwards",g=y.setTimeout(()=>{r.style.animationFillMode==="forwards"&&(r.style.animationFillMode=C)})}},w=T=>{T.target===r&&(f.current=qo(o.current))};return r.addEventListener("animationstart",w),r.addEventListener("animationcancel",S),r.addEventListener("animationend",S),()=>{y.clearTimeout(g),r.removeEventListener("animationstart",w),r.removeEventListener("animationcancel",S),r.removeEventListener("animationend",S)}}else v("ANIMATION_END")},[r,v]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:x.useCallback(g=>{o.current=g?getComputedStyle(g):null,i(g)},[])}}function qo(n){return(n==null?void 0:n.animationName)||"none"}function mN(n){var o,d;let r=(o=Object.getOwnPropertyDescriptor(n.props,"ref"))==null?void 0:o.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?n.ref:(r=(d=Object.getOwnPropertyDescriptor(n,"ref"))==null?void 0:d.get,i=r&&"isReactWarning"in r&&r.isReactWarning,i?n.props.ref:n.props.ref||n.ref)}var Vd=0;function vN(){x.useEffect(()=>{const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",n[0]??Sg()),document.body.insertAdjacentElement("beforeend",n[1]??Sg()),Vd++,()=>{Vd===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),Vd--}},[])}function Sg(){const n=document.createElement("span");return n.setAttribute("data-radix-focus-guard",""),n.tabIndex=0,n.style.outline="none",n.style.opacity="0",n.style.position="fixed",n.style.pointerEvents="none",n}var Ja=function(){return Ja=Object.assign||function(r){for(var i,o=1,d=arguments.length;o<d;o++){i=arguments[o];for(var f in i)Object.prototype.hasOwnProperty.call(i,f)&&(r[f]=i[f])}return r},Ja.apply(this,arguments)};function Fp(n,r){var i={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&r.indexOf(o)<0&&(i[o]=n[o]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,o=Object.getOwnPropertySymbols(n);d<o.length;d++)r.indexOf(o[d])<0&&Object.prototype.propertyIsEnumerable.call(n,o[d])&&(i[o[d]]=n[o[d]]);return i}function gN(n,r,i){if(i||arguments.length===2)for(var o=0,d=r.length,f;o<d;o++)(f||!(o in r))&&(f||(f=Array.prototype.slice.call(r,0,o)),f[o]=r[o]);return n.concat(f||Array.prototype.slice.call(r))}var $o="right-scroll-bar-position",Jo="width-before-scroll-bar",pN="with-scroll-bars-hidden",yN="--removed-body-scroll-bar-size";function Qd(n,r){return typeof n=="function"?n(r):n&&(n.current=r),n}function xN(n,r){var i=x.useState(function(){return{value:n,callback:r,facade:{get current(){return i.value},set current(o){var d=i.value;d!==o&&(i.value=o,i.callback(o,d))}}}})[0];return i.callback=r,i.facade}var bN=typeof window<"u"?x.useLayoutEffect:x.useEffect,Ng=new WeakMap;function wN(n,r){var i=xN(null,function(o){return n.forEach(function(d){return Qd(d,o)})});return bN(function(){var o=Ng.get(i);if(o){var d=new Set(o),f=new Set(n),m=i.current;d.forEach(function(h){f.has(h)||Qd(h,null)}),f.forEach(function(h){d.has(h)||Qd(h,m)})}Ng.set(i,n)},[n]),i}function SN(n){return n}function NN(n,r){r===void 0&&(r=SN);var i=[],o=!1,d={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:n},useMedium:function(f){var m=r(f,o);return i.push(m),function(){i=i.filter(function(h){return h!==m})}},assignSyncMedium:function(f){for(o=!0;i.length;){var m=i;i=[],m.forEach(f)}i={push:function(h){return f(h)},filter:function(){return i}}},assignMedium:function(f){o=!0;var m=[];if(i.length){var h=i;i=[],h.forEach(f),m=i}var v=function(){var y=m;m=[],y.forEach(f)},g=function(){return Promise.resolve().then(v)};g(),i={push:function(y){m.push(y),g()},filter:function(y){return m=m.filter(y),i}}}};return d}function EN(n){n===void 0&&(n={});var r=NN(null);return r.options=Ja({async:!0,ssr:!1},n),r}var Zp=function(n){var r=n.sideCar,i=Fp(n,["sideCar"]);if(!r)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=r.read();if(!o)throw new Error("Sidecar medium not found");return x.createElement(o,Ja({},i))};Zp.isSideCarExport=!0;function jN(n,r){return n.useMedium(r),Zp}var $p=EN(),Kd=function(){},gu=x.forwardRef(function(n,r){var i=x.useRef(null),o=x.useState({onScrollCapture:Kd,onWheelCapture:Kd,onTouchMoveCapture:Kd}),d=o[0],f=o[1],m=n.forwardProps,h=n.children,v=n.className,g=n.removeScrollBar,y=n.enabled,S=n.shards,w=n.sideCar,T=n.noRelative,j=n.noIsolation,N=n.inert,C=n.allowPinchZoom,M=n.as,P=M===void 0?"div":M,_=n.gapMode,V=Fp(n,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),q=w,O=wN([i,r]),te=Ja(Ja({},V),d);return x.createElement(x.Fragment,null,y&&x.createElement(q,{sideCar:$p,removeScrollBar:g,shards:S,noRelative:T,noIsolation:j,inert:N,setCallbacks:f,allowPinchZoom:!!C,lockRef:i,gapMode:_}),m?x.cloneElement(x.Children.only(h),Ja(Ja({},te),{ref:O})):x.createElement(P,Ja({},te,{className:v,ref:O}),h))});gu.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};gu.classNames={fullWidth:Jo,zeroRight:$o};var TN=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function RN(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var r=TN();return r&&n.setAttribute("nonce",r),n}function MN(n,r){n.styleSheet?n.styleSheet.cssText=r:n.appendChild(document.createTextNode(r))}function AN(n){var r=document.head||document.getElementsByTagName("head")[0];r.appendChild(n)}var CN=function(){var n=0,r=null;return{add:function(i){n==0&&(r=RN())&&(MN(r,i),AN(r)),n++},remove:function(){n--,!n&&r&&(r.parentNode&&r.parentNode.removeChild(r),r=null)}}},DN=function(){var n=CN();return function(r,i){x.useEffect(function(){return n.add(r),function(){n.remove()}},[r&&i])}},Jp=function(){var n=DN(),r=function(i){var o=i.styles,d=i.dynamic;return n(o,d),null};return r},ON={left:0,top:0,right:0,gap:0},Pd=function(n){return parseInt(n||"",10)||0},zN=function(n){var r=window.getComputedStyle(document.body),i=r[n==="padding"?"paddingLeft":"marginLeft"],o=r[n==="padding"?"paddingTop":"marginTop"],d=r[n==="padding"?"paddingRight":"marginRight"];return[Pd(i),Pd(o),Pd(d)]},_N=function(n){if(n===void 0&&(n="margin"),typeof window>"u")return ON;var r=zN(n),i=document.documentElement.clientWidth,o=window.innerWidth;return{left:r[0],top:r[1],right:r[2],gap:Math.max(0,o-i+r[2]-r[0])}},kN=Jp(),Br="data-scroll-locked",LN=function(n,r,i,o){var d=n.left,f=n.top,m=n.right,h=n.gap;return i===void 0&&(i="margin"),`
  .`.concat(pN,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(h,"px ").concat(o,`;
  }
  body[`).concat(Br,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([r&&"position: relative ".concat(o,";"),i==="margin"&&`
    padding-left: `.concat(d,`px;
    padding-top: `).concat(f,`px;
    padding-right: `).concat(m,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(o,`;
    `),i==="padding"&&"padding-right: ".concat(h,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat($o,` {
    right: `).concat(h,"px ").concat(o,`;
  }
  
  .`).concat(Jo,` {
    margin-right: `).concat(h,"px ").concat(o,`;
  }
  
  .`).concat($o," .").concat($o,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(Jo," .").concat(Jo,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(Br,`] {
    `).concat(yN,": ").concat(h,`px;
  }
`)},Eg=function(){var n=parseInt(document.body.getAttribute(Br)||"0",10);return isFinite(n)?n:0},UN=function(){x.useEffect(function(){return document.body.setAttribute(Br,(Eg()+1).toString()),function(){var n=Eg()-1;n<=0?document.body.removeAttribute(Br):document.body.setAttribute(Br,n.toString())}},[])},HN=function(n){var r=n.noRelative,i=n.noImportant,o=n.gapMode,d=o===void 0?"margin":o;UN();var f=x.useMemo(function(){return _N(d)},[d]);return x.createElement(kN,{styles:LN(f,!r,d,i?"":"!important")})},uf=!1;if(typeof window<"u")try{var Go=Object.defineProperty({},"passive",{get:function(){return uf=!0,!0}});window.addEventListener("test",Go,Go),window.removeEventListener("test",Go,Go)}catch{uf=!1}var zr=uf?{passive:!1}:!1,BN=function(n){return n.tagName==="TEXTAREA"},Ip=function(n,r){if(!(n instanceof Element))return!1;var i=window.getComputedStyle(n);return i[r]!=="hidden"&&!(i.overflowY===i.overflowX&&!BN(n)&&i[r]==="visible")},qN=function(n){return Ip(n,"overflowY")},GN=function(n){return Ip(n,"overflowX")},jg=function(n,r){var i=r.ownerDocument,o=r;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var d=Wp(n,o);if(d){var f=ey(n,o),m=f[1],h=f[2];if(m>h)return!0}o=o.parentNode}while(o&&o!==i.body);return!1},YN=function(n){var r=n.scrollTop,i=n.scrollHeight,o=n.clientHeight;return[r,i,o]},VN=function(n){var r=n.scrollLeft,i=n.scrollWidth,o=n.clientWidth;return[r,i,o]},Wp=function(n,r){return n==="v"?qN(r):GN(r)},ey=function(n,r){return n==="v"?YN(r):VN(r)},QN=function(n,r){return n==="h"&&r==="rtl"?-1:1},KN=function(n,r,i,o,d){var f=QN(n,window.getComputedStyle(r).direction),m=f*o,h=i.target,v=r.contains(h),g=!1,y=m>0,S=0,w=0;do{if(!h)break;var T=ey(n,h),j=T[0],N=T[1],C=T[2],M=N-C-f*j;(j||M)&&Wp(n,h)&&(S+=M,w+=j);var P=h.parentNode;h=P&&P.nodeType===Node.DOCUMENT_FRAGMENT_NODE?P.host:P}while(!v&&h!==document.body||v&&(r.contains(h)||r===h));return(y&&Math.abs(S)<1||!y&&Math.abs(w)<1)&&(g=!0),g},Yo=function(n){return"changedTouches"in n?[n.changedTouches[0].clientX,n.changedTouches[0].clientY]:[0,0]},Tg=function(n){return[n.deltaX,n.deltaY]},Rg=function(n){return n&&"current"in n?n.current:n},PN=function(n,r){return n[0]===r[0]&&n[1]===r[1]},XN=function(n){return`
  .block-interactivity-`.concat(n,` {pointer-events: none;}
  .allow-interactivity-`).concat(n,` {pointer-events: all;}
`)},FN=0,_r=[];function ZN(n){var r=x.useRef([]),i=x.useRef([0,0]),o=x.useRef(),d=x.useState(FN++)[0],f=x.useState(Jp)[0],m=x.useRef(n);x.useEffect(function(){m.current=n},[n]),x.useEffect(function(){if(n.inert){document.body.classList.add("block-interactivity-".concat(d));var N=gN([n.lockRef.current],(n.shards||[]).map(Rg),!0).filter(Boolean);return N.forEach(function(C){return C.classList.add("allow-interactivity-".concat(d))}),function(){document.body.classList.remove("block-interactivity-".concat(d)),N.forEach(function(C){return C.classList.remove("allow-interactivity-".concat(d))})}}},[n.inert,n.lockRef.current,n.shards]);var h=x.useCallback(function(N,C){if("touches"in N&&N.touches.length===2||N.type==="wheel"&&N.ctrlKey)return!m.current.allowPinchZoom;var M=Yo(N),P=i.current,_="deltaX"in N?N.deltaX:P[0]-M[0],V="deltaY"in N?N.deltaY:P[1]-M[1],q,O=N.target,te=Math.abs(_)>Math.abs(V)?"h":"v";if("touches"in N&&te==="h"&&O.type==="range")return!1;var K=jg(te,O);if(!K)return!0;if(K?q=te:(q=te==="v"?"h":"v",K=jg(te,O)),!K)return!1;if(!o.current&&"changedTouches"in N&&(_||V)&&(o.current=q),!q)return!0;var W=o.current||q;return KN(W,C,N,W==="h"?_:V)},[]),v=x.useCallback(function(N){var C=N;if(!(!_r.length||_r[_r.length-1]!==f)){var M="deltaY"in C?Tg(C):Yo(C),P=r.current.filter(function(q){return q.name===C.type&&(q.target===C.target||C.target===q.shadowParent)&&PN(q.delta,M)})[0];if(P&&P.should){C.cancelable&&C.preventDefault();return}if(!P){var _=(m.current.shards||[]).map(Rg).filter(Boolean).filter(function(q){return q.contains(C.target)}),V=_.length>0?h(C,_[0]):!m.current.noIsolation;V&&C.cancelable&&C.preventDefault()}}},[]),g=x.useCallback(function(N,C,M,P){var _={name:N,delta:C,target:M,should:P,shadowParent:$N(M)};r.current.push(_),setTimeout(function(){r.current=r.current.filter(function(V){return V!==_})},1)},[]),y=x.useCallback(function(N){i.current=Yo(N),o.current=void 0},[]),S=x.useCallback(function(N){g(N.type,Tg(N),N.target,h(N,n.lockRef.current))},[]),w=x.useCallback(function(N){g(N.type,Yo(N),N.target,h(N,n.lockRef.current))},[]);x.useEffect(function(){return _r.push(f),n.setCallbacks({onScrollCapture:S,onWheelCapture:S,onTouchMoveCapture:w}),document.addEventListener("wheel",v,zr),document.addEventListener("touchmove",v,zr),document.addEventListener("touchstart",y,zr),function(){_r=_r.filter(function(N){return N!==f}),document.removeEventListener("wheel",v,zr),document.removeEventListener("touchmove",v,zr),document.removeEventListener("touchstart",y,zr)}},[]);var T=n.removeScrollBar,j=n.inert;return x.createElement(x.Fragment,null,j?x.createElement(f,{styles:XN(d)}):null,T?x.createElement(HN,{noRelative:n.noRelative,gapMode:n.gapMode}):null)}function $N(n){for(var r=null;n!==null;)n instanceof ShadowRoot&&(r=n.host,n=n.host),n=n.parentNode;return r}const JN=jN($p,ZN);var ty=x.forwardRef(function(n,r){return x.createElement(gu,Ja({},n,{ref:r,sideCar:JN}))});ty.classNames=gu.classNames;var IN=function(n){if(typeof document>"u")return null;var r=Array.isArray(n)?n[0]:n;return r.ownerDocument.body},kr=new WeakMap,Vo=new WeakMap,Qo={},Xd=0,ay=function(n){return n&&(n.host||ay(n.parentNode))},WN=function(n,r){return r.map(function(i){if(n.contains(i))return i;var o=ay(i);return o&&n.contains(o)?o:(console.error("aria-hidden",i,"in not contained inside",n,". Doing nothing"),null)}).filter(function(i){return!!i})},eE=function(n,r,i,o){var d=WN(r,Array.isArray(n)?n:[n]);Qo[i]||(Qo[i]=new WeakMap);var f=Qo[i],m=[],h=new Set,v=new Set(d),g=function(S){!S||h.has(S)||(h.add(S),g(S.parentNode))};d.forEach(g);var y=function(S){!S||v.has(S)||Array.prototype.forEach.call(S.children,function(w){if(h.has(w))y(w);else try{var T=w.getAttribute(o),j=T!==null&&T!=="false",N=(kr.get(w)||0)+1,C=(f.get(w)||0)+1;kr.set(w,N),f.set(w,C),m.push(w),N===1&&j&&Vo.set(w,!0),C===1&&w.setAttribute(i,"true"),j||w.setAttribute(o,"true")}catch(M){console.error("aria-hidden: cannot operate on ",w,M)}})};return y(r),h.clear(),Xd++,function(){m.forEach(function(S){var w=kr.get(S)-1,T=f.get(S)-1;kr.set(S,w),f.set(S,T),w||(Vo.has(S)||S.removeAttribute(o),Vo.delete(S)),T||S.removeAttribute(i)}),Xd--,Xd||(kr=new WeakMap,kr=new WeakMap,Vo=new WeakMap,Qo={})}},tE=function(n,r,i){i===void 0&&(i="data-aria-hidden");var o=Array.from(Array.isArray(n)?n:[n]),d=IN(n);return d?(o.push.apply(o,Array.from(d.querySelectorAll("[aria-live], script"))),eE(o,d,i,"aria-hidden")):function(){return null}},pu="Dialog",[ny,uT]=Jr(pu),[aE,Ba]=ny(pu),ly=n=>{const{__scopeDialog:r,children:i,open:o,defaultOpen:d,onOpenChange:f,modal:m=!0}=n,h=x.useRef(null),v=x.useRef(null),[g,y]=vs({prop:o,defaultProp:d??!1,onChange:f,caller:pu});return u.jsx(aE,{scope:r,triggerRef:h,contentRef:v,contentId:Kl(),titleId:Kl(),descriptionId:Kl(),open:g,onOpenChange:y,onOpenToggle:x.useCallback(()=>y(S=>!S),[y]),modal:m,children:i})};ly.displayName=pu;var ry="DialogTrigger",iy=x.forwardRef((n,r)=>{const{__scopeDialog:i,...o}=n,d=Ba(ry,i),f=Kt(r,d.triggerRef);return u.jsx(Xe.button,{type:"button","aria-haspopup":"dialog","aria-expanded":d.open,"aria-controls":d.contentId,"data-state":kf(d.open),...o,ref:f,onClick:Ke(n.onClick,d.onOpenToggle)})});iy.displayName=ry;var zf="DialogPortal",[nE,sy]=ny(zf,{forceMount:void 0}),oy=n=>{const{__scopeDialog:r,forceMount:i,children:o,container:d}=n,f=Ba(zf,r);return u.jsx(nE,{scope:r,forceMount:i,children:x.Children.map(o,m=>u.jsx(Nn,{present:i||f.open,children:u.jsx(Xp,{asChild:!0,container:d,children:m})}))})};oy.displayName=zf;var ru="DialogOverlay",uy=x.forwardRef((n,r)=>{const i=sy(ru,n.__scopeDialog),{forceMount:o=i.forceMount,...d}=n,f=Ba(ru,n.__scopeDialog);return f.modal?u.jsx(Nn,{present:o||f.open,children:u.jsx(rE,{...d,ref:r})}):null});uy.displayName=ru;var lE=us("DialogOverlay.RemoveScroll"),rE=x.forwardRef((n,r)=>{const{__scopeDialog:i,...o}=n,d=Ba(ru,i);return u.jsx(ty,{as:lE,allowPinchZoom:!0,shards:[d.contentRef],children:u.jsx(Xe.div,{"data-state":kf(d.open),...o,ref:r,style:{pointerEvents:"auto",...o.style}})})}),Pl="DialogContent",cy=x.forwardRef((n,r)=>{const i=sy(Pl,n.__scopeDialog),{forceMount:o=i.forceMount,...d}=n,f=Ba(Pl,n.__scopeDialog);return u.jsx(Nn,{present:o||f.open,children:f.modal?u.jsx(iE,{...d,ref:r}):u.jsx(sE,{...d,ref:r})})});cy.displayName=Pl;var iE=x.forwardRef((n,r)=>{const i=Ba(Pl,n.__scopeDialog),o=x.useRef(null),d=Kt(r,i.contentRef,o);return x.useEffect(()=>{const f=o.current;if(f)return tE(f)},[]),u.jsx(dy,{...n,ref:d,trapFocus:i.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Ke(n.onCloseAutoFocus,f=>{var m;f.preventDefault(),(m=i.triggerRef.current)==null||m.focus()}),onPointerDownOutside:Ke(n.onPointerDownOutside,f=>{const m=f.detail.originalEvent,h=m.button===0&&m.ctrlKey===!0;(m.button===2||h)&&f.preventDefault()}),onFocusOutside:Ke(n.onFocusOutside,f=>f.preventDefault())})}),sE=x.forwardRef((n,r)=>{const i=Ba(Pl,n.__scopeDialog),o=x.useRef(!1),d=x.useRef(!1);return u.jsx(dy,{...n,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:f=>{var m,h;(m=n.onCloseAutoFocus)==null||m.call(n,f),f.defaultPrevented||(o.current||(h=i.triggerRef.current)==null||h.focus(),f.preventDefault()),o.current=!1,d.current=!1},onInteractOutside:f=>{var v,g;(v=n.onInteractOutside)==null||v.call(n,f),f.defaultPrevented||(o.current=!0,f.detail.originalEvent.type==="pointerdown"&&(d.current=!0));const m=f.target;((g=i.triggerRef.current)==null?void 0:g.contains(m))&&f.preventDefault(),f.detail.originalEvent.type==="focusin"&&d.current&&f.preventDefault()}})}),dy=x.forwardRef((n,r)=>{const{__scopeDialog:i,trapFocus:o,onOpenAutoFocus:d,onCloseAutoFocus:f,...m}=n,h=Ba(Pl,i),v=x.useRef(null),g=Kt(r,v);return vN(),u.jsxs(u.Fragment,{children:[u.jsx(Kp,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:d,onUnmountAutoFocus:f,children:u.jsx(Of,{role:"dialog",id:h.contentId,"aria-describedby":h.descriptionId,"aria-labelledby":h.titleId,"data-state":kf(h.open),...m,ref:g,onDismiss:()=>h.onOpenChange(!1)})}),u.jsxs(u.Fragment,{children:[u.jsx(uE,{titleId:h.titleId}),u.jsx(dE,{contentRef:v,descriptionId:h.descriptionId})]})]})}),_f="DialogTitle",fy=x.forwardRef((n,r)=>{const{__scopeDialog:i,...o}=n,d=Ba(_f,i);return u.jsx(Xe.h2,{id:d.titleId,...o,ref:r})});fy.displayName=_f;var hy="DialogDescription",oE=x.forwardRef((n,r)=>{const{__scopeDialog:i,...o}=n,d=Ba(hy,i);return u.jsx(Xe.p,{id:d.descriptionId,...o,ref:r})});oE.displayName=hy;var my="DialogClose",vy=x.forwardRef((n,r)=>{const{__scopeDialog:i,...o}=n,d=Ba(my,i);return u.jsx(Xe.button,{type:"button",...o,ref:r,onClick:Ke(n.onClick,()=>d.onOpenChange(!1))})});vy.displayName=my;function kf(n){return n?"open":"closed"}var gy="DialogTitleWarning",[cT,py]=YS(gy,{contentName:Pl,titleName:_f,docsSlug:"dialog"}),uE=({titleId:n})=>{const r=py(gy),i=`\`${r.contentName}\` requires a \`${r.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${r.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${r.docsSlug}`;return x.useEffect(()=>{n&&(document.getElementById(n)||console.error(i))},[i,n]),null},cE="DialogDescriptionWarning",dE=({contentRef:n,descriptionId:r})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${py(cE).contentName}}.`;return x.useEffect(()=>{var f;const d=(f=n.current)==null?void 0:f.getAttribute("aria-describedby");r&&d&&(document.getElementById(r)||console.warn(o))},[o,n,r]),null},fE=ly,hE=iy,mE=oy,vE=uy,gE=cy,pE=fy,yE=vy;/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xE=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),bE=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,i,o)=>o?o.toUpperCase():i.toLowerCase()),Mg=n=>{const r=bE(n);return r.charAt(0).toUpperCase()+r.slice(1)},yy=(...n)=>n.filter((r,i,o)=>!!r&&r.trim()!==""&&o.indexOf(r)===i).join(" ").trim(),wE=n=>{for(const r in n)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var SE={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NE=x.forwardRef(({color:n="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:d="",children:f,iconNode:m,...h},v)=>x.createElement("svg",{ref:v,...SE,width:r,height:r,stroke:n,strokeWidth:o?Number(i)*24/Number(r):i,className:yy("lucide",d),...!f&&!wE(h)&&{"aria-hidden":"true"},...h},[...m.map(([g,y])=>x.createElement(g,y)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fe=(n,r)=>{const i=x.forwardRef(({className:o,...d},f)=>x.createElement(NE,{ref:f,iconNode:r,className:yy(`lucide-${xE(Mg(n))}`,`lucide-${n}`,o),...d}));return i.displayName=Mg(n),i};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const EE=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],xy=Fe("arrow-right",EE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jE=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]],TE=Fe("award",jE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RE=[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}]],ME=Fe("badge",RE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AE=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],by=Fe("book-open",AE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CE=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]],DE=Fe("brain",CE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OE=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],zE=Fe("calendar",OE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _E=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],kE=Fe("chevron-down",_E);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const LE=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],nl=Fe("circle-check-big",LE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const UE=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],HE=Fe("clock",UE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BE=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],wy=Fe("eye",BE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qE=[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]],Ag=Fe("flame",qE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GE=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],YE=Fe("globe",GE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const VE=[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]],QE=Fe("headphones",VE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KE=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],PE=Fe("lock",KE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const XE=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],FE=Fe("map-pin",XE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ZE=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],$E=Fe("menu",ZE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JE=[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]],IE=Fe("mic",JE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WE=[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]],ej=Fe("pen-tool",WE);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tj=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]],Cg=Fe("pencil",tj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aj=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],Io=Fe("play",aj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nj=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],lj=Fe("shield",nj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rj=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],Ur=Fe("sparkles",rj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ij=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],ul=Fe("star",ij);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sj=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],Sy=Fe("target",sj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oj=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],gs=Fe("trending-up",oj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uj=[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]],Ny=Fe("trophy",uj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cj=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],Ey=Fe("user-plus",cj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dj=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],iu=Fe("users",dj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fj=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],hj=Fe("x",fj);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mj=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],vj=Fe("zap",mj);function gj({...n}){return u.jsx(fE,{"data-slot":"sheet",...n})}function pj({...n}){return u.jsx(hE,{"data-slot":"sheet-trigger",...n})}function yj({...n}){return u.jsx(mE,{"data-slot":"sheet-portal",...n})}function xj({className:n,...r}){return u.jsx(vE,{"data-slot":"sheet-overlay",className:nt("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",n),...r})}function bj({className:n,children:r,side:i="right",...o}){return u.jsxs(yj,{children:[u.jsx(xj,{}),u.jsxs(gE,{"data-slot":"sheet-content",className:nt("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",i==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",i==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",i==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",i==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",n),...o,children:[r,u.jsxs(yE,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[u.jsx(hj,{className:"size-4"}),u.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function wj({className:n,...r}){return u.jsx("div",{"data-slot":"sheet-header",className:nt("flex flex-col gap-1.5 p-4",n),...r})}function Sj({className:n,...r}){return u.jsx(pE,{"data-slot":"sheet-title",className:nt("text-foreground font-semibold",n),...r})}var Nj=x.createContext(void 0);function Lf(n){const r=x.useContext(Nj);return n||r||"ltr"}function Uf(n){const r=n+"CollectionProvider",[i,o]=Jr(r),[d,f]=i(r,{collectionRef:{current:null},itemMap:new Map}),m=N=>{const{scope:C,children:M}=N,P=wn.useRef(null),_=wn.useRef(new Map).current;return u.jsx(d,{scope:C,itemMap:_,collectionRef:P,children:M})};m.displayName=r;const h=n+"CollectionSlot",v=us(h),g=wn.forwardRef((N,C)=>{const{scope:M,children:P}=N,_=f(h,M),V=Kt(C,_.collectionRef);return u.jsx(v,{ref:V,children:P})});g.displayName=h;const y=n+"CollectionItemSlot",S="data-radix-collection-item",w=us(y),T=wn.forwardRef((N,C)=>{const{scope:M,children:P,..._}=N,V=wn.useRef(null),q=Kt(C,V),O=f(y,M);return wn.useEffect(()=>(O.itemMap.set(V,{ref:V,..._}),()=>void O.itemMap.delete(V))),u.jsx(w,{[S]:"",ref:q,children:P})});T.displayName=y;function j(N){const C=f(n+"CollectionConsumer",N);return wn.useCallback(()=>{const P=C.collectionRef.current;if(!P)return[];const _=Array.from(P.querySelectorAll(`[${S}]`));return Array.from(C.itemMap.values()).sort((O,te)=>_.indexOf(O.ref.current)-_.indexOf(te.ref.current))},[C.collectionRef,C.itemMap])}return[{Provider:m,Slot:g,ItemSlot:T},j,o]}function Ej(n){const r=x.useRef({value:n,previous:n});return x.useMemo(()=>(r.current.value!==n&&(r.current.previous=r.current.value,r.current.value=n),r.current.previous),[n])}var jj=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),Tj="VisuallyHidden",jy=x.forwardRef((n,r)=>u.jsx(Xe.span,{...n,ref:r,style:{...jj,...n.style}}));jy.displayName=Tj;var Rj=jy,Zl="NavigationMenu",[Hf,Ty,Mj]=Uf(Zl),[cf,Aj,Cj]=Uf(Zl),[Bf,dT]=Jr(Zl,[Mj,Cj]),[Dj,Ta]=Bf(Zl),[Oj,zj]=Bf(Zl),Ry=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,value:o,onValueChange:d,defaultValue:f,delayDuration:m=200,skipDelayDuration:h=300,orientation:v="horizontal",dir:g,...y}=n,[S,w]=x.useState(null),T=Kt(r,W=>w(W)),j=Lf(g),N=x.useRef(0),C=x.useRef(0),M=x.useRef(0),[P,_]=x.useState(!0),[V,q]=vs({prop:o,onChange:W=>{const ge=W!=="",Le=h>0;ge?(window.clearTimeout(M.current),Le&&_(!1)):(window.clearTimeout(M.current),M.current=window.setTimeout(()=>_(!0),h)),d==null||d(W)},defaultProp:f??"",caller:Zl}),O=x.useCallback(()=>{window.clearTimeout(C.current),C.current=window.setTimeout(()=>q(""),150)},[q]),te=x.useCallback(W=>{window.clearTimeout(C.current),q(W)},[q]),K=x.useCallback(W=>{V===W?window.clearTimeout(C.current):N.current=window.setTimeout(()=>{window.clearTimeout(C.current),q(W)},m)},[V,q,m]);return x.useEffect(()=>()=>{window.clearTimeout(N.current),window.clearTimeout(C.current),window.clearTimeout(M.current)},[]),u.jsx(My,{scope:i,isRootMenu:!0,value:V,dir:j,orientation:v,rootNavigationMenu:S,onTriggerEnter:W=>{window.clearTimeout(N.current),P?K(W):te(W)},onTriggerLeave:()=>{window.clearTimeout(N.current),O()},onContentEnter:()=>window.clearTimeout(C.current),onContentLeave:O,onItemSelect:W=>{q(ge=>ge===W?"":W)},onItemDismiss:()=>q(""),children:u.jsx(Xe.nav,{"aria-label":"Main","data-orientation":v,dir:j,...y,ref:T})})});Ry.displayName=Zl;var df="NavigationMenuSub",_j=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,value:o,onValueChange:d,defaultValue:f,orientation:m="horizontal",...h}=n,v=Ta(df,i),[g,y]=vs({prop:o,onChange:d,defaultProp:f??"",caller:df});return u.jsx(My,{scope:i,isRootMenu:!1,value:g,dir:v.dir,orientation:m,rootNavigationMenu:v.rootNavigationMenu,onTriggerEnter:S=>y(S),onItemSelect:S=>y(S),onItemDismiss:()=>y(""),children:u.jsx(Xe.div,{"data-orientation":m,...h,ref:r})})});_j.displayName=df;var My=n=>{const{scope:r,isRootMenu:i,rootNavigationMenu:o,dir:d,orientation:f,children:m,value:h,onItemSelect:v,onItemDismiss:g,onTriggerEnter:y,onTriggerLeave:S,onContentEnter:w,onContentLeave:T}=n,[j,N]=x.useState(null),[C,M]=x.useState(new Map),[P,_]=x.useState(null);return u.jsx(Dj,{scope:r,isRootMenu:i,rootNavigationMenu:o,value:h,previousValue:Ej(h),baseId:Kl(),dir:d,orientation:f,viewport:j,onViewportChange:N,indicatorTrack:P,onIndicatorTrackChange:_,onTriggerEnter:Wt(y),onTriggerLeave:Wt(S),onContentEnter:Wt(w),onContentLeave:Wt(T),onItemSelect:Wt(v),onItemDismiss:Wt(g),onViewportContentChange:x.useCallback((V,q)=>{M(O=>(O.set(V,q),new Map(O)))},[]),onViewportContentRemove:x.useCallback(V=>{M(q=>q.has(V)?(q.delete(V),new Map(q)):q)},[]),children:u.jsx(Hf.Provider,{scope:r,children:u.jsx(Oj,{scope:r,items:C,children:m})})})},Ay="NavigationMenuList",Cy=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,...o}=n,d=Ta(Ay,i),f=u.jsx(Xe.ul,{"data-orientation":d.orientation,...o,ref:r});return u.jsx(Xe.div,{style:{position:"relative"},ref:d.onIndicatorTrackChange,children:u.jsx(Hf.Slot,{scope:i,children:d.isRootMenu?u.jsx(By,{asChild:!0,children:f}):f})})});Cy.displayName=Ay;var Dy="NavigationMenuItem",[kj,Oy]=Bf(Dy),zy=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,value:o,...d}=n,f=Kl(),m=o||f||"LEGACY_REACT_AUTO_VALUE",h=x.useRef(null),v=x.useRef(null),g=x.useRef(null),y=x.useRef(()=>{}),S=x.useRef(!1),w=x.useCallback((j="start")=>{if(h.current){y.current();const N=hf(h.current);N.length&&Yf(j==="start"?N:N.reverse())}},[]),T=x.useCallback(()=>{if(h.current){const j=hf(h.current);j.length&&(y.current=Vj(j))}},[]);return u.jsx(kj,{scope:i,value:m,triggerRef:v,contentRef:h,focusProxyRef:g,wasEscapeCloseRef:S,onEntryKeyDown:w,onFocusProxyEnter:w,onRootContentClose:T,onContentFocusOutside:T,children:u.jsx(Xe.li,{...d,ref:r})})});zy.displayName=Dy;var ff="NavigationMenuTrigger",_y=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,disabled:o,...d}=n,f=Ta(ff,n.__scopeNavigationMenu),m=Oy(ff,n.__scopeNavigationMenu),h=x.useRef(null),v=Kt(h,m.triggerRef,r),g=Gy(f.baseId,m.value),y=Yy(f.baseId,m.value),S=x.useRef(!1),w=x.useRef(!1),T=m.value===f.value;return u.jsxs(u.Fragment,{children:[u.jsx(Hf.ItemSlot,{scope:i,value:m.value,children:u.jsx(qy,{asChild:!0,children:u.jsx(Xe.button,{id:g,disabled:o,"data-disabled":o?"":void 0,"data-state":Vf(T),"aria-expanded":T,"aria-controls":y,...d,ref:v,onPointerEnter:Ke(n.onPointerEnter,()=>{w.current=!1,m.wasEscapeCloseRef.current=!1}),onPointerMove:Ke(n.onPointerMove,su(()=>{o||w.current||m.wasEscapeCloseRef.current||S.current||(f.onTriggerEnter(m.value),S.current=!0)})),onPointerLeave:Ke(n.onPointerLeave,su(()=>{o||(f.onTriggerLeave(),S.current=!1)})),onClick:Ke(n.onClick,()=>{f.onItemSelect(m.value),w.current=T}),onKeyDown:Ke(n.onKeyDown,j=>{const C={horizontal:"ArrowDown",vertical:f.dir==="rtl"?"ArrowLeft":"ArrowRight"}[f.orientation];T&&j.key===C&&(m.onEntryKeyDown(),j.preventDefault())})})})}),T&&u.jsxs(u.Fragment,{children:[u.jsx(Rj,{"aria-hidden":!0,tabIndex:0,ref:m.focusProxyRef,onFocus:j=>{const N=m.contentRef.current,C=j.relatedTarget,M=C===h.current,P=N==null?void 0:N.contains(C);(M||!P)&&m.onFocusProxyEnter(M?"start":"end")}}),f.viewport&&u.jsx("span",{"aria-owns":y})]})]})});_y.displayName=ff;var Lj="NavigationMenuLink",Dg="navigationMenu.linkSelect",ky=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,active:o,onSelect:d,...f}=n;return u.jsx(qy,{asChild:!0,children:u.jsx(Xe.a,{"data-active":o?"":void 0,"aria-current":o?"page":void 0,...f,ref:r,onClick:Ke(n.onClick,m=>{const h=m.target,v=new CustomEvent(Dg,{bubbles:!0,cancelable:!0});if(h.addEventListener(Dg,g=>d==null?void 0:d(g),{once:!0}),sf(h,v),!v.defaultPrevented&&!m.metaKey){const g=new CustomEvent(Wo,{bubbles:!0,cancelable:!0});sf(h,g)}},{checkForDefaultPrevented:!1})})})});ky.displayName=Lj;var qf="NavigationMenuIndicator",Uj=x.forwardRef((n,r)=>{const{forceMount:i,...o}=n,d=Ta(qf,n.__scopeNavigationMenu),f=!!d.value;return d.indicatorTrack?Yp.createPortal(u.jsx(Nn,{present:i||f,children:u.jsx(Hj,{...o,ref:r})}),d.indicatorTrack):null});Uj.displayName=qf;var Hj=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,...o}=n,d=Ta(qf,i),f=Ty(i),[m,h]=x.useState(null),[v,g]=x.useState(null),y=d.orientation==="horizontal",S=!!d.value;x.useEffect(()=>{var N;const j=(N=f().find(C=>C.value===d.value))==null?void 0:N.ref.current;j&&h(j)},[f,d.value]);const w=()=>{m&&g({size:y?m.offsetWidth:m.offsetHeight,offset:y?m.offsetLeft:m.offsetTop})};return mf(m,w),mf(d.indicatorTrack,w),v?u.jsx(Xe.div,{"aria-hidden":!0,"data-state":S?"visible":"hidden","data-orientation":d.orientation,...o,ref:r,style:{position:"absolute",...y?{left:0,width:v.size+"px",transform:`translateX(${v.offset}px)`}:{top:0,height:v.size+"px",transform:`translateY(${v.offset}px)`},...o.style}}):null}),Fr="NavigationMenuContent",Ly=x.forwardRef((n,r)=>{const{forceMount:i,...o}=n,d=Ta(Fr,n.__scopeNavigationMenu),f=Oy(Fr,n.__scopeNavigationMenu),m=Kt(f.contentRef,r),h=f.value===d.value,v={value:f.value,triggerRef:f.triggerRef,focusProxyRef:f.focusProxyRef,wasEscapeCloseRef:f.wasEscapeCloseRef,onContentFocusOutside:f.onContentFocusOutside,onRootContentClose:f.onRootContentClose,...o};return d.viewport?u.jsx(Bj,{forceMount:i,...v,ref:m}):u.jsx(Nn,{present:i||h,children:u.jsx(Uy,{"data-state":Vf(h),...v,ref:m,onPointerEnter:Ke(n.onPointerEnter,d.onContentEnter),onPointerLeave:Ke(n.onPointerLeave,su(d.onContentLeave)),style:{pointerEvents:!h&&d.isRootMenu?"none":void 0,...v.style}})})});Ly.displayName=Fr;var Bj=x.forwardRef((n,r)=>{const i=Ta(Fr,n.__scopeNavigationMenu),{onViewportContentChange:o,onViewportContentRemove:d}=i;return Ha(()=>{o(n.value,{ref:r,...n})},[n,r,o]),Ha(()=>()=>d(n.value),[n.value,d]),null}),Wo="navigationMenu.rootContentDismiss",Uy=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,value:o,triggerRef:d,focusProxyRef:f,wasEscapeCloseRef:m,onRootContentClose:h,onContentFocusOutside:v,...g}=n,y=Ta(Fr,i),S=x.useRef(null),w=Kt(S,r),T=Gy(y.baseId,o),j=Yy(y.baseId,o),N=Ty(i),C=x.useRef(null),{onItemDismiss:M}=y;x.useEffect(()=>{const _=S.current;if(y.isRootMenu&&_){const V=()=>{var q;M(),h(),_.contains(document.activeElement)&&((q=d.current)==null||q.focus())};return _.addEventListener(Wo,V),()=>_.removeEventListener(Wo,V)}},[y.isRootMenu,n.value,d,M,h]);const P=x.useMemo(()=>{const V=N().map(ge=>ge.value);y.dir==="rtl"&&V.reverse();const q=V.indexOf(y.value),O=V.indexOf(y.previousValue),te=o===y.value,K=O===V.indexOf(o);if(!te&&!K)return C.current;const W=(()=>{if(q!==O){if(te&&O!==-1)return q>O?"from-end":"from-start";if(K&&q!==-1)return q>O?"to-start":"to-end"}return null})();return C.current=W,W},[y.previousValue,y.value,y.dir,N,o]);return u.jsx(By,{asChild:!0,children:u.jsx(Of,{id:j,"aria-labelledby":T,"data-motion":P,"data-orientation":y.orientation,...g,ref:w,disableOutsidePointerEvents:!1,onDismiss:()=>{var V;const _=new Event(Wo,{bubbles:!0,cancelable:!0});(V=S.current)==null||V.dispatchEvent(_)},onFocusOutside:Ke(n.onFocusOutside,_=>{var q;v();const V=_.target;(q=y.rootNavigationMenu)!=null&&q.contains(V)&&_.preventDefault()}),onPointerDownOutside:Ke(n.onPointerDownOutside,_=>{var te;const V=_.target,q=N().some(K=>{var W;return(W=K.ref.current)==null?void 0:W.contains(V)}),O=y.isRootMenu&&((te=y.viewport)==null?void 0:te.contains(V));(q||O||!y.isRootMenu)&&_.preventDefault()}),onKeyDown:Ke(n.onKeyDown,_=>{var O;const V=_.altKey||_.ctrlKey||_.metaKey;if(_.key==="Tab"&&!V){const te=hf(_.currentTarget),K=document.activeElement,W=te.findIndex(qe=>qe===K),Le=_.shiftKey?te.slice(0,W).reverse():te.slice(W+1,te.length);Yf(Le)?_.preventDefault():(O=f.current)==null||O.focus()}}),onEscapeKeyDown:Ke(n.onEscapeKeyDown,_=>{m.current=!0})})})}),Gf="NavigationMenuViewport",Hy=x.forwardRef((n,r)=>{const{forceMount:i,...o}=n,f=!!Ta(Gf,n.__scopeNavigationMenu).value;return u.jsx(Nn,{present:i||f,children:u.jsx(qj,{...o,ref:r})})});Hy.displayName=Gf;var qj=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,children:o,...d}=n,f=Ta(Gf,i),m=Kt(r,f.onViewportChange),h=zj(Fr,n.__scopeNavigationMenu),[v,g]=x.useState(null),[y,S]=x.useState(null),w=v?(v==null?void 0:v.width)+"px":void 0,T=v?(v==null?void 0:v.height)+"px":void 0,j=!!f.value,N=j?f.value:f.previousValue;return mf(y,()=>{y&&g({width:y.offsetWidth,height:y.offsetHeight})}),u.jsx(Xe.div,{"data-state":Vf(j),"data-orientation":f.orientation,...d,ref:m,style:{pointerEvents:!j&&f.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":w,"--radix-navigation-menu-viewport-height":T,...d.style},onPointerEnter:Ke(n.onPointerEnter,f.onContentEnter),onPointerLeave:Ke(n.onPointerLeave,su(f.onContentLeave)),children:Array.from(h.items).map(([M,{ref:P,forceMount:_,...V}])=>{const q=N===M;return u.jsx(Nn,{present:_||q,children:u.jsx(Uy,{...V,ref:Af(P,O=>{q&&O&&S(O)})})},M)})})}),Gj="FocusGroup",By=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,...o}=n,d=Ta(Gj,i);return u.jsx(cf.Provider,{scope:i,children:u.jsx(cf.Slot,{scope:i,children:u.jsx(Xe.div,{dir:d.dir,...o,ref:r})})})}),Og=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],Yj="FocusGroupItem",qy=x.forwardRef((n,r)=>{const{__scopeNavigationMenu:i,...o}=n,d=Aj(i),f=Ta(Yj,i);return u.jsx(cf.ItemSlot,{scope:i,children:u.jsx(Xe.button,{...o,ref:r,onKeyDown:Ke(n.onKeyDown,m=>{if(["Home","End",...Og].includes(m.key)){let v=d().map(S=>S.ref.current);if([f.dir==="rtl"?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(m.key)&&v.reverse(),Og.includes(m.key)){const S=v.indexOf(m.currentTarget);v=v.slice(S+1)}setTimeout(()=>Yf(v)),m.preventDefault()}})})})});function hf(n){const r=[],i=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const d=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||d?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;i.nextNode();)r.push(i.currentNode);return r}function Yf(n){const r=document.activeElement;return n.some(i=>i===r?!0:(i.focus(),document.activeElement!==r))}function Vj(n){return n.forEach(r=>{r.dataset.tabindex=r.getAttribute("tabindex")||"",r.setAttribute("tabindex","-1")}),()=>{n.forEach(r=>{const i=r.dataset.tabindex;r.setAttribute("tabindex",i)})}}function mf(n,r){const i=Wt(r);Ha(()=>{let o=0;if(n){const d=new ResizeObserver(()=>{cancelAnimationFrame(o),o=window.requestAnimationFrame(i)});return d.observe(n),()=>{window.cancelAnimationFrame(o),d.unobserve(n)}}},[n,i])}function Vf(n){return n?"open":"closed"}function Gy(n,r){return`${n}-trigger-${r}`}function Yy(n,r){return`${n}-content-${r}`}function su(n){return r=>r.pointerType==="mouse"?n(r):void 0}var Qj=Ry,Kj=Cy,Pj=zy,Xj=_y,Fj=ky,Zj=Ly,$j=Hy;function Jj({className:n,children:r,viewport:i=!0,...o}){return u.jsxs(Qj,{"data-slot":"navigation-menu","data-viewport":i,className:nt("group/navigation-menu relative flex max-w-max flex-1 items-center justify-center",n),...o,children:[r,i&&u.jsx(a4,{})]})}function Ij({className:n,...r}){return u.jsx(Kj,{"data-slot":"navigation-menu-list",className:nt("group flex flex-1 list-none items-center justify-center gap-1",n),...r})}function zg({className:n,...r}){return u.jsx(Pj,{"data-slot":"navigation-menu-item",className:nt("relative",n),...r})}const Wj=Cf("group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1");function e4({className:n,children:r,...i}){return u.jsxs(Xj,{"data-slot":"navigation-menu-trigger",className:nt(Wj(),"group",n),...i,children:[r," ",u.jsx(kE,{className:"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})}function t4({className:n,...r}){return u.jsx(Zj,{"data-slot":"navigation-menu-content",className:nt("data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto","group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none",n),...r})}function a4({className:n,...r}){return u.jsx("div",{className:nt("absolute top-full left-0 isolate z-50 flex justify-center"),children:u.jsx($j,{"data-slot":"navigation-menu-viewport",className:nt("origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]",n),...r})})}function n4({className:n,...r}){return u.jsx(Fj,{"data-slot":"navigation-menu-link",className:nt("data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4",n),...r})}const _g=[{title:"A1 Seviye",href:"/grammar/a1",description:"Temel Türkçe dilbilgisi ve günlük konuşma kalıpları."},{title:"A2 Seviye",href:"/grammar/a2",description:"Basit cümle yapıları ve temel iletişim becerileri."},{title:"B1 Seviye",href:"/grammar/b1",description:"Orta düzey dilbilgisi ve akıcı konuşma teknikleri."},{title:"B2 Seviye",href:"/grammar/b2",description:"İleri düzey dilbilgisi ve akademik yazma becerileri."},{title:"C1 Seviye",href:"/grammar/c1",description:"Profesyonel düzeyde Türkçe kullanımı ve edebi metinler."}],l4=()=>u.jsx("nav",{className:"bg-white/95 backdrop-blur-sm border-b border-gray-100 sticky top-0 z-50 shadow-sm w-full",children:u.jsx("div",{className:"max-w-7xl mx-auto px-2 sm:px-4 lg:px-8",children:u.jsxs("div",{className:"flex justify-between items-center h-16 sm:h-20",children:[u.jsx("div",{className:"flex items-center",children:u.jsx("img",{src:"/turk-test.png",alt:"logo",className:"h-14 w-auto"})}),u.jsx("div",{className:"hidden lg:block",children:u.jsxs("div",{className:"ml-4 sm:ml-6 lg:ml-10 flex items-baseline space-x-2 sm:space-x-4 lg:space-x-6",children:[u.jsx(Jj,{children:u.jsxs(Ij,{children:[u.jsxs(zg,{children:[u.jsx(e4,{className:"text-gray-600 hover:text-red-600",children:"Gramer"}),u.jsx(t4,{children:u.jsx("ul",{className:"grid grid-cols-1 sm:grid-cols-2 w-[300px] sm:w-[500px] gap-2 sm:gap-3 p-3 sm:p-4 bg-white",children:_g.map(n=>u.jsx("li",{children:u.jsx(n4,{asChild:!0,children:u.jsxs(rt,{to:n.href,className:"block select-none rounded-md p-2 sm:p-3 leading-none no-underline outline-none transition-colors hover:bg-red-50 focus:bg-red-50",children:[u.jsx("div",{className:"text-xs sm:text-sm font-medium leading-none text-gray-900 mb-1 sm:mb-2",children:n.title}),u.jsx("p",{className:"text-xs sm:text-sm leading-snug text-gray-600 line-clamp-2",children:n.description})]})})},n.title))})})]}),u.jsx(zg,{children:u.jsx(rt,{to:"/price",className:"text-gray-600 hover:text-red-600 px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium transition-colors duration-200 cursor-pointer",children:"Fiyatlar"})})]})}),u.jsx(rt,{to:"/test",className:"text-gray-600 hover:text-red-600 px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium transition-colors duration-200 cursor-pointer",children:"Test"}),u.jsx(rt,{to:"/lugat",className:"text-gray-600 hover:text-red-600 px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium transition-colors duration-200 cursor-pointer",children:"Lügat"}),u.jsx(rt,{to:"/results",className:"text-gray-600 hover:text-red-600 px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium transition-colors duration-200 cursor-pointer",children:"Sonuçlar"}),u.jsx(rt,{to:"/contact",className:"text-gray-600 hover:text-red-600 px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium transition-colors duration-200 cursor-pointer",children:"İletişim"})]})}),u.jsxs("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[u.jsxs("div",{className:"text-xs sm:text-sm hidden sm:block",children:[u.jsx("span",{className:"text-gray-600",children:"Bakiye: "}),u.jsx("span",{className:"font-semibold text-yellow-600",children:"15U"})]}),u.jsxs("div",{className:"hidden sm:flex space-x-2",children:[u.jsx("a",{href:"https://t.me/new_uzb_dev",target:"_blank",rel:"noopener noreferrer",children:u.jsx("img",{src:"https://cdn-icons-png.flaticon.com/512/2111/2111646.png",alt:"Telegram",className:"w-6 h-6 sm:w-8 sm:h-8 cursor-pointer"})}),u.jsx("img",{src:"https://cdn-icons-png.flaticon.com/512/3955/3955024.png",alt:"Instagram",className:"w-6 h-6 sm:w-8 sm:h-8 cursor-pointer"})]}),u.jsx("div",{className:"lg:hidden",children:u.jsxs(gj,{children:[u.jsx(pj,{asChild:!0,children:u.jsx(Et,{variant:"ghost",size:"icon",className:"cursor-pointer",children:u.jsx($E,{className:"h-5 w-5 sm:h-6 sm:w-6"})})}),u.jsxs(bj,{className:"bg-white w-[80vw] sm:w-[60vw]",children:[u.jsx(wj,{children:u.jsx(Sj,{children:"Menu"})}),u.jsxs("div",{className:"flex flex-col space-y-3 mt-6",children:[u.jsx("div",{className:"space-y-3",children:_g.map(n=>u.jsx(rt,{to:n.href,className:"text-gray-600 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200 cursor-pointer block",children:n.title},n.title))}),u.jsx(rt,{to:"/test",className:"text-gray-600 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200 cursor-pointer",children:"Test"}),u.jsx(rt,{to:"/lugat",className:"text-gray-600 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200 cursor-pointer",children:"Lügat"}),u.jsx(rt,{to:"/results",className:"text-gray-600 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200 cursor-pointer",children:"Sonuçlar"}),u.jsx(rt,{to:"/contact",className:"text-gray-600 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200 cursor-pointer",children:"İletişim"}),u.jsx(rt,{to:"/price",className:"text-gray-600 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors duration-200 cursor-pointer",children:"Fiyatlar"}),u.jsxs("div",{className:"flex space-x-4 px-3 py-2",children:[u.jsx("a",{href:"https://t.me/new_uzb_dev",target:"_blank",rel:"noopener noreferrer",children:u.jsx("img",{src:"https://cdn-icons-png.flaticon.com/512/2111/2111646.png",alt:"Telegram",className:"w-6 h-6 cursor-pointer"})}),u.jsx("img",{src:"https://cdn-icons-png.flaticon.com/512/3955/3955024.png",alt:"Instagram",className:"w-6 h-6 cursor-pointer"})]})]})]})]})})]})]})})}),r4=()=>u.jsxs("div",{className:" bg-white",children:[u.jsx(l4,{}),u.jsxs("main",{children:[u.jsx(xw,{}),u.jsx(tS,{})]})]}),i4=()=>u.jsx("div",{children:u.jsx("section",{className:"py-20 bg-white border-t border-gray-100",children:u.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:u.jsxs("div",{className:"grid md:grid-cols-4 gap-8 text-center",children:[u.jsxs("div",{className:"group",children:[u.jsx("div",{className:"text-4xl font-bold bg-gradient-to-r from-red-600 to-red-500 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-200",children:"15,000+"}),u.jsx("div",{className:"text-gray-600",children:"Aktif Kullanıcı"})]}),u.jsxs("div",{className:"group",children:[u.jsx("div",{className:"text-4xl font-bold bg-gradient-to-r from-red-600 to-red-500 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-200",children:"50,000+"}),u.jsx("div",{className:"text-gray-600",children:"Tamamlanan Test"})]}),u.jsxs("div",{className:"group",children:[u.jsx("div",{className:"text-4xl font-bold bg-gradient-to-r from-red-600 to-red-500 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-200",children:"98%"}),u.jsx("div",{className:"text-gray-600",children:"Memnuniyet Oranı"})]}),u.jsxs("div",{className:"group",children:[u.jsx("div",{className:"text-4xl font-bold bg-gradient-to-r from-red-600 to-red-500 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-200",children:"24/7"}),u.jsx("div",{className:"text-gray-600",children:"Destek Hizmeti"})]})]})})})}),s4=()=>u.jsx("div",{children:u.jsx("section",{id:"features",className:"py-24 bg-white",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs("div",{className:"text-center mb-20",children:[u.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Neden TürkTest?"}),u.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Profesyonel Türkçe dil yeterlilik testinizi güvenilir ve kapsamlı platformumuzda gerçekleştirin"})]}),u.jsxs("div",{className:"grid lg:grid-cols-3 gap-12",children:[u.jsxs("div",{className:"text-center group",children:[u.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl shadow-lg flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200",children:u.jsx(nl,{className:"h-8 w-8 text-white"})}),u.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Anında Sonuç"}),u.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Test tamamlandıktan hemen sonra detaylı performans raporunuzu görüntüleyin. Güçlü ve geliştirilmesi gereken alanlarınızı keşfedin."})]}),u.jsxs("div",{className:"text-center group",children:[u.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl shadow-lg flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200",children:u.jsx(TE,{className:"h-8 w-8 text-white"})}),u.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Sertifikalı Sonuçlar"}),u.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Uluslararası standartlara uygun test sonuçlarınızı resmi sertifika ile belgelendirin. CV'nizde ve başvurularınızda kullanın."})]}),u.jsxs("div",{className:"text-center group",children:[u.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl shadow-lg flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-200",children:u.jsx(gs,{className:"h-8 w-8 text-white"})}),u.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"İlerleme Takibi"}),u.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Zaman içindeki gelişiminizi takip edin. Detaylı analitikler ile hangi alanlarda ilerleme kaydettiğinizi görün."})]})]})]})})}),o4=Cf("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function Sa({className:n,variant:r,asChild:i=!1,...o}){const d=i?Mp:"span";return u.jsx(d,{"data-slot":"badge",className:nt(o4({variant:r}),n),...o})}const u4=()=>u.jsx("div",{children:u.jsx("section",{className:"py-12 bg-gradient-to-b from-white to-gray-50",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-2",children:[u.jsxs("div",{className:"text-center mb-8",children:[u.jsxs(Sa,{className:"mb-4 bg-gradient-to-r from-red-500 to-red-600 text-white border-none text-lg",children:[u.jsx(gs,{className:"h-5 w-5 mr-2 animate-pulse"}),"Son 30 Gün Sonuçları"]}),u.jsx("h2",{className:"text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 mb-4",children:"En Yüksek Puanlar"})]}),u.jsxs("div",{className:"space-y-3 max-w-7xl mx-auto",children:[u.jsx("div",{className:"bg-white/70 backdrop-blur-sm p-5 rounded-2xl border border-gray-100/50",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx("img",{src:"https://randomuser.me/api/portraits/men/1.jpg",alt:"Mustafa Yıldırım",className:"w-10 h-10 rounded-full object-cover"}),u.jsxs("div",{children:[u.jsx("p",{className:"text-gray-900 font-semibold text-lg",children:"Mustafa Yıldırım"}),u.jsx("p",{className:"text-sm text-gray-500",children:"20.06.2025 10:52"})]})]}),u.jsxs("div",{className:"flex flex-col items-end",children:[u.jsx("div",{className:"text-red-500 font-bold text-2xl bg-red-50 px-4 py-1.5 rounded-xl",children:"C2"}),u.jsx("div",{className:"mt-2 text-sm font-medium text-gray-600 bg-gray-50 px-3 py-1 rounded-lg",children:"D:9 • O:8.5 • Y:7.5 • K:8"})]})]})}),u.jsx("div",{className:"bg-white/70 backdrop-blur-sm p-5 rounded-2xl border border-gray-100/50",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx("img",{src:"https://randomuser.me/api/portraits/men/2.jpg",alt:"Ali Kaya",className:"w-10 h-10 rounded-full object-cover"}),u.jsxs("div",{children:[u.jsx("p",{className:"text-gray-900 font-semibold text-lg",children:"Ali Kaya"}),u.jsx("p",{className:"text-sm text-gray-500",children:"28.05.2025 23:16"})]})]}),u.jsxs("div",{className:"flex flex-col items-end",children:[u.jsx("div",{className:"text-red-500 font-bold text-2xl bg-red-50 px-4 py-1.5 rounded-xl",children:"C1"}),u.jsx("div",{className:"mt-2 text-sm font-medium text-gray-600 bg-gray-50 px-3 py-1 rounded-lg",children:"D:9 • O:9 • Y:8.5 • K:8"})]})]})}),u.jsx("div",{className:"bg-white/70 backdrop-blur-sm p-5 rounded-2xl border border-gray-100/50",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx("img",{src:"https://randomuser.me/api/portraits/women/1.jpg",alt:"Ayşe Demir",className:"w-10 h-10 rounded-full object-cover"}),u.jsxs("div",{children:[u.jsx("p",{className:"text-gray-900 font-semibold text-lg",children:"Ayşe Demir"}),u.jsx("p",{className:"text-sm text-gray-500",children:"23.06.2025 15:02"})]})]}),u.jsxs("div",{className:"flex flex-col items-end",children:[u.jsx("div",{className:"text-red-500 font-bold text-2xl bg-red-50 px-4 py-1.5 rounded-xl",children:"B2"}),u.jsx("div",{className:"mt-2 text-sm font-medium text-gray-600 bg-gray-50 px-3 py-1 rounded-lg",children:"D:9 • O:9 • Y:8.5 • K:8"})]})]})}),u.jsx("div",{className:"bg-white/70 backdrop-blur-sm p-5 rounded-2xl border border-gray-100/50",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx("img",{src:"https://randomuser.me/api/portraits/women/2.jpg",alt:"Zeynep Şahin",className:"w-10 h-10 rounded-full object-cover"}),u.jsxs("div",{children:[u.jsx("p",{className:"text-gray-900 font-semibold text-lg",children:"Zeynep Şahin"}),u.jsx("p",{className:"text-sm text-gray-500",children:"02.06.2025 17:27"})]})]}),u.jsxs("div",{className:"flex flex-col items-end",children:[u.jsx("div",{className:"text-red-500 font-bold text-2xl bg-red-50 px-4 py-1.5 rounded-xl",children:"B1"}),u.jsx("div",{className:"mt-2 text-sm font-medium text-gray-600 bg-gray-50 px-3 py-1 rounded-lg",children:"D:9 • O:8.5 • Y:8 • K:8"})]})]})}),u.jsx("div",{className:"bg-white/70 backdrop-blur-sm p-5 rounded-2xl border border-gray-100/50",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx("img",{src:"https://randomuser.me/api/portraits/men/3.jpg",alt:"Mehmet Öztürk",className:"w-10 h-10 rounded-full object-cover"}),u.jsxs("div",{children:[u.jsx("p",{className:"text-gray-900 font-semibold text-lg",children:"Mehmet Öztürk"}),u.jsx("p",{className:"text-sm text-gray-500",children:"04.06.2025 11:40"})]})]}),u.jsxs("div",{className:"flex flex-col items-end",children:[u.jsx("div",{className:"text-red-500 font-bold text-2xl bg-red-50 px-4 py-1.5 rounded-xl",children:"B2"}),u.jsx("div",{className:"mt-2 text-sm font-medium text-gray-600 bg-gray-50 px-3 py-1 rounded-lg",children:"D:9 • O:9 • Y:6.5 • K:7"})]})]})})]})]})})});function Qt({className:n,...r}){return u.jsx("div",{"data-slot":"card",className:nt("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",n),...r})}function Na({className:n,...r}){return u.jsx("div",{"data-slot":"card-header",className:nt("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",n),...r})}function Ea({className:n,...r}){return u.jsx("div",{"data-slot":"card-title",className:nt("leading-none font-semibold",n),...r})}function Hl({className:n,...r}){return u.jsx("div",{"data-slot":"card-description",className:nt("text-muted-foreground text-sm",n),...r})}function Mt({className:n,...r}){return u.jsx("div",{"data-slot":"card-content",className:nt("px-6",n),...r})}function Ko({className:n,...r}){return u.jsx("div",{"data-slot":"card-footer",className:nt("flex items-center px-6 [.border-t]:pt-6",n),...r})}const c4=()=>u.jsx("div",{children:u.jsx("section",{className:"min-h-screen flex items-center bg-white",children:u.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 lg:py-16",children:u.jsxs("div",{className:"text-center mb-8 sm:mb-12",children:[u.jsxs(Sa,{variant:"secondary",className:"mb-4 sm:mb-6 bg-red-100 text-red-700 border-red-200 px-3 sm:px-4 py-1.5 sm:py-2 text-base sm:text-lg",children:[u.jsx(Ur,{className:"h-4 w-4 sm:h-5 sm:w-5 mr-2"}),"Türkiye'nin En Güvenilir Dil Testi Platformu"]}),u.jsxs("h1",{className:"text-4xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-4 sm:mb-6",children:["Türkçe Dil",u.jsx("br",{}),u.jsx("span",{className:"bg-gradient-to-r from-red-600 to-red-500 bg-clip-text text-transparent",children:"Yeterlilik Testi"})]}),u.jsx("p",{className:"text-lg sm:text-lg md:text-xl text-gray-600 max-w-4xl mx-auto mb-6 sm:mb-8 leading-relaxed",children:"Profesyonel Türkçe dil seviyenizi ölçün. Dinleme, okuma, yazma ve konuşma becerilerinizi kapsamlı şekilde değerlendirin ve sertifikanızı alın."}),u.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-8 sm:mb-10",children:[u.jsx(rt,{to:"/test-selection",children:u.jsxs(Et,{size:"lg",className:"bg-red-600 hover:bg-red-700 text-white px-4 sm:px-6 py-2 sm:py-3 text-lg sm:text-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 w-full sm:w-auto",children:["Ücretsiz Teste Başla",u.jsx(xy,{className:"ml-2 h-5 w-5 sm:h-6 sm:w-6"})]})}),u.jsx(Et,{variant:"outline",size:"lg",className:"border-red-200 text-red-600 hover:bg-red-50 px-4 sm:px-6 py-2 sm:py-3 text-lg sm:text-xl w-full sm:w-auto",children:"Demo İzle"})]}),u.jsxs("div",{className:"flex flex-wrap justify-center items-center gap-4 sm:gap-6 text-sm sm:text-base text-gray-500 mb-8 sm:mb-12",children:[u.jsxs("div",{className:"flex items-center",children:[u.jsx(iu,{className:"h-4 w-4 sm:h-5 sm:w-5 mr-2 text-red-500"}),u.jsx("span",{className:"text-lg sm:text-xl",children:"15,000+ aktif kullanıcı"})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx(lj,{className:"h-4 w-4 sm:h-5 sm:w-5 mr-2 text-red-500"}),u.jsx("span",{className:"text-lg sm:text-xl",children:"Güvenli ve sertifikalı"})]}),u.jsxs("div",{className:"flex items-center",children:[u.jsx(YE,{className:"h-4 w-4 sm:h-5 sm:w-5 mr-2 text-red-500"}),u.jsx("span",{className:"text-lg sm:text-xl",children:"Uluslararası standartlar"})]})]}),u.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 lg:gap-10 max-w-6xl mx-auto",children:[u.jsxs(Qt,{className:"text-center border-red-100 hover:border-red-200 hover:shadow-lg transition-all duration-300 group",children:[u.jsxs(Na,{className:"pb-2 sm:pb-3",children:[u.jsx("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-200",children:u.jsx(QE,{className:"h-5 w-5 sm:h-6 sm:w-6 text-white"})}),u.jsx(Ea,{className:"text-lg sm:text-xl",children:"Dinleme"})]}),u.jsx(Mt,{children:u.jsx("p",{className:"text-sm sm:text-base text-gray-600",children:"Türkçe dinleme becerinizi test edin"})})]}),u.jsxs(Qt,{className:"text-center border-red-100 hover:border-red-200 hover:shadow-lg transition-all duration-300 group",children:[u.jsxs(Na,{className:"pb-2 sm:pb-3",children:[u.jsx("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-200",children:u.jsx(by,{className:"h-5 w-5 sm:h-6 sm:w-6 text-white"})}),u.jsx(Ea,{className:"text-lg sm:text-xl",children:"Okuma"})]}),u.jsx(Mt,{children:u.jsx("p",{className:"text-sm sm:text-base text-gray-600",children:"Metin anlama ve yorumlama"})})]}),u.jsxs(Qt,{className:"text-center border-red-100 hover:border-red-200 hover:shadow-lg transition-all duration-300 group",children:[u.jsxs(Na,{className:"pb-2 sm:pb-3",children:[u.jsx("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-200",children:u.jsx(ej,{className:"h-5 w-5 sm:h-6 sm:w-6 text-white"})}),u.jsx(Ea,{className:"text-lg sm:text-xl",children:"Yazma"})]}),u.jsx(Mt,{children:u.jsx("p",{className:"text-sm sm:text-base text-gray-600",children:"Yazılı ifade becerinizi ölçün"})})]}),u.jsxs(Qt,{className:"text-center border-red-100 hover:border-red-200 hover:shadow-lg transition-all duration-300 group",children:[u.jsxs(Na,{className:"pb-2 sm:pb-3",children:[u.jsx("div",{className:"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform duration-200",children:u.jsx(IE,{className:"h-5 w-5 sm:h-6 sm:w-6 text-white"})}),u.jsx(Ea,{className:"text-lg sm:text-xl",children:"Konuşma"})]}),u.jsx(Mt,{children:u.jsx("p",{className:"text-sm sm:text-base text-gray-600",children:"Sözlü ifade ve telaffuz"})})]})]})]})})})}),d4=()=>u.jsx("div",{children:u.jsx("section",{className:"py-24 bg-white",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs("div",{className:"text-center mb-20",children:[u.jsxs(Sa,{className:"mb-6 bg-red-100 text-red-700 border-red-200",children:[u.jsx(Ur,{className:"h-4 w-4 mr-2"}),"Süreç"]}),u.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Nasıl Çalışır?"}),u.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Sadece üç basit adımda Türkçe dil seviyenizi öğrenin"})]}),u.jsxs("div",{className:"grid md:grid-cols-3 gap-12",children:[u.jsxs("div",{className:"text-center group",children:[u.jsxs("div",{className:"relative mb-8",children:[u.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-red-600 to-red-700 rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-200",children:u.jsx("span",{className:"text-2xl font-bold text-white",children:"1"})}),u.jsx("div",{className:"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center",children:u.jsx(Ur,{className:"h-3 w-3 text-yellow-800"})})]}),u.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Test Seçin"}),u.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Dinleme, okuma, yazma veya konuşma testlerinden birini seçin. Tam değerlendirme için dört testin tamamını alabilirsiniz."})]}),u.jsxs("div",{className:"text-center group",children:[u.jsxs("div",{className:"relative mb-8",children:[u.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-red-600 to-red-700 rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-200",children:u.jsx("span",{className:"text-2xl font-bold text-white",children:"2"})}),u.jsx("div",{className:"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center",children:u.jsx(Ur,{className:"h-3 w-3 text-yellow-800"})})]}),u.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Testi Tamamlayın"}),u.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Gerçek sınav ortamını simüle eden platformumuzda testinizi tamamlayın. Süre takibi ve ilerleme çubuğu ile kendinizi takip edin."})]}),u.jsxs("div",{className:"text-center group",children:[u.jsxs("div",{className:"relative mb-8",children:[u.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-red-600 to-red-700 rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-200",children:u.jsx("span",{className:"text-2xl font-bold text-white",children:"3"})}),u.jsx("div",{className:"absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center",children:u.jsx(Ur,{className:"h-3 w-3 text-yellow-800"})})]}),u.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Sonuçları Alın"}),u.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Anında puanlama ile seviyenizi öğrenin. Detaylı rapor ve gelişim önerileri ile Türkçenizi geliştirin."})]})]})]})})}),f4=()=>u.jsx("div",{children:u.jsx("section",{className:"py-24 bg-white",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs("div",{className:"text-center mb-20",children:[u.jsxs(Sa,{className:"mb-6 bg-red-100 text-red-700 border-red-200",children:[u.jsx(ul,{className:"h-4 w-4 mr-2"}),"Yorumlar"]}),u.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Kullanıcı Deneyimleri"}),u.jsx("p",{className:"text-xl text-gray-600",children:"Binlerce kullanıcının güvendiği platform"})]}),u.jsxs("div",{className:"grid lg:grid-cols-3 gap-8",children:[u.jsx(Qt,{className:"border-red-100 hover:border-red-200 hover:shadow-xl transition-all duration-300 group",children:u.jsxs(Mt,{className:"pt-8",children:[u.jsx("div",{className:"flex mb-6",children:[...Array(5)].map((n,r)=>u.jsx(ul,{className:"h-5 w-5 text-yellow-400 fill-current"},r))}),u.jsx("blockquote",{className:"text-gray-700 mb-6 leading-relaxed",children:'"TürkTest sayesinde Türkçe seviyemi doğru şekilde belirledim. Test sonuçları çok detaylı ve profesyonel. Kesinlikle tavsiye ederim."'}),u.jsxs("div",{className:"border-t border-red-100 pt-4",children:[u.jsx("div",{className:"font-semibold text-gray-900",children:"Ahmet Yılmaz"}),u.jsx("div",{className:"text-sm text-red-600",children:"Öğretmen • Türkçe Seviye: B2"})]})]})}),u.jsx(Qt,{className:"border-red-100 hover:border-red-200 hover:shadow-xl transition-all duration-300 group",children:u.jsxs(Mt,{className:"pt-8",children:[u.jsx("div",{className:"flex mb-6",children:[...Array(5)].map((n,r)=>u.jsx(ul,{className:"h-5 w-5 text-yellow-400 fill-current"},r))}),u.jsx("blockquote",{className:"text-gray-700 mb-6 leading-relaxed",children:'"Konuşma testi özellikle çok başarılı. Telaffuzumu geliştirmek için aldığım geri bildirimler çok faydalı oldu."'}),u.jsxs("div",{className:"border-t border-red-100 pt-4",children:[u.jsx("div",{className:"font-semibold text-gray-900",children:"Fatma Demir"}),u.jsx("div",{className:"text-sm text-red-600",children:"Mühendis • Türkçe Seviye: C1"})]})]})}),u.jsx(Qt,{className:"border-red-100 hover:border-red-200 hover:shadow-xl transition-all duration-300 group",children:u.jsxs(Mt,{className:"pt-8",children:[u.jsx("div",{className:"flex mb-6",children:[...Array(5)].map((n,r)=>u.jsx(ul,{className:"h-5 w-5 text-yellow-400 fill-current"},r))}),u.jsx("blockquote",{className:"text-gray-700 mb-6 leading-relaxed",children:'"Kullanıcı dostu arayüz ve kapsamlı test içeriği. Türkçe öğrenmek isteyenlere kesinlikle tavsiye ederim."'}),u.jsxs("div",{className:"border-t border-red-100 pt-4",children:[u.jsx("div",{className:"font-semibold text-gray-900",children:"Mehmet Özkan"}),u.jsx("div",{className:"text-sm text-red-600",children:"Öğrenci • Türkçe Seviye: A2"})]})]})})]})]})})}),h4=()=>u.jsx("div",{children:u.jsx("section",{id:"pricing",className:"py-24 bg-white",children:u.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[u.jsxs("div",{className:"text-center mb-20",children:[u.jsxs(Sa,{className:"mb-6 bg-red-100 text-red-700 border-red-200",children:[u.jsx(gs,{className:"h-4 w-4 mr-2"}),"Fiyatlar"]}),u.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Fiyat Planları"}),u.jsx("p",{className:"text-xl text-gray-600",children:"İhtiyacınıza uygun planı seçin"})]}),u.jsxs("div",{className:"grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto",children:[u.jsxs(Qt,{className:"relative border-2 border-gray-200 hover:border-red-300 transition-colors h-[450px] flex flex-col",children:[u.jsxs(Na,{className:"text-center pb-4",children:[u.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3",children:u.jsx(nl,{className:"w-6 h-6 text-green-600"})}),u.jsx(Ea,{className:"text-xl font-bold",children:"Başlangıç Deneme"}),u.jsx(Hl,{className:"text-sm",children:"Platformumuzu deneyimlemek isteyen yeni kullanıcılar için mükemmel"})]}),u.jsxs(Mt,{className:"text-center flex-1 flex flex-col justify-between",children:[u.jsxs("div",{children:[u.jsxs("div",{className:"mb-4",children:[u.jsx("span",{className:"text-sm text-gray-600",children:"Birim dahil: "}),u.jsx("span",{className:"text-lg font-bold text-yellow-600",children:"8U"})]}),u.jsx("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Ücretsiz"}),u.jsx("p",{className:"text-sm text-gray-600",children:"İlk kayıt olduğunuzda bonus birimler kazanın"})]}),u.jsx(rt,{to:"/price",className:"mt-auto",children:u.jsx(Et,{className:"w-full bg-red-600 hover:bg-red-700 text-white",children:"Ücretsiz Bonusu Al"})})]})]}),u.jsxs(Qt,{className:"relative border-2 border-gray-200 hover:border-red-300 transition-colors h-[450px] flex flex-col",children:[u.jsxs(Na,{className:"text-center pb-4",children:[u.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3",children:u.jsx(ul,{className:"w-6 h-6 text-blue-600"})}),u.jsx(Ea,{className:"text-xl font-bold",children:"Hızlı Değerlendirme"}),u.jsx(Hl,{className:"text-sm",children:"Hedefli pratik testlerle tahmini puanınızı alın"})]}),u.jsxs(Mt,{className:"text-center flex-1 flex flex-col justify-between",children:[u.jsxs("div",{children:[u.jsxs("div",{className:"mb-4",children:[u.jsx("span",{className:"text-sm text-gray-600",children:"Birim dahil: "}),u.jsx("span",{className:"text-lg font-bold text-yellow-600",children:"15U"})]}),u.jsx("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:"25.000 TL"}),u.jsx("p",{className:"text-sm text-gray-600",children:"Bir kapsamlı sınav veya birden fazla odaklı bölüm için ideal"})]}),u.jsx(rt,{to:"/price",className:"mt-auto",children:u.jsx(Et,{className:"w-full bg-red-600 hover:bg-red-700 text-white",children:"15U Paketi Satın Al"})})]})]}),u.jsxs(Qt,{className:"relative border-2 border-red-300 hover:border-red-400 transition-colors h-[450px] flex flex-col",children:[u.jsx(Sa,{className:"absolute -top-3 left-1/2 transform -translate-x-1/2 bg-red-600 text-white",children:"En Popüler"}),u.jsxs(Na,{className:"text-center pb-4 pt-6",children:[u.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:u.jsx(ul,{className:"w-6 h-6 text-yellow-600"})}),u.jsx(Ea,{className:"text-xl font-bold",children:"Yoğun Hazırlık ⚡"}),u.jsx(Hl,{className:"text-sm",children:"6-8 tam sınav veya odaklı beceri pratiği için mükemmel"})]}),u.jsxs(Mt,{className:"flex-1 flex flex-col justify-between",children:[u.jsxs("ul",{className:"space-y-4",children:[u.jsxs("li",{className:"flex items-center",children:[u.jsx(nl,{className:"h-5 w-5 text-green-600 mr-3"}),u.jsx("span",{children:"Profesyonel'deki Her Şey"})]}),u.jsxs("li",{className:"flex items-center",children:[u.jsx(nl,{className:"h-5 w-5 text-green-600 mr-3"}),u.jsx("span",{children:"Ekip Yönetimi"})]}),u.jsxs("li",{className:"flex items-center",children:[u.jsx(nl,{className:"h-5 w-5 text-green-600 mr-3"}),u.jsx("span",{children:"Özel Raporlama"})]}),u.jsxs("li",{className:"flex items-center",children:[u.jsx(nl,{className:"h-5 w-5 text-green-600 mr-3"}),u.jsx("span",{children:"Öncelikli Destek"})]})]}),u.jsx(Et,{variant:"outline",className:"w-full border-red-200 text-red-600 hover:bg-red-50 mt-auto",children:"İletişime Geç"})]})]})]})]})})});function m4(){return u.jsxs("div",{className:" bg-white",children:[u.jsx(c4,{}),u.jsx(i4,{}),u.jsx(s4,{}),u.jsx(d4,{}),u.jsx(f4,{}),u.jsx(u4,{}),u.jsx(h4,{}),u.jsx("section",{className:"py-24 bg-gradient-to-r from-red-600 to-red-700",children:u.jsxs("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[u.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"Türkçe Seviyenizi Öğrenmeye Hazır mısınız?"}),u.jsx("p",{className:"text-xl text-red-100 mb-12",children:"Binlerce kullanıcının güvendiği platformda Türkçe dil yeterlilik testinizi hemen başlatın."}),u.jsx(rt,{to:"/test-selection",children:u.jsxs(Et,{size:"lg",className:"bg-white text-red-600 hover:bg-gray-100 px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:["Ücretsiz Teste Başla",u.jsx(xy,{className:"ml-2 h-5 w-5"})]})})]})})]})}function v4(){return u.jsx("div",{className:"min-h-screen bg-gray-50",children:u.jsxs("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[u.jsxs("div",{className:"text-center mb-12",children:[u.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Premium Test Planları"}),u.jsx("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto mb-2",children:"Öğrencilerimiz TestMaster Deneme sonuçlarıyla tutarlı Türkçe Yeterlilik puanları elde ediyor."}),u.jsx("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto mb-6",children:"TestMaster, 12.000'den fazla öğrencinin hedef puanlarına ulaşmasına yardımcı oldu."}),u.jsx("p",{className:"text-xl font-semibold text-gray-900 mb-8",children:"Geleceğinize yatırım yapın - aşağıdan size uygun planı seçin."})]}),u.jsxs("div",{className:"rounded-xl p-6 mb-12",children:[u.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4 text-center",children:"Bireysel Test Ücretleri"}),u.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-base font-medium text-gray-600",children:"Dinleme"}),u.jsx("div",{className:"text-lg font-bold text-red-600",children:"3U"})]}),u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-base font-medium text-gray-600",children:"Okuma"}),u.jsx("div",{className:"text-lg font-bold text-red-600",children:"3U"})]}),u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-base font-medium text-gray-600",children:"Yazma"}),u.jsx("div",{className:"text-lg font-bold text-red-600",children:"5U"})]}),u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-base font-medium text-gray-600",children:"Konuşma"}),u.jsx("div",{className:"text-lg font-bold text-red-600",children:"5U"})]}),u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-base font-medium text-gray-600",children:"Tam Test"}),u.jsx("div",{className:"text-lg font-bold text-red-600",children:"12U"})]})]})]}),u.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[u.jsxs(Qt,{className:"relative border-2 border-gray-200 hover:border-red-300 transition-colors flex flex-col",children:[u.jsxs(Na,{className:"text-center flex-grow",children:[u.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto",children:u.jsx(nl,{className:"w-6 h-6 text-green-600"})}),u.jsx(Ea,{className:"text-xl font-bold mt-4",children:"Başlangıç Deneme"}),u.jsx(Hl,{className:"text-sm",children:"Platformumuzu deneyimlemek isteyen yeni kullanıcılar için mükemmel"})]}),u.jsxs(Mt,{className:"text-center",children:[u.jsxs("div",{className:"flex justify-center items-center gap-2",children:[u.jsx("span",{className:"text-sm text-gray-600",children:"Birim dahil:"}),u.jsx("span",{className:"text-lg font-bold text-yellow-600",children:"8U"})]}),u.jsx("div",{className:"text-3xl font-bold text-gray-900 mt-4",children:"Ücretsiz"}),u.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"İlk kayıt olduğunuzda bonus birimler kazanın"})]}),u.jsx(Ko,{className:"mt-auto",children:u.jsx(Et,{className:"bg-red-600 hover:bg-red-700 text-white w-full",children:"Ücretsiz Bonusu Al"})})]}),u.jsxs(Qt,{className:"relative border-2 border-gray-200 hover:border-red-300 transition-colors flex flex-col",children:[u.jsxs(Na,{className:"text-center flex-grow",children:[u.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto",children:u.jsx(Sy,{className:"w-6 h-6 text-blue-600"})}),u.jsx(Ea,{className:"text-xl font-bold mt-4",children:"Hızlı Değerlendirme"}),u.jsx(Hl,{className:"text-sm",children:"Hedefli pratik testlerle tahmini puanınızı alın"})]}),u.jsxs(Mt,{className:"text-center",children:[u.jsxs("div",{className:"flex justify-center items-center gap-2",children:[u.jsx("span",{className:"text-sm text-gray-600",children:"Birim dahil:"}),u.jsx("span",{className:"text-lg font-bold text-yellow-600",children:"15U"})]}),u.jsx("div",{className:"text-3xl font-bold text-gray-900 mt-4",children:"25.000 TL"}),u.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Bir kapsamlı sınav veya birden fazla odaklı bölüm için ideal"})]}),u.jsx(Ko,{className:"mt-auto",children:u.jsx(Et,{className:"bg-red-600 hover:bg-red-700 text-white w-full",children:"15U Paketi Satın Al"})})]}),u.jsxs(Qt,{className:"relative border-2 border-red-300 hover:border-red-400 transition-colors flex flex-col",children:[u.jsx(Sa,{className:"absolute -top-3 left-1/2 transform -translate-x-1/2 bg-red-600 text-white",children:"En Popüler"}),u.jsxs(Na,{className:"text-center flex-grow",children:[u.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto",children:u.jsx(vj,{className:"w-6 h-6 text-yellow-600"})}),u.jsx(Ea,{className:"text-xl font-bold mt-4",children:"Yoğun Hazırlık ⚡"}),u.jsx(Hl,{className:"text-sm",children:"6-8 tam sınav veya odaklı beceri pratiği için mükemmel"})]}),u.jsxs(Mt,{className:"text-center",children:[u.jsxs("div",{className:"flex justify-center items-center gap-2",children:[u.jsx("span",{className:"text-sm text-gray-600",children:"Birim dahil:"}),u.jsx("span",{className:"text-lg font-bold text-yellow-600",children:"50U"})]}),u.jsx("div",{className:"text-3xl font-bold text-gray-900 mt-4",children:"75.000 TL"}),u.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Ciddi sınav adayları için kapsamlı hazırlık"})]}),u.jsx(Ko,{className:"mt-auto",children:u.jsx(Et,{className:"bg-red-600 hover:bg-red-700 text-white w-full",children:"50U Paketi Satın Al"})})]}),u.jsxs(Qt,{className:"relative border-2 border-gray-200 hover:border-red-300 transition-colors flex flex-col",children:[u.jsxs(Na,{className:"text-center flex-grow",children:[u.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto",children:u.jsx(Ny,{className:"w-6 h-6 text-purple-600"})}),u.jsx(Ea,{className:"text-xl font-bold mt-4",children:"Uzman Paketi ✨"}),u.jsx(Hl,{className:"text-sm",children:"Sınırsız pratik fırsatlarıyla nihai hazırlık"})]}),u.jsxs(Mt,{className:"text-center",children:[u.jsxs("div",{className:"flex justify-center items-center gap-2",children:[u.jsx("span",{className:"text-sm text-gray-600",children:"Birim dahil:"}),u.jsx("span",{className:"text-lg font-bold text-yellow-600",children:"120U"})]}),u.jsx("div",{className:"text-3xl font-bold text-gray-900 mt-4",children:"150.000 TL"}),u.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Maksimum puan artışı için tam hakimiyet paketi"})]}),u.jsx(Ko,{className:"mt-auto",children:u.jsx(Et,{className:"bg-red-600 hover:bg-red-700 text-white w-full",children:"120U Paketi Satın Al"})})]})]}),u.jsx("div",{className:"mt-12 text-center",children:u.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6 max-w-4xl mx-auto",children:[u.jsx(iu,{className:"w-8 h-8 text-red-600 mx-auto mb-3"}),u.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Binlerce Başarılı Öğrenciye Katılın"}),u.jsx("p",{className:"text-gray-600",children:"Kanıtlanmış metodolojimiz ve kapsamlı pratik testlerimiz, dünya çapında öğrencilerin hedef Türkçe Yeterlilik puanlarına ulaşmalarına yardımcı oldu. Başarı yolculuğunuza bugün başlayın!"})]})})]})})}var Fd={exports:{}},Zd={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kg;function g4(){if(kg)return Zd;kg=1;var n=du();function r(S,w){return S===w&&(S!==0||1/S===1/w)||S!==S&&w!==w}var i=typeof Object.is=="function"?Object.is:r,o=n.useState,d=n.useEffect,f=n.useLayoutEffect,m=n.useDebugValue;function h(S,w){var T=w(),j=o({inst:{value:T,getSnapshot:w}}),N=j[0].inst,C=j[1];return f(function(){N.value=T,N.getSnapshot=w,v(N)&&C({inst:N})},[S,T,w]),d(function(){return v(N)&&C({inst:N}),S(function(){v(N)&&C({inst:N})})},[S]),m(T),T}function v(S){var w=S.getSnapshot;S=S.value;try{var T=w();return!i(S,T)}catch{return!0}}function g(S,w){return w()}var y=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?g:h;return Zd.useSyncExternalStore=n.useSyncExternalStore!==void 0?n.useSyncExternalStore:y,Zd}var Lg;function p4(){return Lg||(Lg=1,Fd.exports=g4()),Fd.exports}var y4=p4();function x4(){return y4.useSyncExternalStore(b4,()=>!0,()=>!1)}function b4(){return()=>{}}var Qf="Avatar",[w4,fT]=Jr(Qf),[S4,Vy]=w4(Qf),ou=x.forwardRef((n,r)=>{const{__scopeAvatar:i,...o}=n,[d,f]=x.useState("idle");return u.jsx(S4,{scope:i,imageLoadingStatus:d,onImageLoadingStatusChange:f,children:u.jsx(Xe.span,{...o,ref:r})})});ou.displayName=Qf;var Qy="AvatarImage",uu=x.forwardRef((n,r)=>{const{__scopeAvatar:i,src:o,onLoadingStatusChange:d=()=>{},...f}=n,m=Vy(Qy,i),h=N4(o,f),v=Wt(g=>{d(g),m.onImageLoadingStatusChange(g)});return Ha(()=>{h!=="idle"&&v(h)},[h,v]),h==="loaded"?u.jsx(Xe.img,{...f,ref:r,src:o}):null});uu.displayName=Qy;var Ky="AvatarFallback",cu=x.forwardRef((n,r)=>{const{__scopeAvatar:i,delayMs:o,...d}=n,f=Vy(Ky,i),[m,h]=x.useState(o===void 0);return x.useEffect(()=>{if(o!==void 0){const v=window.setTimeout(()=>h(!0),o);return()=>window.clearTimeout(v)}},[o]),m&&f.imageLoadingStatus!=="loaded"?u.jsx(Xe.span,{...d,ref:r}):null});cu.displayName=Ky;function Ug(n,r){return n?r?(n.src!==r&&(n.src=r),n.complete&&n.naturalWidth>0?"loaded":"loading"):"error":"idle"}function N4(n,{referrerPolicy:r,crossOrigin:i}){const o=x4(),d=x.useRef(null),f=o?(d.current||(d.current=new window.Image),d.current):null,[m,h]=x.useState(()=>Ug(f,n));return Ha(()=>{h(Ug(f,n))},[f,n]),Ha(()=>{const v=S=>()=>{h(S)};if(!f)return;const g=v("loaded"),y=v("error");return f.addEventListener("load",g),f.addEventListener("error",y),r&&(f.referrerPolicy=r),typeof i=="string"&&(f.crossOrigin=i),()=>{f.removeEventListener("load",g),f.removeEventListener("error",y)}},[f,i,r]),m}var E4=ou,j4=uu,T4=cu;function R4({className:n,...r}){return u.jsx(E4,{"data-slot":"avatar",className:nt("relative flex size-8 shrink-0 overflow-hidden rounded-full",n),...r})}function M4({className:n,...r}){return u.jsx(j4,{"data-slot":"avatar-image",className:nt("aspect-square size-full",n),...r})}function A4({className:n,...r}){return u.jsx(T4,{"data-slot":"avatar-fallback",className:nt("bg-muted flex size-full items-center justify-center rounded-full",n),...r})}const Xa={name:"Ahmet Yılmaz",username:"@ahmetyilmaz",avatar:"https://i.pravatar.cc/300",followers:234,following:189,joinDate:"Mart 2023",totalPoints:2847,bio:"Türkçe dil ve kültürünü öğrenmeye tutkulu. Şu anda B1 sertifikası için hazırlanıyorum."},C4=()=>{const[n,r]=x.useState(!1);return u.jsxs("div",{children:[u.jsx(Mt,{className:"py-8",children:u.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[u.jsxs("div",{className:"flex flex-col items-center md:items-start relative",children:[u.jsxs(R4,{className:"w-32 h-32 border-4 border-white shadow-lg",children:[u.jsx(M4,{src:Xa.avatar,alt:Xa.name}),u.jsx(A4,{className:"text-2xl font-semibold text-red-700",children:Xa.name.split(" ").map(i=>i[0]).join("")})]}),u.jsx(Et,{className:"absolute top-0 right-0 p-2 bg-white rounded-full",children:u.jsx(Cg,{className:"w-4 h-4 text-red-600"})})]}),u.jsxs("div",{className:"flex-1 space-y-4",children:[u.jsxs("div",{className:"flex justify-between items-start",children:[u.jsxs("div",{children:[u.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-1",children:Xa.name}),u.jsx("p",{className:"text-lg text-gray-600 mb-3",children:Xa.username}),u.jsx("p",{className:"text-gray-700 leading-relaxed",children:Xa.bio})]}),u.jsxs(Et,{variant:"outline",className:" bg-red-500 text-white rounded-lg",children:[u.jsx(Cg,{className:"w-4 h-4 "}),"Profili Düzenle"]})]}),u.jsxs("div",{className:"flex flex-wrap gap-6 text-sm",children:[u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(zE,{className:"w-4 h-4 text-gray-500"}),u.jsxs("span",{className:"text-gray-600",children:[Xa.joinDate," tarihinde katıldı"]})]}),u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(ul,{className:"w-4 h-4 text-red-500"}),u.jsxs("span",{className:"font-medium text-gray-900",children:[Xa.totalPoints.toLocaleString()," puan"]})]})]}),u.jsxs("div",{className:"flex items-center gap-6 text-sm",children:[u.jsxs("div",{children:[u.jsx("span",{className:"font-semibold text-gray-900",children:Xa.followers}),u.jsx("span",{className:"text-gray-600 ml-1",children:"takipçi"})]}),u.jsxs("div",{children:[u.jsx("span",{className:"font-semibold text-gray-900",children:Xa.following}),u.jsx("span",{className:"text-gray-600 ml-1",children:"takip edilen"})]})]}),u.jsx("div",{className:"flex gap-3 pt-2",children:u.jsxs(Et,{onClick:()=>r(!n),className:` ${n?"bg-white border border-red-200 text-red-600 hover:bg-red-50":"bg-red-600 hover:bg-red-700 text-white"}`,children:[u.jsx(Ey,{className:"w-4 h-4 mr-2"}),n?"Takip Ediliyor":"Takip Et"]})})]})]})}),u.jsx("div",{className:"h-0.5 bg-gray-200 w-full mt-4"})]})};var $d="rovingFocusGroup.onEntryFocus",D4={bubbles:!1,cancelable:!0},ps="RovingFocusGroup",[vf,Py,O4]=Uf(ps),[z4,Xy]=Jr(ps,[O4]),[_4,k4]=z4(ps),Fy=x.forwardRef((n,r)=>u.jsx(vf.Provider,{scope:n.__scopeRovingFocusGroup,children:u.jsx(vf.Slot,{scope:n.__scopeRovingFocusGroup,children:u.jsx(L4,{...n,ref:r})})}));Fy.displayName=ps;var L4=x.forwardRef((n,r)=>{const{__scopeRovingFocusGroup:i,orientation:o,loop:d=!1,dir:f,currentTabStopId:m,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:v,onEntryFocus:g,preventScrollOnEntryFocus:y=!1,...S}=n,w=x.useRef(null),T=Kt(r,w),j=Lf(f),[N,C]=vs({prop:m,defaultProp:h??null,onChange:v,caller:ps}),[M,P]=x.useState(!1),_=Wt(g),V=Py(i),q=x.useRef(!1),[O,te]=x.useState(0);return x.useEffect(()=>{const K=w.current;if(K)return K.addEventListener($d,_),()=>K.removeEventListener($d,_)},[_]),u.jsx(_4,{scope:i,orientation:o,dir:j,loop:d,currentTabStopId:N,onItemFocus:x.useCallback(K=>C(K),[C]),onItemShiftTab:x.useCallback(()=>P(!0),[]),onFocusableItemAdd:x.useCallback(()=>te(K=>K+1),[]),onFocusableItemRemove:x.useCallback(()=>te(K=>K-1),[]),children:u.jsx(Xe.div,{tabIndex:M||O===0?-1:0,"data-orientation":o,...S,ref:T,style:{outline:"none",...n.style},onMouseDown:Ke(n.onMouseDown,()=>{q.current=!0}),onFocus:Ke(n.onFocus,K=>{const W=!q.current;if(K.target===K.currentTarget&&W&&!M){const ge=new CustomEvent($d,D4);if(K.currentTarget.dispatchEvent(ge),!ge.defaultPrevented){const Le=V().filter(L=>L.focusable),qe=Le.find(L=>L.active),pt=Le.find(L=>L.id===N),Ne=[qe,pt,...Le].filter(Boolean).map(L=>L.ref.current);Jy(Ne,y)}}q.current=!1}),onBlur:Ke(n.onBlur,()=>P(!1))})})}),Zy="RovingFocusGroupItem",$y=x.forwardRef((n,r)=>{const{__scopeRovingFocusGroup:i,focusable:o=!0,active:d=!1,tabStopId:f,children:m,...h}=n,v=Kl(),g=f||v,y=k4(Zy,i),S=y.currentTabStopId===g,w=Py(i),{onFocusableItemAdd:T,onFocusableItemRemove:j,currentTabStopId:N}=y;return x.useEffect(()=>{if(o)return T(),()=>j()},[o,T,j]),u.jsx(vf.ItemSlot,{scope:i,id:g,focusable:o,active:d,children:u.jsx(Xe.span,{tabIndex:S?0:-1,"data-orientation":y.orientation,...h,ref:r,onMouseDown:Ke(n.onMouseDown,C=>{o?y.onItemFocus(g):C.preventDefault()}),onFocus:Ke(n.onFocus,()=>y.onItemFocus(g)),onKeyDown:Ke(n.onKeyDown,C=>{if(C.key==="Tab"&&C.shiftKey){y.onItemShiftTab();return}if(C.target!==C.currentTarget)return;const M=B4(C,y.orientation,y.dir);if(M!==void 0){if(C.metaKey||C.ctrlKey||C.altKey||C.shiftKey)return;C.preventDefault();let _=w().filter(V=>V.focusable).map(V=>V.ref.current);if(M==="last")_.reverse();else if(M==="prev"||M==="next"){M==="prev"&&_.reverse();const V=_.indexOf(C.currentTarget);_=y.loop?q4(_,V+1):_.slice(V+1)}setTimeout(()=>Jy(_))}}),children:typeof m=="function"?m({isCurrentTabStop:S,hasTabStop:N!=null}):m})})});$y.displayName=Zy;var U4={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function H4(n,r){return r!=="rtl"?n:n==="ArrowLeft"?"ArrowRight":n==="ArrowRight"?"ArrowLeft":n}function B4(n,r,i){const o=H4(n.key,i);if(!(r==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(r==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return U4[o]}function Jy(n,r=!1){const i=document.activeElement;for(const o of n)if(o===i||(o.focus({preventScroll:r}),document.activeElement!==i))return}function q4(n,r){return n.map((i,o)=>n[(r+o)%n.length])}var G4=Fy,Y4=$y,yu="Tabs",[V4,hT]=Jr(yu,[Xy]),Iy=Xy(),[Q4,Kf]=V4(yu),Wy=x.forwardRef((n,r)=>{const{__scopeTabs:i,value:o,onValueChange:d,defaultValue:f,orientation:m="horizontal",dir:h,activationMode:v="automatic",...g}=n,y=Lf(h),[S,w]=vs({prop:o,onChange:d,defaultProp:f??"",caller:yu});return u.jsx(Q4,{scope:i,baseId:Kl(),value:S,onValueChange:w,orientation:m,dir:y,activationMode:v,children:u.jsx(Xe.div,{dir:y,"data-orientation":m,...g,ref:r})})});Wy.displayName=yu;var ex="TabsList",tx=x.forwardRef((n,r)=>{const{__scopeTabs:i,loop:o=!0,...d}=n,f=Kf(ex,i),m=Iy(i);return u.jsx(G4,{asChild:!0,...m,orientation:f.orientation,dir:f.dir,loop:o,children:u.jsx(Xe.div,{role:"tablist","aria-orientation":f.orientation,...d,ref:r})})});tx.displayName=ex;var ax="TabsTrigger",nx=x.forwardRef((n,r)=>{const{__scopeTabs:i,value:o,disabled:d=!1,...f}=n,m=Kf(ax,i),h=Iy(i),v=rx(m.baseId,o),g=ix(m.baseId,o),y=o===m.value;return u.jsx(Y4,{asChild:!0,...h,focusable:!d,active:y,children:u.jsx(Xe.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":g,"data-state":y?"active":"inactive","data-disabled":d?"":void 0,disabled:d,id:v,...f,ref:r,onMouseDown:Ke(n.onMouseDown,S=>{!d&&S.button===0&&S.ctrlKey===!1?m.onValueChange(o):S.preventDefault()}),onKeyDown:Ke(n.onKeyDown,S=>{[" ","Enter"].includes(S.key)&&m.onValueChange(o)}),onFocus:Ke(n.onFocus,()=>{const S=m.activationMode!=="manual";!y&&!d&&S&&m.onValueChange(o)})})})});nx.displayName=ax;var lx="TabsContent",gf=x.forwardRef((n,r)=>{const{__scopeTabs:i,value:o,forceMount:d,children:f,...m}=n,h=Kf(lx,i),v=rx(h.baseId,o),g=ix(h.baseId,o),y=o===h.value,S=x.useRef(y);return x.useEffect(()=>{const w=requestAnimationFrame(()=>S.current=!1);return()=>cancelAnimationFrame(w)},[]),u.jsx(Nn,{present:d||y,children:({present:w})=>u.jsx(Xe.div,{"data-state":y?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":v,hidden:!w,id:g,tabIndex:0,...m,ref:r,style:{...n.style,animationDuration:S.current?"0s":void 0},children:w&&f})})});gf.displayName=lx;function rx(n,r){return`${n}-trigger-${r}`}function ix(n,r){return`${n}-content-${r}`}var K4=Wy,P4=tx,X4=nx;function F4({className:n,...r}){return u.jsx(K4,{"data-slot":"tabs",className:nt("flex flex-col gap-2",n),...r})}function Z4({className:n,...r}){return u.jsx(P4,{"data-slot":"tabs-list",className:nt("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",n),...r})}function Hg({className:n,...r}){return u.jsx(X4,{"data-slot":"tabs-trigger",className:nt("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",n),...r})}const Po=[{name:"Elif Kaya",avatar:"/placeholder.svg?height=40&width=40",level:"B1",status:"online",streak:12},{name:"Mehmet Öz",avatar:"/placeholder.svg?height=40&width=40",level:"A2",status:"offline",streak:8},{name:"Zeynep Ak",avatar:"/placeholder.svg?height=40&width=40",level:"B2",status:"online",streak:25},{name:"Can Demir",avatar:"/placeholder.svg?height=40&width=40",level:"A2",status:"online",streak:5},{name:"Ayşe Yılmaz",avatar:"/placeholder.svg?height=40&width=40",level:"B1",status:"online",streak:15}],$4=()=>u.jsx("div",{className:"w-full h-fit",children:u.jsx(Qt,{className:"border-red-100 hover:border-red-200 transition-all duration-300 mx-4",title:"Friends",children:u.jsx(Mt,{className:"p-4",children:u.jsxs(F4,{defaultValue:"following",className:"w-full",children:[u.jsxs(Z4,{className:"w-full flex border-b border-gray-200 mb-4",children:[u.jsx(Hg,{value:"following",className:"flex-1 pb-2 data-[state=active]:text-red-600 data-[state=active]:border-b-2 data-[state=active]:border-red-600 text-gray-600 hover:text-red-600 transition-colors text-base md:text-lg",children:"Takip Edilenler"}),u.jsx(Hg,{value:"followers",className:"flex-1 pb-2 data-[state=active]:text-red-600 data-[state=active]:border-b-2 data-[state=active]:border-red-600 text-gray-600 hover:text-red-600 transition-colors text-base md:text-lg",children:"Takipçiler"})]}),u.jsxs(gf,{value:"following",children:[u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(iu,{className:"w-4 h-4 text-red-600"}),u.jsx("span",{className:"text-base md:text-lg",children:"Takip Edilenler"})]}),u.jsx(Sa,{variant:"secondary",className:"text-sm md:text-base",children:Po.length})]}),u.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:Po.map((n,r)=>u.jsxs("div",{className:"flex items-center justify-between p-3 border border-red-100 rounded-lg hover:shadow-sm transition-all duration-300 group",children:[u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsxs("div",{className:"relative",children:[u.jsxs(ou,{className:"w-8 h-8",children:[u.jsx(uu,{src:n.avatar||"/placeholder.svg",alt:n.name,className:"rounded-full"}),u.jsx(cu,{className:"bg-red-50 text-red-700 text-xs w-8 h-8 rounded-full flex items-center justify-center",children:n.name.split(" ").map(i=>i[0]).join("")})]}),u.jsx("div",{className:`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border border-white ${n.status==="online"?"bg-green-500":"bg-gray-400"}`})]}),u.jsxs("div",{className:"min-w-0 flex-1",children:[u.jsx("h4",{className:"font-medium text-gray-900 text-base md:text-lg truncate group-hover:text-red-700 transition-colors",children:n.name}),u.jsxs("div",{className:"flex items-center gap-2 text-sm md:text-base text-gray-600",children:[u.jsx(Sa,{className:"bg-red-100 text-red-800 border border-red-200 text-sm md:text-base px-1 py-0",children:n.level}),u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(Ag,{className:"w-3 h-3 text-red-500"}),u.jsx("span",{children:n.streak})]})]})]})]}),u.jsx(wy,{size:24,className:"cursor-pointer text-red-500"})]},r))})]}),u.jsxs(gf,{value:"followers",children:[u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(iu,{className:"w-4 h-4 text-red-600"}),u.jsx("span",{className:"text-base md:text-lg",children:"Takipçiler"})]}),u.jsx(Sa,{variant:"secondary",className:"text-sm md:text-base",children:Po.length})]}),u.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:Po.map((n,r)=>u.jsxs("div",{className:"flex items-center justify-between p-3 border border-red-100 rounded-lg hover:shadow-sm transition-all duration-300 group",children:[u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsxs("div",{className:"relative",children:[u.jsxs(ou,{className:"w-8 h-8",children:[u.jsx(uu,{src:n.avatar||"/placeholder.svg",alt:n.name,className:"rounded-full"}),u.jsx(cu,{className:"bg-red-50 text-red-700 text-xs w-8 h-8 rounded-full flex items-center justify-center",children:n.name.split(" ").map(i=>i[0]).join("")})]}),u.jsx("div",{className:`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border border-white ${n.status==="online"?"bg-green-500":"bg-gray-400"}`})]}),u.jsxs("div",{className:"min-w-0 flex-1",children:[u.jsx("h4",{className:"font-medium text-gray-900 text-base md:text-lg truncate group-hover:text-red-700 transition-colors",children:n.name}),u.jsxs("div",{className:"flex items-center gap-2 text-sm md:text-base text-gray-600",children:[u.jsx(Sa,{className:"bg-red-100 text-red-800 border border-red-200 text-sm md:text-base px-1 py-0",children:n.level}),u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(Ag,{className:"w-3 h-3 text-red-500"}),u.jsx("span",{children:n.streak})]})]})]})]}),u.jsx(Et,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-red-600 hover:bg-red-50",children:u.jsx(Ey,{className:"w-3 h-3"})})]},r))})]})]})})})}),J4=(n,r)=>{const i=n.completed;return u.jsx("svg",{className:"absolute inset-0 w-full h-full pointer-events-none",children:u.jsx("line",{x1:`${n.position.x}%`,y1:`${n.position.y}%`,x2:`${r.position.x}%`,y2:`${r.position.y}%`,stroke:i?"#8b5cf6":"#e5e7eb",strokeWidth:i?"3":"2",strokeDasharray:i?"none":"6,4",className:"transition-all duration-500"})})},In=[{id:"a1",level:"A1",name:"Temel",description:"Türkçe dilinin temellerini öğrenin",requiredPoints:500,completed:!0,current:!1,locked:!1,position:{x:18,y:75},stars:5,estimatedHours:40,topics:["Temel Kelimeler","Selamlaşma","Sayılar","Telaffuz"]},{id:"a2",level:"A2",name:"İletişim",description:"Günlük konuşma becerilerini geliştirin",requiredPoints:1e3,completed:!1,current:!0,locked:!1,position:{x:35,y:55},stars:3,estimatedHours:60,topics:["Günlük Rutinler","Alışveriş","Aile","Geçmiş Zaman"]},{id:"b1",level:"B1",name:"İfade",description:"Karmaşık fikirleri akıcı şekilde ifade edin",requiredPoints:2e3,completed:!1,current:!1,locked:!0,position:{x:52,y:35},stars:0,estimatedHours:80,topics:["Görüşler","Seyahat","İş","Gelecek Planları"]},{id:"b2",level:"B2",name:"Ustalık",description:"Gelişmiş tartışmalar ve resmi yazım",requiredPoints:3500,completed:!1,current:!1,locked:!0,position:{x:69,y:25},stars:0,estimatedHours:100,topics:["Tartışmalar","Resmi Yazım","Kültür","Soyut Kavramlar"]},{id:"c1",level:"C1",name:"Uzmanlık",description:"Ana dile yakın yeterlilik ve akademik akıcılık",requiredPoints:5e3,completed:!1,current:!1,locked:!0,position:{x:82,y:45},stars:0,estimatedHours:120,topics:["Akademik Türkçe","Profesyonel","Edebiyat","İnce İfadeler"]},{id:"c2",level:"C2",name:"Mükemmellik",description:"Ana dil seviyesinde tam ustalık",requiredPoints:7e3,completed:!1,current:!1,locked:!0,position:{x:88,y:15},stars:0,estimatedHours:150,topics:["Ana Dil Akıcılığı","Öğretmenlik","Kültürel Uzmanlık","Mükemmel İfade"]}],I4=()=>{var m;const[n,r]=x.useState(null),[i,o]=x.useState(!1),[d,f]=x.useState(null);return u.jsx("div",{children:u.jsx("div",{className:"w-full mx-auto",children:u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-20 gap-4",children:[u.jsx("div",{className:"lg:col-span-13 grid grid-cols-1 md:grid-cols-1 gap-4",children:u.jsxs("div",{className:"border-red-100 bg-white overflow-hidden transition-all duration-300 rounded-md",children:[u.jsxs("div",{className:"flex items-center justify-between  px-8 py-6",children:[u.jsxs("div",{children:[u.jsx("h2",{className:"text-2xl font-semibold text-red-500 mb-1",children:"Türkçe Öğrenme Yolculuğu"}),u.jsx("p",{className:"text-white/80",children:"Akıcılığa giden yolunuz"})]}),u.jsxs("div",{className:"flex items-center gap-8 text-white",children:[u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-xl font-semibold",children:"2,847"}),u.jsx("div",{className:"text-xs text-white/80 uppercase tracking-wide",children:"Puan"})]}),u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"text-xl font-semibold",children:"1/6"}),u.jsx("div",{className:"text-xs text-white/80 uppercase tracking-wide",children:"Seviye"})]})]})]}),u.jsx(Mt,{className:"p-0",children:u.jsxs("div",{className:"relative h-[500px]  bg-opacity-10",children:[u.jsx("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M54.627 0l.83.828-1.415 1.415L51.8 0h2.827zM5.373 0l-.83.828L5.96 2.243 8.2 0H5.374zM48.97 0l3.657 3.657-1.414 1.414L46.143 0h2.828zM11.03 0L7.372 3.657 8.787 5.07 13.857 0H11.03zm32.284 0L49.8 6.485 48.384 7.9l-7.9-7.9h2.83zM16.686 0L10.2 6.485 11.616 7.9l7.9-7.9h-2.83zM22.343 0L13.8 8.544l1.414 1.414 9.9-9.9h-2.77zm22.628 0L53.8 8.828l-1.415 1.415L41.456 0h3.515zM32.657 0L41.2 8.544l-1.414 1.414-9.9-9.9h2.77zm-6.985 0L36.143 10.515l-1.414 1.414L22.457 0h3.215zM24.17 0L36.67 12.5l-1.414 1.414L20.485 0h3.685zm16.943 0L50.8 9.485l-1.414 1.414L36.114 0h4.997zm-8.6 0L47.8 15.485l-1.414 1.414-17.9-17.9h4.656zM0 47.8l8.485 8.485-1.414 1.414L0 50.627V47.8zm0-4.657l10.485 10.485-1.414 1.414L0 45.97v-2.827zm0-4.657l12.485 12.485-1.414 1.414L0 41.314v-2.827zm0-4.657l14.485 14.485-1.414 1.414L0 36.657V33.83zm0-4.657l16.485 16.485-1.414 1.414L0 32V29.173zm0-4.657l18.485 18.485-1.414 1.414L0 27.343v-2.827zm0-4.657l20.485 20.485-1.414 1.414L0 22.686v-2.827zm0-4.657l22.485 22.485-1.414 1.414L0 18.03v-2.827zm0-4.657l24.485 24.485-1.414 1.414L0 13.372v-2.827zm0-4.657l26.485 26.485-1.414 1.414L0 8.715V5.888zm0-4.657l28.485 28.485-1.414 1.414L0 4.058V1.23z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E")`}}),In.map((h,v)=>v<In.length-1?u.jsx("div",{children:J4(h,In[v+1])},`path-${v}`):null),In.map(h=>u.jsxs("div",{className:"absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-300 hover:scale-105",style:{left:`${h.position.x}%`,top:`${h.position.y}%`},onClick:()=>{if(h.locked){const v=In[In.findIndex(g=>g.id===h.id)-1];f({level:h.level,prevLevel:v.level}),o(!0)}else r(n===h.id?null:h.id)},children:[u.jsx("div",{className:`relative w-16 h-16 rounded-full border-4 border-white  flex items-center justify-center font-semibold text-white transition-all duration-300 ${h.completed?"bg-red-600":h.current?"bg-red-600 ring-4 ring-red-200":h.locked?"bg-gray-400":"bg-gray-300"}`,children:h.completed?u.jsx(nl,{className:"w-7 h-7"}):h.current?u.jsx(Io,{className:"w-6 h-6"}):h.locked?u.jsx(PE,{className:"w-6 h-6"}):u.jsx(FE,{className:"w-6 h-6"})}),u.jsx("div",{className:"absolute -top-2 -right-2 bg-white rounded-full px-2 py-1 text-xs font-bold text-red-700 shadow-md border border-red-200",children:h.level}),h.stars>0&&u.jsx("div",{className:"absolute -top-1 -left-1 flex",children:[...Array(Math.min(h.stars,3))].map((v,g)=>u.jsx(ul,{className:"w-3 h-3 text-yellow-500 fill-current"},g))}),u.jsx("div",{className:"absolute top-20 left-1/2 transform -translate-x-1/2 text-center",children:u.jsx("div",{className:`px-3 py-1 rounded-lg text-sm font-medium shadow-sm ${h.completed?"bg-red-50 text-red-700 border border-red-200":h.current?"bg-red-100 text-red-800 border border-red-300":"bg-white text-gray-600 border border-gray-200"}`,children:h.name})})]},h.id)),i&&d&&u.jsx("div",{className:"fixed inset-0  flex items-center justify-center z-50",children:u.jsxs("div",{className:"bg-white rounded-xl p-6 max-w-md mx-4",children:[u.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Seviye Kilitli"}),u.jsx("p",{className:"text-gray-600 mb-6",children:`${d.level} seviyesini açmak için ${d.prevLevel} seviyesini tamamlamalı ve en az 3 yıldız kazanmalısınız.`}),u.jsx(Et,{onClick:()=>o(!1),className:"w-full bg-red-600 hover:bg-red-700 text-white",children:"Anladım"})]})}),n&&!((m=In.find(h=>h.id===n))!=null&&m.locked)&&u.jsx("div",{className:"absolute bottom-6 left-6 right-6 bg-white rounded-xl shadow-xl border border-red-200 animate-in slide-in-from-bottom duration-300",children:(()=>{const h=In.find(v=>v.id===n);return h?u.jsxs("div",{className:"p-6",children:[u.jsxs("div",{className:"flex items-start justify-between mb-4",children:[u.jsxs("div",{className:"flex-1",children:[u.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[u.jsxs("h3",{className:"text-xl font-semibold text-gray-900",children:["Seviye ",h.level]}),u.jsx(ME,{className:`${h.completed?"bg-red-100 text-red-700":h.current?"bg-red-600 text-white":"bg-gray-100 text-gray-600"}`,children:h.name})]}),u.jsx("p",{className:"text-gray-600 mb-3",children:h.description}),u.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(Sy,{className:"w-4 h-4"}),u.jsxs("span",{children:[h.requiredPoints," puan"]})]}),u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(HE,{className:"w-4 h-4"}),u.jsxs("span",{children:[h.estimatedHours," saat"]})]}),u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx(by,{className:"w-4 h-4"}),u.jsxs("span",{children:[h.topics.length," konu"]})]})]})]}),!h.locked&&u.jsx(Et,{className:`ml-4 ${h.current?"bg-[#58cc02] hover:bg-[#76d91c] text-white":h.completed?"bg-green-100 hover:bg-green-200 text-green-700":"bg-[#58cc02] hover:bg-[#76d91c] text-white"}`,children:h.current?u.jsxs(u.Fragment,{children:[u.jsx(Io,{className:"w-4 h-4 mr-2"}),"Devam Et"]}):h.completed?u.jsxs(u.Fragment,{children:[u.jsx(Ny,{className:"w-4 h-4 mr-2"}),"Gözden Geçir"]}):u.jsxs(u.Fragment,{children:[u.jsx(Io,{className:"w-4 h-4 mr-2"}),"Başla"]})})]}),u.jsxs("div",{children:[u.jsxs("h4",{className:"text-sm font-medium text-gray-700 mb-2 flex items-center gap-2",children:[u.jsx(DE,{className:"w-4 h-4"}),"Ana Konular"]}),u.jsx("div",{className:"grid grid-cols-2 gap-2",children:h.topics.map((v,g)=>u.jsxs("div",{className:"flex items-center gap-2 p-2 bg-green-50 rounded-lg",children:[u.jsx("div",{className:`w-2 h-2 rounded-full ${h.completed?"bg-[#58cc02]":h.current?"bg-[#76d91c]":"bg-gray-300"}`}),u.jsx("span",{className:"text-sm text-gray-600",children:v})]},g))})]})]}):null})()})]})})]})}),u.jsx("div",{className:"lg:col-span-7",children:u.jsx($4,{})})]})})})},W4=[{type:"Dinleme Sınavı",level:"A2",date:"20 Aralık 2024",questions:50,duration:"12 dakika"},{type:"Okuma Sınavı",level:"A2",date:"17 Aralık 2024",questions:40,duration:"18 dakika"},{type:"Türkçe Yeterlilik Sınavı",level:"A2",date:"15 Aralık 2024",questions:100,duration:"45 dakika"}],eT=()=>u.jsx("div",{className:"max-w-7xl mx-auto",children:u.jsxs("div",{className:"bg-white shadow-lg rounded-xl border-0 ",children:[u.jsx(Na,{className:"bg-gradient-to-r from-red-50 to-red-100 rounded-t-xl",children:u.jsxs(Ea,{className:"flex items-center gap-3 text-xl",children:[u.jsx(gs,{className:"w-6 h-6 text-red-500"}),u.jsx("span",{className:"text-gray-800 py-2",children:"Son Sınav Performansı"})]})}),u.jsx(Mt,{className:"p-6",children:u.jsx("div",{className:"space-y-6",children:W4.map((n,r)=>u.jsx("div",{className:"bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300",children:u.jsx("div",{className:"p-5",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"space-y-2",children:[u.jsx("h4",{className:"text-lg font-semibold text-gray-800 hover:text-red-600 transition-colors",children:n.type}),u.jsxs("div",{className:"flex items-center gap-3 text-sm text-gray-500",children:[u.jsxs("span",{className:"flex items-center gap-1",children:[u.jsx("span",{children:"📅"})," ",n.date]}),u.jsx("span",{children:"•"}),u.jsxs("span",{className:"flex items-center gap-1",children:[u.jsx("span",{children:"❓"})," ",n.questions," soru"]}),u.jsx("span",{children:"•"}),u.jsxs("span",{className:"flex items-center gap-1",children:[u.jsx("span",{children:"⏱️"})," ",n.duration]})]})]}),u.jsx("div",{className:"text-right flex flex-col items-end gap-2",children:u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsx(Sa,{className:"bg-red-50 text-red-700 px-3 py-1 text-sm font-medium rounded-full",children:n.level}),u.jsx(wy,{className:"w-5 h-5 text-gray-400 hover:text-red-500 cursor-pointer transition-colors"})]})})]})})},r))})})]})}),tT=()=>u.jsx("div",{children:u.jsx("section",{className:"py-28 bg-gradient-to-r from-red-600 to-red-700",children:u.jsxs("div",{className:"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[u.jsx("div",{className:"flex justify-center mb-6",children:u.jsx(Ur,{className:"h-6 w-6 mr-2 text-white"})}),u.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-4",children:"Türkçe Seviyenizi Yükseltmeye Hazır mısınız?"}),u.jsx("p",{className:"text-xl text-red-100 mb-8",children:"Kişiselleştirilmiş testler ve detaylı analizlerle Türkçenizi geliştirin."}),u.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[u.jsxs(Et,{size:"lg",className:"bg-white text-red-600 hover:bg-gray-100 px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105",children:["Yeni Test Başlat",u.jsx(Io,{className:"ml-2 h-5 w-5"})]}),u.jsxs(Et,{size:"lg",variant:"outline",className:"border-white text-white hover:bg-white/10 px-8 py-4 text-lg",children:["İlerleme Raporunu Görüntüle",u.jsx(gs,{className:"ml-2 h-5 w-5"})]})]})]})})});function aT(){return u.jsxs("div",{className:"min-h-screen bg-white ",children:[u.jsxs("div",{className:"max-w-[1180px] mx-auto  space-y-6 my-10",children:[u.jsx(C4,{}),u.jsx(I4,{}),u.jsx(eT,{})]}),u.jsx(tT,{})]})}const nT=Qw([{path:"/",element:u.jsx(r4,{}),children:[{index:!0,element:u.jsx(m4,{})},{path:"/profile",element:u.jsx(aT,{})},{path:"/price",element:u.jsx(v4,{})}]}]),lT=()=>u.jsx(gw,{router:nT});function rT(n={}){const{nonce:r,onScriptLoadSuccess:i,onScriptLoadError:o}=n,[d,f]=x.useState(!1),m=x.useRef(i);m.current=i;const h=x.useRef(o);return h.current=o,x.useEffect(()=>{const v=document.createElement("script");return v.src="https://accounts.google.com/gsi/client",v.async=!0,v.defer=!0,v.nonce=r,v.onload=()=>{var g;f(!0),(g=m.current)===null||g===void 0||g.call(m)},v.onerror=()=>{var g;f(!1),(g=h.current)===null||g===void 0||g.call(h)},document.body.appendChild(v),()=>{document.body.removeChild(v)}},[r]),d}const iT=x.createContext(null);function sT({clientId:n,nonce:r,onScriptLoadSuccess:i,onScriptLoadError:o,children:d}){const f=rT({nonce:r,onScriptLoadSuccess:i,onScriptLoadError:o}),m=x.useMemo(()=>({clientId:n,scriptLoadedSuccessfully:f}),[n,f]);return wn.createElement(iT.Provider,{value:m},d)}w1.createRoot(document.getElementById("root")).render(u.jsx(X1,{children:u.jsx(sT,{clientId:"************-ivqlimhr8lb49h9fqq31uvjgltvc536t.apps.googleusercontent.com",children:u.jsx(lT,{})})}));
