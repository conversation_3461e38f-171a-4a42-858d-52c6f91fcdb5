import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Phone,
  User,
  ArrowLeft,
  Eye,
  EyeOff,
  Shield,
  CheckCircle,
  Clock,
  UserPlus,
} from "lucide-react";
import { NavLink } from "react-router";
import axiosPrivate from "@/config/api";
import { toast } from "sonner";

const SignUp = () => {
  const [step, setStep] = useState<"form" | "otp" | "register">("form");
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
  });
  const [registrationData, setRegistrationData] = useState({
    name: "",
    password: "",
    phoneNumber: "",
    avatarUrl: "",
    userName: "",
  });
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);
  const [timer, setTimer] = useState(0);
  const [canResend, setCanResend] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timer]);

  const startTimer = () => {
    setTimer(60); // 1 minute
    setCanResend(false);
  };

  const handleFormSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    setLoading(true);

    try {
      // Ensure phone number has 998 prefix (without +)
      let phoneWithPrefix = formData.phone.trim();
      if (phoneWithPrefix.startsWith("+998")) {
        // Remove + and keep 998 prefix
        phoneWithPrefix = phoneWithPrefix.substring(1);
      } else if (phoneWithPrefix.startsWith("998")) {
        // Already has 998 prefix
        phoneWithPrefix = phoneWithPrefix;
      } else {
        // No prefix at all
        phoneWithPrefix = `998${phoneWithPrefix}`;
      }

      // Use the correct endpoint for sending OTP during signup
      await axiosPrivate.post("/api/otp/send", {
        phone: phoneWithPrefix,
        name: formData.name,
      });
      toast.success("OTP kodu gönderildi");
      setStep("otp");
      startTimer(); // Start the 1-minute timer
    } catch (error: any) {
      console.error("OTP send error:", error);
      toast.error(error.response?.data?.message || "OTP gönderilemedi");
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Ensure phone number has 998 prefix (without +) and ensure code is string
      let phoneWithPrefix = formData.phone.trim();
      if (phoneWithPrefix.startsWith("+998")) {
        // Remove + and keep 998 prefix
        phoneWithPrefix = phoneWithPrefix.substring(1);
      } else if (phoneWithPrefix.startsWith("998")) {
        // Already has 998 prefix
        phoneWithPrefix = phoneWithPrefix;
      } else {
        // No prefix at all
        phoneWithPrefix = `998${phoneWithPrefix}`;
      }

      const response = await axiosPrivate.post("/api/otp/verify", {
        phoneNumber: phoneWithPrefix,
        code: otp.toString(),
        name: formData.name,
        isSignUp: true,
      });

      // Check if OTP verification was successful
      if (
        response.data.message === "Kod muvaffaqiyatli tasdiqlandi" ||
        response.data.message?.includes("tasdiqlandi") ||
        response.status === 200 ||
        response.status === 201
      ) {
        // OTP verified successfully, move to registration step
        setRegistrationData({
          ...registrationData,
          name: formData.name,
          phoneNumber: phoneWithPrefix,
        });
        toast.success("OTP doğrulandı");
        setStep("register");
      } else {
        toast.error("OTP doğrulanamadı");
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "OTP doğrulanamadı");
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Prepare registration data with proper structure
      const registrationPayload = {
        name: registrationData.name,
        password: registrationData.password,
        phoneNumber: registrationData.phoneNumber,
        userName: registrationData.userName,
        avatarUrl: registrationData.avatarUrl || "",
      };

      const response = await axiosPrivate.post(
        "/api/user/register",
        registrationPayload
      );

      if (response.data.accessToken) {
        localStorage.setItem("accessToken", response.data.accessToken);
        toast.success("Kayıt başarılı");
        window.location.href = "/";
      } else if (response.status === 200 || response.status === 201) {
        toast.success("Kayıt başarılı");
        window.location.href = "/login";
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      toast.error(error.response?.data?.message || "Kayıt başarısız");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-md">
        {/* Progress indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 ${
                step === "form"
                  ? "bg-red-600 border-red-600 text-white"
                  : "bg-white border-red-600 text-red-600"
              }`}
            >
              <Phone className="w-4 h-4" />
            </div>
            <div
              className={`h-1 w-12 transition-all duration-300 ${
                step === "otp" || step === "register"
                  ? "bg-red-600"
                  : "bg-gray-200"
              }`}
            />
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 ${
                step === "otp"
                  ? "bg-red-600 border-red-600 text-white"
                  : step === "register"
                  ? "bg-white border-red-600 text-red-600"
                  : "bg-white border-gray-200 text-gray-400"
              }`}
            >
              <Shield className="w-4 h-4" />
            </div>
            <div
              className={`h-1 w-12 transition-all duration-300 ${
                step === "register" ? "bg-red-600" : "bg-gray-200"
              }`}
            />
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 ${
                step === "register"
                  ? "bg-red-600 border-red-600 text-white"
                  : "bg-white border-gray-200 text-gray-400"
              }`}
            >
              <UserPlus className="w-4 h-4" />
            </div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-2 px-2">
            <span>Telefon</span>
            <span>Doğrulama</span>
            <span>Kayıt</span>
          </div>
        </div>

        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
              {step === "form" ? (
                <Phone className="w-8 h-8 text-white" />
              ) : step === "otp" ? (
                <Shield className="w-8 h-8 text-white" />
              ) : (
                <UserPlus className="w-8 h-8 text-white" />
              )}
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900">
              {step === "form"
                ? "Kayıt Ol"
                : step === "otp"
                ? "OTP Doğrulama"
                : "Kullanıcı Bilgileri"}
            </CardTitle>
            <CardDescription className="text-gray-600">
              {step === "form"
                ? "Hesap oluşturmak için bilgilerinizi girin"
                : step === "otp"
                ? "Size gönderilen 4 haneli kodu girin"
                : "Hesabınızı tamamlamak için bilgilerinizi girin"}
            </CardDescription>
            {step === "otp" && (
              <Badge
                variant="outline"
                className="mx-auto mt-2 border-red-200 text-red-700"
              >
                <Clock className="w-3 h-3 mr-1" />
                {timer > 0
                  ? `${Math.floor(timer / 60)}:${(timer % 60)
                      .toString()
                      .padStart(2, "0")}`
                  : "Süre doldu"}
              </Badge>
            )}
          </CardHeader>

          <CardContent className="space-y-6">
            {step === "form" ? (
              <form onSubmit={handleFormSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label
                    htmlFor="name"
                    className="text-sm font-medium text-gray-700"
                  >
                    Ad Soyad
                  </Label>
                  <div className="relative group">
                    <User className="absolute left-3 top-3 h-4 w-4 text-gray-400 group-focus-within:text-red-500 transition-colors" />
                    <Input
                      id="name"
                      type="text"
                      placeholder="Adınız Soyadınız"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData({ ...formData, name: e.target.value })
                      }
                      className="pl-10 h-12 border-gray-200 focus:border-red-500 focus:ring-red-500/20 transition-all duration-200"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label
                    htmlFor="phone"
                    className="text-sm font-medium text-gray-700"
                  >
                    Telefon Numarası
                  </Label>
                  <div className="relative group">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400 group-focus-within:text-red-500 transition-colors" />
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="+998901234567"
                      value={formData.phone}
                      onChange={(e) =>
                        setFormData({ ...formData, phone: e.target.value })
                      }
                      className="pl-10 h-12 border-gray-200 focus:border-red-500 focus:ring-red-500/20 transition-all duration-200"
                      required
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Uzbekistan telefon numaranızı girin
                  </p>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Gönderiliyor...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4" />
                      <span>OTP Gönder</span>
                    </div>
                  )}
                </Button>
              </form>
            ) : step === "otp" ? (
              <form onSubmit={handleVerifyOtp} className="space-y-6">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => setStep("form")}
                  className="mb-4 text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Geri Dön
                </Button>

                <div className="space-y-4">
                  <div className="text-center">
                    <Label className="text-lg font-medium text-gray-700">
                      OTP Kodu
                    </Label>
                    <p className="text-sm text-gray-500 mt-1">
                      {formData.phone.startsWith("+")
                        ? formData.phone
                        : formData.phone.startsWith("998")
                        ? `+${formData.phone}`
                        : `+998${formData.phone}`}{" "}
                      numarasına gönderilen 4 haneli kodu girin
                    </p>
                  </div>

                  <div className="flex justify-center py-4">
                    <InputOTP
                      maxLength={4}
                      pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                      value={otp}
                      onChange={(value) => setOtp(value)}
                      className="gap-3"
                    >
                      <InputOTPGroup className="gap-3">
                        <InputOTPSlot
                          index={0}
                          className="w-14 h-14 text-xl font-bold border-2 border-gray-200 focus:border-red-500 focus:ring-red-500/20 rounded-lg transition-all duration-200"
                        />
                        <InputOTPSlot
                          index={1}
                          className="w-14 h-14 text-xl font-bold border-2 border-gray-200 focus:border-red-500 focus:ring-red-500/20 rounded-lg transition-all duration-200"
                        />
                        <InputOTPSlot
                          index={2}
                          className="w-14 h-14 text-xl font-bold border-2 border-gray-200 focus:border-red-500 focus:ring-red-500/20 rounded-lg transition-all duration-200"
                        />
                        <InputOTPSlot
                          index={3}
                          className="w-14 h-14 text-xl font-bold border-2 border-gray-200 focus:border-red-500 focus:ring-red-500/20 rounded-lg transition-all duration-200"
                        />
                      </InputOTPGroup>
                    </InputOTP>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                  disabled={loading || otp.length !== 4}
                >
                  {loading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Doğrulanıyor...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4" />
                      <span>Doğrula</span>
                    </div>
                  )}
                </Button>

                <div className="text-center">
                  {timer > 0 ? (
                    <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                      <Clock className="w-4 h-4" />
                      <span>
                        Tekrar gönder: {Math.floor(timer / 60)}:
                        {(timer % 60).toString().padStart(2, "0")}
                      </span>
                    </div>
                  ) : (
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full h-12 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 transition-all duration-200"
                      onClick={() => handleFormSubmit()}
                      disabled={loading || !canResend}
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Tekrar Gönder
                    </Button>
                  )}
                </div>
              </form>
            ) : (
              <form onSubmit={handleRegister} className="space-y-6">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => setStep("otp")}
                  className="mb-4 text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Geri Dön
                </Button>

                {/* Name field */}
                <div className="space-y-2">
                  <Label
                    htmlFor="regName"
                    className="text-sm font-medium text-gray-700"
                  >
                    Ad Soyad
                  </Label>
                  <div className="relative group">
                    <User className="absolute left-3 top-3 h-4 w-4 text-gray-400 group-focus-within:text-red-500 transition-colors" />
                    <Input
                      id="regName"
                      type="text"
                      placeholder="Adınız Soyadınız"
                      value={registrationData.name}
                      onChange={(e) =>
                        setRegistrationData({
                          ...registrationData,
                          name: e.target.value,
                        })
                      }
                      className="pl-10 h-12 border-gray-200 focus:border-red-500 focus:ring-red-500/20 transition-all duration-200"
                      required
                    />
                  </div>
                </div>

                {/* Username field */}
                <div className="space-y-2">
                  <Label
                    htmlFor="userName"
                    className="text-sm font-medium text-gray-700"
                  >
                    Kullanıcı Adı
                  </Label>
                  <div className="relative group">
                    <User className="absolute left-3 top-3 h-4 w-4 text-gray-400 group-focus-within:text-red-500 transition-colors" />
                    <Input
                      id="userName"
                      type="text"
                      placeholder="kullaniciadi"
                      value={registrationData.userName}
                      onChange={(e) =>
                        setRegistrationData({
                          ...registrationData,
                          userName: e.target.value,
                        })
                      }
                      className="pl-10 h-12 border-gray-200 focus:border-red-500 focus:ring-red-500/20 transition-all duration-200"
                      required
                    />
                  </div>
                  <p className="text-xs text-gray-500">
                    Benzersiz bir kullanıcı adı seçin
                  </p>
                </div>

                {/* Phone Number field (readonly) */}
                <div className="space-y-2">
                  <Label
                    htmlFor="phoneNumber"
                    className="text-sm font-medium text-gray-700"
                  >
                    Telefon Numarası
                  </Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="phoneNumber"
                      type="tel"
                      placeholder="998901234567"
                      value={registrationData.phoneNumber}
                      className="pl-10 h-12 bg-gray-50 border-gray-200 text-gray-600 cursor-not-allowed"
                      readOnly
                    />
                    <Badge
                      variant="outline"
                      className="absolute right-3 top-3 text-xs border-green-200 text-green-700 bg-green-50"
                    >
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Doğrulandı
                    </Badge>
                  </div>
                </div>

                {/* Password field */}
                <div className="space-y-2">
                  <Label
                    htmlFor="password"
                    className="text-sm font-medium text-gray-700"
                  >
                    Şifre
                  </Label>
                  <div className="relative group">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Güçlü bir şifre oluşturun"
                      value={registrationData.password}
                      onChange={(e) =>
                        setRegistrationData({
                          ...registrationData,
                          password: e.target.value,
                        })
                      }
                      className="pr-10 h-12 border-gray-200 focus:border-red-500 focus:ring-red-500/20 transition-all duration-200"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-red-500 transition-colors"
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">
                    En az 6 karakter uzunluğunda olmalı
                  </p>
                </div>

                {/* Avatar URL field */}
                <div className="space-y-2">
                  <Label
                    htmlFor="avatarUrl"
                    className="text-sm font-medium text-gray-700"
                  >
                    Avatar URL (İsteğe bağlı)
                  </Label>
                  <Input
                    id="avatarUrl"
                    type="url"
                    placeholder="https://example.com/avatar.jpg"
                    value={registrationData.avatarUrl}
                    onChange={(e) =>
                      setRegistrationData({
                        ...registrationData,
                        avatarUrl: e.target.value,
                      })
                    }
                    className="h-12 border-gray-200 focus:border-red-500 focus:ring-red-500/20 transition-all duration-200"
                  />
                  <p className="text-xs text-gray-500">
                    Profil resminiz için bir URL ekleyin
                  </p>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                  disabled={loading}
                >
                  {loading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Kayıt Oluşturuluyor...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <UserPlus className="w-4 h-4" />
                      <span>Hesap Oluştur</span>
                    </div>
                  )}
                </Button>
              </form>
            )}
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            Zaten hesabınız var mı?{" "}
            <NavLink
              to="/login"
              className="text-red-600 hover:text-red-700 font-medium hover:underline transition-colors"
            >
              Giriş Yap
            </NavLink>
          </p>
        </div>

        {/* Trust indicators */}
        <div className="mt-6 flex items-center justify-center space-x-6 text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <Shield className="w-3 h-3" />
            <span>Güvenli</span>
          </div>
          <div className="flex items-center space-x-1">
            <CheckCircle className="w-3 h-3" />
            <span>Doğrulanmış</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3" />
            <span>Hızlı Kayıt</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
